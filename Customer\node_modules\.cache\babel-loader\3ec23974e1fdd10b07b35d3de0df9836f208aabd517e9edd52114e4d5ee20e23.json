{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\BookingCheckPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { Container, Row, Col, Card, Form, Button, InputGroup } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\nimport Banner from \"../../../images/banner.jpg\";\nimport Header from \"../Header\";\nimport Footer from \"../Footer\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport ConfirmationModal from \"@components/ConfirmationModal\";\nimport PromotionModal from \"./components/PromotionModal\";\nimport PromotionValidationHandler from \"../../../components/PromotionValidationHandler\";\nimport usePromotionValidation from \"../../../hooks/usePromotionValidation\";\nimport { showToast } from \"../../../components/ToastContainer\";\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\nimport Utils from \"../../../utils/Utils\";\nimport Factories from \"../../../redux/search/factories\";\nimport { ChatBox } from \"./HomePage\";\nimport SearchActions from \"../../../redux/search/actions\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport HotelClosedModal from \"./components/HotelClosedModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookingCheckPage = () => {\n  _s();\n  var _hotelDetail$hotelNam, _hotelDetail$address;\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const SearchInformation = useAppSelector(state => state.Search.SearchInformation);\n  const selectedRoomsTemps = useAppSelector(state => state.Search.selectedRooms);\n  const selectedServicesFromRedux = useAppSelector(state => state.Search.selectedServices);\n  const hotelDetailFromRedux = useAppSelector(state => state.Search.hotelDetail);\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\n\n  // Promotion code state\n  const [promotionCode, setPromotionCode] = useState(\"\");\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\n  const [promotionId, setPromotionId] = useState(null);\n\n  // Real-time promotion validation\n  const {\n    validationError,\n    isValidating,\n    showValidationModal,\n    currentPromotion,\n    validatePromotion,\n    setActivePromotion,\n    removePromotion,\n    clearValidationError,\n    setShowValidationModal,\n    setValidationError\n  } = usePromotionValidation();\n\n  // Add state for booking data\n  const [bookingData, setBookingData] = useState({\n    selectedRooms: selectedRoomsTemps || [],\n    selectedServices: selectedServicesFromRedux || [],\n    hotelDetail: hotelDetailFromRedux || null,\n    searchInfo: SearchInformation\n  });\n  const [dataRestored, setDataRestored] = useState(false);\n\n  // Restore data from sessionStorage stack when component mounts\n  useEffect(() => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      const currentBooking = bookingStack[bookingStack.length - 1];\n      setBookingData(currentBooking);\n\n      // Update Redux store with current data\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: currentBooking.selectedRooms,\n          selectedServices: currentBooking.selectedServices,\n          hotelDetail: currentBooking.hotelDetail\n        }\n      });\n    }\n    setDataRestored(true);\n  }, [dispatch]);\n\n  // Load promotion info from sessionStorage AFTER booking data is restored\n  useEffect(() => {\n    if (dataRestored) {\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\n      if (promo) {\n        setPromotionCode(promo.promotionCode || \"\");\n        setPromotionDiscount(promo.promotionDiscount || 0);\n        setPromotionMessage(promo.promotionMessage || \"\");\n        setPromotionId(promo.promotionId || null);\n\n        // Set active promotion for real-time validation when restored from sessionStorage\n        if (promo.promotionId) {\n          setActivePromotion({\n            id: promo.promotionId,\n            code: promo.promotionCode,\n            discount: promo.promotionDiscount\n          });\n        }\n      }\n    }\n  }, [dataRestored, setActivePromotion]);\n\n  // Save promotion info to sessionStorage when any promotion state changes\n  useEffect(() => {\n    if (dataRestored) {\n      // Chỉ save khi đã restore xong data\n      sessionStorage.setItem(\"promotionInfo\", JSON.stringify({\n        promotionCode,\n        promotionDiscount,\n        promotionMessage,\n        promotionId\n      }));\n    }\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored]);\n\n  // Use bookingData instead of Redux state\n  const selectedRooms = bookingData.selectedRooms;\n  const selectedServices = bookingData.selectedServices;\n  const hotelDetail = bookingData.hotelDetail;\n  const searchInfo = bookingData.searchInfo;\n\n  // Calculate number of days between check-in and check-out\n  const calculateNumberOfDays = () => {\n    const checkIn = new Date(searchInfo.checkinDate);\n    const checkOut = new Date(searchInfo.checkoutDate);\n    const diffTime = Math.abs(checkOut - checkIn);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n  const numberOfDays = calculateNumberOfDays();\n\n  // Calculate prices\n  const totalRoomPrice = selectedRooms.reduce((total, {\n    room,\n    amount\n  }) => total + room.price * amount * numberOfDays, 0);\n  const totalServicePrice = selectedServices.reduce((total, service) => {\n    const selectedDates = service.selectedDates || [];\n    const serviceQuantity = service.quantity * selectedDates.length;\n    return total + service.price * serviceQuantity;\n  }, 0);\n  const totalPrice = totalRoomPrice + totalServicePrice;\n  const finalPrice = Math.max(totalPrice - promotionDiscount, 0);\n\n  // Debug: Log price calculations\n  console.log(\"Price calculation:\", {\n    totalRoomPrice,\n    totalServicePrice,\n    totalPrice,\n    promotionDiscount,\n    finalPrice,\n    promotionCode\n  });\n\n  // Validate promotion when data is restored or booking changes\n  useEffect(() => {\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\n\n    // Add a small delay to ensure promotion is fully restored before validation\n    const timeoutId = setTimeout(() => {\n      const validatePromotion = async () => {\n        try {\n          const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\n            code: promotionCode,\n            orderAmount: totalPrice\n          });\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\n            // Promotion is no longer valid or discount changed\n            setPromotionCode(\"\");\n            setPromotionDiscount(0);\n            setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\n            setPromotionId(null);\n            sessionStorage.removeItem(\"promotionInfo\");\n          }\n        } catch (err) {\n          // Promotion validation failed\n          setPromotionCode(\"\");\n          setPromotionDiscount(0);\n          setPromotionMessage(\"Promotion is no longer valid\");\n          setPromotionId(null);\n          sessionStorage.removeItem(\"promotionInfo\");\n        }\n      };\n      validatePromotion();\n    }, 100);\n    return () => clearTimeout(timeoutId);\n  }, [dataRestored, totalPrice, promotionCode, promotionId, promotionDiscount]); // Validate when total price changes or data is restored\n\n  // Handle navigation back to HomeDetailPage\n  const handleBackToHomeDetail = () => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      // Remove the current booking from stack\n      bookingStack.pop();\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n    }\n    navigate(-1);\n  };\n\n  // Star rating component\n  const StarRating = ({\n    rating\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"star-rating\",\n      children: [...Array(5)].map((_, index) => index < rating ? /*#__PURE__*/_jsxDEV(FaStar, {\n        className: \"star filled\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(FaRegStar, {\n        className: \"star\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this);\n  };\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\n\n  // Hàm xử lý áp dụng promotion từ modal\n  const handleApplyPromotionFromModal = promotionData => {\n    console.log(\"handleApplyPromotionFromModal called with:\", promotionData);\n    setPromotionCode(promotionData.code);\n    setPromotionDiscount(promotionData.discount);\n    setPromotionMessage(promotionData.message);\n    setPromotionId(promotionData.promotionId);\n\n    // Set active promotion for real-time validation\n    if (promotionData.promotionId) {\n      setActivePromotion({\n        id: promotionData.promotionId,\n        code: promotionData.code,\n        discount: promotionData.discount\n      });\n      console.log(\"Active promotion set:\", {\n        id: promotionData.promotionId,\n        code: promotionData.code,\n        discount: promotionData.discount\n      });\n    } else {\n      // Remove promotion\n      console.log(\"Removing promotion\");\n      removePromotion();\n    }\n  };\n\n  // Handle promotion validation error actions\n  const handleRemovePromotion = () => {\n    handleApplyPromotionFromModal({\n      code: \"\",\n      discount: 0,\n      message: \"\",\n      promotionId: null\n    });\n  };\n  const handleApplyAlternativePromotion = async alternative => {\n    // Apply the alternative promotion\n    handleApplyPromotionFromModal({\n      code: alternative.code,\n      discount: alternative.discount,\n      message: `Applied alternative promotion: ${alternative.name}`,\n      promotionId: alternative.id\n    });\n  };\n  const handleRetryValidation = () => {\n    if (promotionId && totalPrice) {\n      validatePromotion(promotionId, totalPrice);\n    }\n  };\n  const createBooking = async () => {\n    dispatch({\n      type: HotelActions.FETCH_DETAIL_HOTEL,\n      payload: {\n        hotelId: hotelDetail._id,\n        userId: Auth._id,\n        onSuccess: async hotel => {\n          console.log(\"Hotel detail fetched successfully:\", hotel);\n          if (hotel.ownerStatus === \"ACTIVE\") {\n            const totalRoomPrice = selectedRooms.reduce((total, {\n              room,\n              amount\n            }) => total + room.price * amount * numberOfDays, 0);\n            const totalServicePrice = selectedServices.reduce((total, service) => {\n              const selectedDates = service.selectedDates || [];\n              const serviceQuantity = service.quantity * selectedDates.length;\n              return total + service.price * serviceQuantity;\n            }, 0);\n            const totalPrice = totalRoomPrice + totalServicePrice;\n            const params = {\n              hotelId: hotelDetail._id,\n              checkOutDate: searchInfo.checkoutDate,\n              checkInDate: searchInfo.checkinDate,\n              totalPrice: totalPrice,\n              // giá gốc\n              finalPrice: finalPrice,\n              // giá sau giảm giá\n              roomDetails: selectedRooms.map(({\n                room,\n                amount\n              }) => ({\n                room: {\n                  _id: room._id\n                },\n                amount: amount\n              })),\n              serviceDetails: selectedServices.map(service => {\n                var _service$selectedDate;\n                return {\n                  _id: service._id,\n                  quantity: service.quantity * (((_service$selectedDate = service.selectedDates) === null || _service$selectedDate === void 0 ? void 0 : _service$selectedDate.length) || 0),\n                  selectDate: service.selectedDates || []\n                };\n              }),\n              // Thêm promotionId và promotionDiscount nếu có\n              ...(promotionId && {\n                promotionId\n              }),\n              ...(promotionDiscount > 0 && {\n                promotionDiscount\n              })\n            };\n            console.log(\"params >> \", params);\n\n            // Helper function to save reservationId to bookingStack\n            const saveReservationIdToBookingStack = reservationId => {\n              if (reservationId) {\n                const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n                if (bookingStack.length > 0) {\n                  bookingStack[bookingStack.length - 1].reservationId = reservationId;\n                  sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n                }\n              }\n            };\n            try {\n              let reservationId = null;\n              const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n              if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\n                reservationId = bookingStack[bookingStack.length - 1].reservationId;\n              }\n              const response = await Factories.create_booking({\n                ...params,\n                reservationId\n              });\n              console.log(\"response >> \", response);\n              if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                var _response$data, _response$data$unpaid, _responseCheckout$dat;\n                reservationId = response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : (_response$data$unpaid = _response$data.unpaidReservation) === null || _response$data$unpaid === void 0 ? void 0 : _response$data$unpaid._id;\n                saveReservationIdToBookingStack(reservationId);\n                const unpaidReservationId = reservationId;\n                const responseCheckout = await Factories.checkout_booking(unpaidReservationId);\n                console.log(\"responseCheckout >> \", responseCheckout);\n                const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat = responseCheckout.data) === null || _responseCheckout$dat === void 0 ? void 0 : _responseCheckout$dat.sessionUrl;\n                if (paymentUrl) {\n                  window.location.href = paymentUrl;\n                }\n              } else if ((response === null || response === void 0 ? void 0 : response.status) === 201) {\n                var _response$data2, _response$data2$reser, _responseCheckout$dat2;\n                reservationId = response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$reser = _response$data2.reservation) === null || _response$data2$reser === void 0 ? void 0 : _response$data2$reser._id;\n                saveReservationIdToBookingStack(reservationId);\n                const responseCheckout = await Factories.checkout_booking(reservationId);\n                const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat2 = responseCheckout.data) === null || _responseCheckout$dat2 === void 0 ? void 0 : _responseCheckout$dat2.sessionUrl;\n                if (paymentUrl) {\n                  window.location.href = paymentUrl;\n                }\n              } else {\n                console.log(\"error create booking\");\n              }\n            } catch (error) {\n              console.error(\"Error create payment: \", error);\n\n              // Check if this is a promotion validation error\n              if (error.response && error.response.status === 400 && error.response.data.promotionValidation) {\n                const errorData = error.response.data;\n                console.log(\"Promotion validation failed during booking:\", errorData);\n\n                // Set validation error to show the modal\n                setValidationError({\n                  valid: false,\n                  errorCode: errorData.errorCode,\n                  message: errorData.message,\n                  severity: errorData.severity || 'ERROR',\n                  recoverable: errorData.recoverable || false,\n                  suggestedAction: errorData.suggestedAction || 'REMOVE_PROMOTION',\n                  alternatives: errorData.alternatives || []\n                });\n                setShowValidationModal(true);\n\n                // Show toast notification\n                showToast.error(errorData.message || 'Promotion is no longer valid');\n                return; // Don't navigate to error page\n              }\n\n              // For other errors, navigate to error page\n              navigate(Routers.ErrorPage);\n            }\n          } else {\n            setShowModalStatusBooking(true);\n          }\n        }\n      }\n    });\n  };\n  const handleAccept = () => {\n    const totalRoomPrice = selectedRooms.reduce((total, {\n      room,\n      amount\n    }) => total + room.price * amount * numberOfDays, 0);\n    if (totalRoomPrice > 0) {\n      createBooking();\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: [],\n          selectedServices: [],\n          hotelDetail: hotelDetail\n        }\n      });\n    }\n  };\n  const handleConfirmBooking = () => {\n    setShowAcceptModal(true);\n  };\n  const formatCurrency = amount => {\n    if (amount === undefined || amount === null) return \"$0\";\n    return new Intl.NumberFormat(\"en-US\", {\n      style: \"currency\",\n      currency: \"USD\",\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(amount);\n  };\n\n  // Add null check for hotelDetail\n  if (!hotelDetail) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: \"100vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\",\n      style: {\n        paddingTop: \"65px\",\n        paddingBottom: \"50px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Container, {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"booking-card text-white\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                marginBottom: \"20px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stars mb-2\",\n                style: {\n                  justifyContent: \"flex-start\",\n                  justifyItems: \"self-start\"\n                },\n                children: /*#__PURE__*/_jsxDEV(StarRating, {\n                  rating: hotelDetail.star\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"hotel-name mb-1\",\n                children: (_hotelDetail$hotelNam = hotelDetail.hotelName) !== null && _hotelDetail$hotelNam !== void 0 ? _hotelDetail$hotelNam : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hotel-address small mb-4\",\n                children: (_hotelDetail$address = hotelDetail.address) !== null && _hotelDetail$address !== void 0 ? _hotelDetail$address : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-4\",\n                children: \"Your booking detail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkin\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 540,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkinDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkout\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkout\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkoutDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 559,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stay-info mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total length of stay:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [numberOfDays, \" night\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 21\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total number of people:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [searchInfo.adults, \" Adults - \", searchInfo.childrens, \" \", \"Childrens\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-room mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-4\",\n                  children: \"You selected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this), selectedRooms.map(({\n                  room,\n                  amount\n                }) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [amount, \" x \", room.name, \" (\", numberOfDays, \" days):\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(room.price * amount * numberOfDays)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 23\n                  }, this)]\n                }, room._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: handleBackToHomeDetail,\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 17\n              }, this), selectedServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-services mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-3\",\n                  children: \"Selected Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 21\n                }, this), selectedServices.map(service => {\n                  const selectedDates = service.selectedDates || [];\n                  const serviceQuantity = service.quantity * selectedDates.length;\n                  const serviceTotal = service.price * serviceQuantity;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [service.quantity, \" x \", service.name, \" (\", selectedDates.length, \" days):\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 635,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"fw-bold\",\n                      children: Utils.formatCurrency(serviceTotal)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 639,\n                      columnNumber: 27\n                    }, this)]\n                  }, service._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: () => {\n                      dispatch({\n                        type: SearchActions.SAVE_SELECTED_ROOMS,\n                        payload: {\n                          selectedRooms: selectedRooms,\n                          selectedServices: selectedServices,\n                          hotelDetail: hotelDetail\n                        }\n                      });\n                      navigate(-1);\n                    },\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-section mb-3\",\n                children: [promotionDiscount > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-applied mb-3\",\n                  style: {\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n                    borderColor: \"#28a745\",\n                    border: \"2px solid #28a745\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"text-success me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 692,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"fw-bold text-success\",\n                            children: promotionCode\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 693,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 691,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-success\",\n                          children: [\"Save \", Utils.formatCurrency(promotionDiscount)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 695,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 690,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleApplyPromotionFromModal({\n                          code: \"\",\n                          discount: 0,\n                          message: \"\",\n                          promotionId: null\n                        }),\n                        className: \"d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 710,\n                          columnNumber: 29\n                        }, this), \"Remove\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 699,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-3 mb-3\",\n                  style: {\n                    border: \"2px dashed rgba(255,255,255,0.3)\",\n                    borderRadius: \"8px\",\n                    backgroundColor: \"rgba(255,255,255,0.05)\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"text-muted mb-2\",\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-muted small\",\n                    children: \"No promotion applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"w-100 d-flex align-items-center justify-content-center\",\n                  onClick: () => setShowPromotionModal(true),\n                  style: {\n                    borderStyle: \"dashed\",\n                    borderWidth: \"2px\",\n                    padding: \"12px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 21\n                  }, this), promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this), promotionMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `mt-2 small text-center ${promotionDiscount > 0 ? \"text-success\" : \"text-danger\"}`,\n                  children: promotionMessage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-danger mb-0\",\n                    children: [\"Total: \", Utils.formatCurrency(finalPrice)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 756,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: \"Includes taxes and fees\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                color: \"white\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-4\",\n                children: \"Check your information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Full name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: Auth.name,\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 779,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 793,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"email\",\n                    value: Auth.email,\n                    placeholder: \"<EMAIL>\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"tel\",\n                    value: Auth.phoneNumber,\n                    placeholder: \"0912345678\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Who are you booking for?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 821,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"mainGuest\",\n                      label: \"I'm the main guest\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"mainGuest\",\n                      onChange: () => setBookingFor(\"mainGuest\"),\n                      className: \"mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 823,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"someoneElse\",\n                      label: \"I'm booking for someone else\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"someoneElse\",\n                      onChange: () => setBookingFor(\"someoneElse\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 832,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 822,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 820,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    className: \"px-4 py-2\",\n                    style: {\n                      borderRadius: \"10px\",\n                      backgroundColor: \"white\",\n                      color: \"#007bff\",\n                      border: \"none\",\n                      fontWeight: \"bold\"\n                    },\n                    onClick: handleConfirmBooking,\n                    children: \"Booking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                    show: showAcceptModal,\n                    onHide: () => setShowAcceptModal(false),\n                    onConfirm: handleAccept,\n                    title: \"Confirm Acceptance\",\n                    message: \"Do you want to proceed with this booking confirmation?\",\n                    confirmButtonText: \"Accept\",\n                    type: \"accept\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(ChatBox, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 874,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 873,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 877,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionModal, {\n      show: showPromotionModal,\n      onHide: () => setShowPromotionModal(false),\n      totalPrice: totalPrice,\n      onApplyPromotion: handleApplyPromotionFromModal,\n      currentPromotionId: promotionId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 880,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HotelClosedModal, {\n      show: showModalStatusBooking,\n      onClose: () => {\n        setShowModalStatusBooking(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 888,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionValidationHandler, {\n      validationError: validationError,\n      show: showValidationModal,\n      onClose: clearValidationError,\n      onRemovePromotion: handleRemovePromotion,\n      onApplyAlternative: handleApplyAlternativePromotion,\n      onRetry: handleRetryValidation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 896,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 482,\n    columnNumber: 5\n  }, this);\n};\n_s(BookingCheckPage, \"+bHGVVYRyroXTyAk5Xi6Ul7oP3s=\", false, function () {\n  return [useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useNavigate, useAppDispatch, usePromotionValidation];\n});\n_c = BookingCheckPage;\nexport default BookingCheckPage;\nvar _c;\n$RefreshReg$(_c, \"BookingCheckPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "InputGroup", "FaStar", "FaRegStar", "FaTag", "FaTimes", "Banner", "Header", "Footer", "Routers", "useNavigate", "ConfirmationModal", "PromotionModal", "PromotionValidationHandler", "usePromotionValidation", "showToast", "useAppSelector", "useAppDispatch", "Utils", "Factories", "ChatBox", "SearchActions", "HotelActions", "HotelClosedModal", "jsxDEV", "_jsxDEV", "BookingCheckPage", "_s", "_hotelDetail$hotelNam", "_hotelDetail$address", "showModalStatusBooking", "setShowModalStatusBooking", "<PERSON><PERSON>", "state", "SearchInformation", "Search", "selectedRoomsTemps", "selectedRooms", "selectedServicesFromRedux", "selectedServices", "hotelDetailFromRedux", "hotelDetail", "navigate", "dispatch", "bookingFor", "setBookingFor", "promotionCode", "setPromotionCode", "promotionDiscount", "setPromotionDiscount", "promotionMessage", "setPromotionMessage", "promotionId", "setPromotionId", "validationError", "isValidating", "showValidationModal", "currentPromotion", "validatePromotion", "setActivePromotion", "removePromotion", "clearValidationError", "setShowValidationModal", "setValidationError", "bookingData", "setBookingData", "searchInfo", "dataRestored", "setDataRestored", "bookingStack", "JSON", "parse", "sessionStorage", "getItem", "length", "currentBooking", "type", "SAVE_SELECTED_ROOMS", "payload", "promo", "id", "code", "discount", "setItem", "stringify", "calculateNumberOfDays", "checkIn", "Date", "checkinDate", "checkOut", "checkoutDate", "diffTime", "Math", "abs", "diffDays", "ceil", "numberOfDays", "totalRoomPrice", "reduce", "total", "room", "amount", "price", "totalServicePrice", "service", "selectedDates", "serviceQuantity", "quantity", "totalPrice", "finalPrice", "max", "console", "log", "timeoutId", "setTimeout", "res", "post", "orderAmount", "data", "valid", "removeItem", "err", "clearTimeout", "handleBackToHomeDetail", "pop", "StarRating", "rating", "className", "children", "Array", "map", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showAcceptModal", "setShowAcceptModal", "showPromotionModal", "setShowPromotionModal", "handleApplyPromotionFromModal", "promotionData", "message", "handleRemovePromotion", "handleApplyAlternativePromotion", "alternative", "name", "handleRetryValidation", "createBooking", "FETCH_DETAIL_HOTEL", "hotelId", "_id", "userId", "onSuccess", "hotel", "ownerStatus", "params", "checkOutDate", "checkInDate", "roomDetails", "serviceDetails", "_service$selectedDate", "selectDate", "saveReservationIdToBookingStack", "reservationId", "response", "create_booking", "status", "_response$data", "_response$data$unpaid", "_responseCheckout$dat", "unpaidReservation", "unpaidReservationId", "responseCheckout", "checkout_booking", "paymentUrl", "sessionUrl", "window", "location", "href", "_response$data2", "_response$data2$reser", "_responseCheckout$dat2", "reservation", "error", "promotionValidation", "errorData", "errorCode", "severity", "recoverable", "suggestedAction", "alternatives", "ErrorPage", "handleAccept", "handleConfirmBooking", "formatCurrency", "undefined", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "height", "role", "backgroundImage", "backgroundSize", "backgroundPosition", "paddingTop", "paddingBottom", "md", "lg", "backgroundColor", "borderRadius", "padding", "marginBottom", "justifyContent", "justifyItems", "star", "hotelName", "address", "margin", "xs", "fontSize", "getDate", "adults", "childrens", "cursor", "onClick", "serviceTotal", "borderColor", "border", "Body", "variant", "size", "borderStyle", "borderWidth", "color", "Group", "Label", "Control", "value", "email", "placeholder", "phoneNumber", "Check", "label", "checked", "onChange", "fontWeight", "show", "onHide", "onConfirm", "title", "confirmButtonText", "onApplyPromotion", "currentPromotionId", "onClose", "onRemovePromotion", "onApplyAlternative", "onRetry", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Customer/src/pages/customer/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Card,\r\n  Form,\r\n  Button,\r\n  InputGroup,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport Header from \"../Header\";\r\nimport Footer from \"../Footer\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport ConfirmationModal from \"@components/ConfirmationModal\";\r\nimport PromotionModal from \"./components/PromotionModal\";\r\nimport PromotionValidationHandler from \"../../../components/PromotionValidationHandler\";\r\nimport usePromotionValidation from \"../../../hooks/usePromotionValidation\";\r\nimport { showToast } from \"../../../components/ToastContainer\";\r\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\r\nimport Utils from \"../../../utils/Utils\";\r\nimport Factories from \"../../../redux/search/factories\";\r\nimport { ChatBox } from \"./HomePage\";\r\nimport SearchActions from \"../../../redux/search/actions\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\nimport HotelClosedModal from \"./components/HotelClosedModal\";\r\n\r\nconst BookingCheckPage = () => {\r\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\r\n\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const SearchInformation = useAppSelector(\r\n    (state) => state.Search.SearchInformation\r\n  );\r\n  const selectedRoomsTemps = useAppSelector(\r\n    (state) => state.Search.selectedRooms\r\n  );\r\n  const selectedServicesFromRedux = useAppSelector(\r\n    (state) => state.Search.selectedServices\r\n  );\r\n  const hotelDetailFromRedux = useAppSelector(\r\n    (state) => state.Search.hotelDetail\r\n  );\r\n  const navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\r\n\r\n  // Promotion code state\r\n  const [promotionCode, setPromotionCode] = useState(\"\");\r\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\r\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\r\n  const [promotionId, setPromotionId] = useState(null);\r\n\r\n  // Real-time promotion validation\r\n  const {\r\n    validationError,\r\n    isValidating,\r\n    showValidationModal,\r\n    currentPromotion,\r\n    validatePromotion,\r\n    setActivePromotion,\r\n    removePromotion,\r\n    clearValidationError,\r\n    setShowValidationModal,\r\n    setValidationError\r\n  } = usePromotionValidation();\r\n\r\n  // Add state for booking data\r\n  const [bookingData, setBookingData] = useState({\r\n    selectedRooms: selectedRoomsTemps || [],\r\n    selectedServices: selectedServicesFromRedux || [],\r\n    hotelDetail: hotelDetailFromRedux || null,\r\n    searchInfo: SearchInformation,\r\n  });\r\n\r\n  const [dataRestored, setDataRestored] = useState(false);\r\n\r\n  // Restore data from sessionStorage stack when component mounts\r\n  useEffect(() => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      const currentBooking = bookingStack[bookingStack.length - 1];\r\n      setBookingData(currentBooking);\r\n\r\n      // Update Redux store with current data\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: currentBooking.selectedRooms,\r\n          selectedServices: currentBooking.selectedServices,\r\n          hotelDetail: currentBooking.hotelDetail,\r\n        },\r\n      });\r\n    }\r\n    setDataRestored(true);\r\n  }, [dispatch]);\r\n\r\n  // Load promotion info from sessionStorage AFTER booking data is restored\r\n  useEffect(() => {\r\n    if (dataRestored) {\r\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\r\n      if (promo) {\r\n        setPromotionCode(promo.promotionCode || \"\");\r\n        setPromotionDiscount(promo.promotionDiscount || 0);\r\n        setPromotionMessage(promo.promotionMessage || \"\");\r\n        setPromotionId(promo.promotionId || null);\r\n\r\n        // Set active promotion for real-time validation when restored from sessionStorage\r\n        if (promo.promotionId) {\r\n          setActivePromotion({\r\n            id: promo.promotionId,\r\n            code: promo.promotionCode,\r\n            discount: promo.promotionDiscount\r\n          });\r\n        }\r\n      }\r\n    }\r\n  }, [dataRestored, setActivePromotion]);\r\n\r\n  // Save promotion info to sessionStorage when any promotion state changes\r\n  useEffect(() => {\r\n    if (dataRestored) { // Chỉ save khi đã restore xong data\r\n      sessionStorage.setItem(\r\n        \"promotionInfo\",\r\n        JSON.stringify({\r\n          promotionCode,\r\n          promotionDiscount,\r\n          promotionMessage,\r\n          promotionId,\r\n        })\r\n      );\r\n    }\r\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored]);\r\n\r\n  // Use bookingData instead of Redux state\r\n  const selectedRooms = bookingData.selectedRooms;\r\n  const selectedServices = bookingData.selectedServices;\r\n  const hotelDetail = bookingData.hotelDetail;\r\n  const searchInfo = bookingData.searchInfo;\r\n\r\n  // Calculate number of days between check-in and check-out\r\n  const calculateNumberOfDays = () => {\r\n    const checkIn = new Date(searchInfo.checkinDate);\r\n    const checkOut = new Date(searchInfo.checkoutDate);\r\n    const diffTime = Math.abs(checkOut - checkIn);\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    return diffDays;\r\n  };\r\n\r\n  const numberOfDays = calculateNumberOfDays();\r\n\r\n  // Calculate prices\r\n  const totalRoomPrice = selectedRooms.reduce(\r\n    (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n    0\r\n  );\r\n  const totalServicePrice = selectedServices.reduce((total, service) => {\r\n    const selectedDates = service.selectedDates || [];\r\n    const serviceQuantity = service.quantity * selectedDates.length;\r\n    return total + service.price * serviceQuantity;\r\n  }, 0);\r\n  const totalPrice = totalRoomPrice + totalServicePrice;\r\n  const finalPrice = Math.max(totalPrice - promotionDiscount, 0);\r\n\r\n  // Debug: Log price calculations\r\n  console.log(\"Price calculation:\", {\r\n    totalRoomPrice,\r\n    totalServicePrice,\r\n    totalPrice,\r\n    promotionDiscount,\r\n    finalPrice,\r\n    promotionCode\r\n  });\r\n\r\n  // Validate promotion when data is restored or booking changes\r\n  useEffect(() => {\r\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\r\n\r\n    // Add a small delay to ensure promotion is fully restored before validation\r\n    const timeoutId = setTimeout(() => {\r\n      const validatePromotion = async () => {\r\n        try {\r\n          const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\r\n            code: promotionCode,\r\n            orderAmount: totalPrice,\r\n          });\r\n          \r\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\r\n            // Promotion is no longer valid or discount changed\r\n            setPromotionCode(\"\");\r\n            setPromotionDiscount(0);\r\n            setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\r\n            setPromotionId(null);\r\n            sessionStorage.removeItem(\"promotionInfo\");\r\n          }\r\n        } catch (err) {\r\n          // Promotion validation failed\r\n          setPromotionCode(\"\");\r\n          setPromotionDiscount(0);\r\n          setPromotionMessage(\"Promotion is no longer valid\");\r\n          setPromotionId(null);\r\n          sessionStorage.removeItem(\"promotionInfo\");\r\n        }\r\n      };\r\n\r\n      validatePromotion();\r\n    }, 100);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [dataRestored, totalPrice, promotionCode, promotionId, promotionDiscount]); // Validate when total price changes or data is restored\r\n\r\n  // Handle navigation back to HomeDetailPage\r\n  const handleBackToHomeDetail = () => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      // Remove the current booking from stack\r\n      bookingStack.pop();\r\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n    }\r\n    navigate(-1);\r\n  };\r\n\r\n  // Star rating component\r\n  const StarRating = ({ rating }) => {\r\n    return (\r\n      <div className=\"star-rating\">\r\n        {[...Array(5)].map((_, index) =>\r\n          index < rating ? (\r\n            <FaStar key={index} className=\"star filled\" />\r\n          ) : (\r\n            <FaRegStar key={index} className=\"star\" />\r\n          )\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\r\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\r\n\r\n  // Hàm xử lý áp dụng promotion từ modal\r\n  const handleApplyPromotionFromModal = (promotionData) => {\r\n    console.log(\"handleApplyPromotionFromModal called with:\", promotionData);\r\n\r\n    setPromotionCode(promotionData.code);\r\n    setPromotionDiscount(promotionData.discount);\r\n    setPromotionMessage(promotionData.message);\r\n    setPromotionId(promotionData.promotionId);\r\n\r\n    // Set active promotion for real-time validation\r\n    if (promotionData.promotionId) {\r\n      setActivePromotion({\r\n        id: promotionData.promotionId,\r\n        code: promotionData.code,\r\n        discount: promotionData.discount\r\n      });\r\n      console.log(\"Active promotion set:\", {\r\n        id: promotionData.promotionId,\r\n        code: promotionData.code,\r\n        discount: promotionData.discount\r\n      });\r\n    } else {\r\n      // Remove promotion\r\n      console.log(\"Removing promotion\");\r\n      removePromotion();\r\n    }\r\n  };\r\n\r\n  // Handle promotion validation error actions\r\n  const handleRemovePromotion = () => {\r\n    handleApplyPromotionFromModal({\r\n      code: \"\",\r\n      discount: 0,\r\n      message: \"\",\r\n      promotionId: null\r\n    });\r\n  };\r\n\r\n  const handleApplyAlternativePromotion = async (alternative) => {\r\n    // Apply the alternative promotion\r\n    handleApplyPromotionFromModal({\r\n      code: alternative.code,\r\n      discount: alternative.discount,\r\n      message: `Applied alternative promotion: ${alternative.name}`,\r\n      promotionId: alternative.id\r\n    });\r\n  };\r\n\r\n  const handleRetryValidation = () => {\r\n    if (promotionId && totalPrice) {\r\n      validatePromotion(promotionId, totalPrice);\r\n    }\r\n  };\r\n\r\n  const createBooking = async () => {\r\n    dispatch({\r\n      type: HotelActions.FETCH_DETAIL_HOTEL,\r\n      payload: {\r\n        hotelId: hotelDetail._id,\r\n        userId: Auth._id,\r\n        onSuccess: async (hotel) => {\r\n          console.log(\"Hotel detail fetched successfully:\", hotel);\r\n          if (hotel.ownerStatus === \"ACTIVE\") {\r\n            const totalRoomPrice = selectedRooms.reduce(\r\n              (total, { room, amount }) =>\r\n                total + room.price * amount * numberOfDays,\r\n              0\r\n            );\r\n\r\n            const totalServicePrice = selectedServices.reduce(\r\n              (total, service) => {\r\n                const selectedDates = service.selectedDates || [];\r\n                const serviceQuantity = service.quantity * selectedDates.length;\r\n                return total + service.price * serviceQuantity;\r\n              },\r\n              0\r\n            );\r\n\r\n            const totalPrice = totalRoomPrice + totalServicePrice;\r\n\r\n            const params = {\r\n              hotelId: hotelDetail._id,\r\n              checkOutDate: searchInfo.checkoutDate,\r\n              checkInDate: searchInfo.checkinDate,\r\n              totalPrice: totalPrice, // giá gốc\r\n              finalPrice: finalPrice, // giá sau giảm giá\r\n              roomDetails: selectedRooms.map(({ room, amount }) => ({\r\n                room: {\r\n                  _id: room._id,\r\n                },\r\n                amount: amount,\r\n              })),\r\n              serviceDetails: selectedServices.map((service) => ({\r\n                _id: service._id,\r\n                quantity:\r\n                  service.quantity * (service.selectedDates?.length || 0),\r\n                selectDate: service.selectedDates || [],\r\n              })),\r\n              // Thêm promotionId và promotionDiscount nếu có\r\n              ...(promotionId && { promotionId }),\r\n              ...(promotionDiscount > 0 && { promotionDiscount }),\r\n            };\r\n\r\n            console.log(\"params >> \", params);\r\n\r\n            // Helper function to save reservationId to bookingStack\r\n            const saveReservationIdToBookingStack = (reservationId) => {\r\n              if (reservationId) {\r\n                const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n                if (bookingStack.length > 0) {\r\n                  bookingStack[bookingStack.length - 1].reservationId = reservationId;\r\n                  sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n                }\r\n              }\r\n            };\r\n            try {\r\n              let reservationId = null;\r\n              const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n              if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\r\n                reservationId = bookingStack[bookingStack.length - 1].reservationId;\r\n              }\r\n              const response = await Factories.create_booking({ ...params, reservationId });\r\n              console.log(\"response >> \", response);\r\n              if (response?.status === 200) {\r\n                reservationId = response?.data?.unpaidReservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const unpaidReservationId = reservationId;\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  unpaidReservationId\r\n                );\r\n                console.log(\"responseCheckout >> \", responseCheckout);\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else if (response?.status === 201) {\r\n                reservationId = response?.data?.reservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  reservationId\r\n                );\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else {\r\n                console.log(\"error create booking\");\r\n              }\r\n            } catch (error) {\r\n              console.error(\"Error create payment: \", error);\r\n\r\n              // Check if this is a promotion validation error\r\n              if (error.response && error.response.status === 400 && error.response.data.promotionValidation) {\r\n                const errorData = error.response.data;\r\n                console.log(\"Promotion validation failed during booking:\", errorData);\r\n\r\n                // Set validation error to show the modal\r\n                setValidationError({\r\n                  valid: false,\r\n                  errorCode: errorData.errorCode,\r\n                  message: errorData.message,\r\n                  severity: errorData.severity || 'ERROR',\r\n                  recoverable: errorData.recoverable || false,\r\n                  suggestedAction: errorData.suggestedAction || 'REMOVE_PROMOTION',\r\n                  alternatives: errorData.alternatives || []\r\n                });\r\n                setShowValidationModal(true);\r\n\r\n                // Show toast notification\r\n                showToast.error(errorData.message || 'Promotion is no longer valid');\r\n\r\n                return; // Don't navigate to error page\r\n              }\r\n\r\n              // For other errors, navigate to error page\r\n              navigate(Routers.ErrorPage);\r\n            }\r\n          } else {\r\n            setShowModalStatusBooking(true);\r\n          }\r\n        },\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleAccept = () => {\r\n    const totalRoomPrice = selectedRooms.reduce(\r\n      (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n      0\r\n    );\r\n\r\n    if (totalRoomPrice > 0) {\r\n      createBooking();\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: [],\r\n          selectedServices: [],\r\n          hotelDetail: hotelDetail,\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleConfirmBooking = () => {\r\n    setShowAcceptModal(true);\r\n  };\r\n\r\n  const formatCurrency = (amount) => {\r\n    if (amount === undefined || amount === null) return \"$0\";\r\n    return new Intl.NumberFormat(\"en-US\", {\r\n      style: \"currency\",\r\n      currency: \"USD\",\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 0,\r\n    }).format(amount);\r\n  };\r\n\r\n  // Add null check for hotelDetail\r\n  if (!hotelDetail) {\r\n    return (\r\n      <div\r\n        className=\"d-flex justify-content-center align-items-center\"\r\n        style={{ height: \"100vh\" }}\r\n      >\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"d-flex flex-column min-vh-100\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Header />\r\n      <div\r\n        className=\"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\"\r\n        style={{ paddingTop: \"65px\", paddingBottom: \"50px\" }}\r\n      >\r\n        <Container className=\"mt-4\">\r\n          <Row className=\"justify-content-center\">\r\n            {/* Left Card - Booking Details */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"booking-card text-white\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  marginBottom: \"20px\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"stars mb-2\"\r\n                  style={{\r\n                    justifyContent: \"flex-start\",\r\n                    justifyItems: \"self-start\",\r\n                  }}\r\n                >\r\n                  <StarRating rating={hotelDetail.star} />\r\n                </div>\r\n\r\n                <h4 className=\"hotel-name mb-1\">\r\n                  {hotelDetail.hotelName ?? \"\"}\r\n                </h4>\r\n\r\n                <p className=\"hotel-address small mb-4\">\r\n                  {hotelDetail.address ?? \"\"}\r\n                </p>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <h5 className=\"mb-4\">Your booking detail</h5>\r\n\r\n                <Row className=\"mb-4\">\r\n                  <Col xs={6}>\r\n                    <div className=\"checkin\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkin\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkinDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                  <Col xs={6}>\r\n                    <div className=\"checkout\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkout\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkoutDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n\r\n                <div className=\"stay-info mb-2\">\r\n                  <div className=\"d-flex justify-content-between mb-2\">\r\n                    <span>Total length of stay:</span>\r\n                    <span className=\"fw-bold\">{numberOfDays} night</span>{\" \"}\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between mb-3\">\r\n                    <span>Total number of people:</span>\r\n                    <span className=\"fw-bold\">\r\n                      {searchInfo.adults} Adults - {searchInfo.childrens}{\" \"}\r\n                      Childrens\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"selected-room mb-2\">\r\n                  <h5 className=\"mb-4\">You selected</h5>\r\n\r\n                  {selectedRooms.map(({ room, amount }) => (\r\n                    <div\r\n                      key={room._id}\r\n                      className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                    >\r\n                      <span>\r\n                        {amount} x {room.name} ({numberOfDays} days):\r\n                      </span>\r\n                      <span className=\"fw-bold\">\r\n                        {Utils.formatCurrency(\r\n                          room.price * amount * numberOfDays\r\n                        )}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n\r\n                  <div className=\"small mb-3\">\r\n                    <a\r\n                      className=\"text-blue text-decoration-none\"\r\n                      style={{ cursor: \"pointer\" }}\r\n                      onClick={handleBackToHomeDetail}\r\n                    >\r\n                      Change your selection\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Selected Services Section */}\r\n                {selectedServices.length > 0 && (\r\n                  <div className=\"selected-services mb-2\">\r\n                    <h5 className=\"mb-3\">Selected Services</h5>\r\n\r\n                    {selectedServices.map((service) => {\r\n                      const selectedDates = service.selectedDates || [];\r\n                      const serviceQuantity =\r\n                        service.quantity * selectedDates.length;\r\n                      const serviceTotal = service.price * serviceQuantity;\r\n\r\n                      return (\r\n                        <div\r\n                          key={service._id}\r\n                          className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                        >\r\n                          <span>\r\n                            {service.quantity} x {service.name} (\r\n                            {selectedDates.length} days):\r\n                          </span>\r\n                          <span className=\"fw-bold\">\r\n                            {Utils.formatCurrency(serviceTotal)}\r\n                          </span>\r\n                        </div>\r\n                      );\r\n                    })}\r\n\r\n                    <div className=\"small mb-3\">\r\n                      <a\r\n                        className=\"text-blue text-decoration-none\"\r\n                        style={{ cursor: \"pointer\" }}\r\n                        onClick={() => {\r\n                          dispatch({\r\n                            type: SearchActions.SAVE_SELECTED_ROOMS,\r\n                            payload: {\r\n                              selectedRooms: selectedRooms,\r\n                              selectedServices: selectedServices,\r\n                              hotelDetail: hotelDetail,\r\n                            },\r\n                          });\r\n                          navigate(-1);\r\n                        }}\r\n                      >\r\n                        Change your selection\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"promotion-section mb-3\">\r\n                  {/* Current applied promotion display */}\r\n                  {promotionDiscount > 0 ? (\r\n                    <Card \r\n                      className=\"promotion-applied mb-3\"\r\n                      style={{ \r\n                        backgroundColor: \"rgba(40, 167, 69, 0.2)\", \r\n                        borderColor: \"#28a745\",\r\n                        border: \"2px solid #28a745\"\r\n                      }}\r\n                    >\r\n                      <Card.Body className=\"py-2\">\r\n                        <div className=\"d-flex justify-content-between align-items-center\">\r\n                          <div>\r\n                            <div className=\"d-flex align-items-center\">\r\n                              <FaTag className=\"text-success me-2\" />\r\n                              <span className=\"fw-bold text-success\">{promotionCode}</span>\r\n                            </div>\r\n                            <small className=\"text-success\">\r\n                              Save {Utils.formatCurrency(promotionDiscount)}\r\n                            </small>\r\n                          </div>\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            size=\"sm\"\r\n                            onClick={() => handleApplyPromotionFromModal({\r\n                              code: \"\",\r\n                              discount: 0,\r\n                              message: \"\",\r\n                              promotionId: null\r\n                            })}\r\n                            className=\"d-flex align-items-center\"\r\n                          >\r\n                            <FaTimes className=\"me-1\" />\r\n                            Remove\r\n                          </Button>\r\n                        </div>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  ) : (\r\n                    <div className=\"text-center py-3 mb-3\" style={{ \r\n                      border: \"2px dashed rgba(255,255,255,0.3)\", \r\n                      borderRadius: \"8px\",\r\n                      backgroundColor: \"rgba(255,255,255,0.05)\"\r\n                    }}>\r\n                      <FaTag className=\"text-muted mb-2\" size={24} />\r\n                      <div className=\"text-muted small\">No promotion applied</div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Button to open promotion modal */}\r\n                  <Button\r\n                    variant=\"outline-light\"\r\n                    className=\"w-100 d-flex align-items-center justify-content-center\"\r\n                    onClick={() => setShowPromotionModal(true)}\r\n                    style={{ \r\n                      borderStyle: \"dashed\",\r\n                      borderWidth: \"2px\",\r\n                      padding: \"12px\"\r\n                    }}\r\n                  >\r\n                    <FaTag className=\"me-2\" />\r\n                    {promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"}\r\n                  </Button>\r\n                  \r\n                  {/* Promotion message */}\r\n                  {promotionMessage && (\r\n                    <div\r\n                      className={`mt-2 small text-center ${\r\n                        promotionDiscount > 0 ? \"text-success\" : \"text-danger\"\r\n                      }`}\r\n                    >\r\n                      {promotionMessage}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"total-price\">\r\n                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                    <h5 className=\"text-danger mb-0\">\r\n                      Total: {Utils.formatCurrency(finalPrice)}\r\n                    </h5>\r\n                  </div>\r\n                  <div className=\"small\">Includes taxes and fees</div>\r\n                </div>\r\n              </Card>\r\n            </Col>\r\n\r\n            {/* Right Card - Customer Information */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"info-card\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  color: \"white\",\r\n                }}\r\n              >\r\n                <h4 className=\"mb-4\">Check your information</h4>\r\n\r\n                <Form>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Full name</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={Auth.name}\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Email</Form.Label>\r\n                    <Form.Control\r\n                      type=\"email\"\r\n                      value={Auth.email}\r\n                      placeholder=\"<EMAIL>\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Phone</Form.Label>\r\n                    <Form.Control\r\n                      type=\"tel\"\r\n                      value={Auth.phoneNumber}\r\n                      placeholder=\"0912345678\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Who are you booking for?</Form.Label>\r\n                    <div>\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"mainGuest\"\r\n                        label=\"I'm the main guest\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"mainGuest\"}\r\n                        onChange={() => setBookingFor(\"mainGuest\")}\r\n                        className=\"mb-2\"\r\n                      />\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"someoneElse\"\r\n                        label=\"I'm booking for someone else\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"someoneElse\"}\r\n                        onChange={() => setBookingFor(\"someoneElse\")}\r\n                      />\r\n                    </div>\r\n                  </Form.Group>\r\n\r\n                  <div className=\"text-center\">\r\n                    <Button\r\n                      className=\"px-4 py-2\"\r\n                      style={{\r\n                        borderRadius: \"10px\",\r\n                        backgroundColor: \"white\",\r\n                        color: \"#007bff\",\r\n                        border: \"none\",\r\n                        fontWeight: \"bold\",\r\n                      }}\r\n                      onClick={handleConfirmBooking}\r\n                    >\r\n                      Booking\r\n                    </Button>\r\n                    {/* Accept Confirmation Modal */}\r\n                    <ConfirmationModal\r\n                      show={showAcceptModal}\r\n                      onHide={() => setShowAcceptModal(false)}\r\n                      onConfirm={handleAccept}\r\n                      title=\"Confirm Acceptance\"\r\n                      message=\"Do you want to proceed with this booking confirmation?\"\r\n                      confirmButtonText=\"Accept\"\r\n                      type=\"accept\"\r\n                    />\r\n                  </div>\r\n                </Form>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n        <div>\r\n          <ChatBox />\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n      \r\n      {/* Promotion Modal */}\r\n      <PromotionModal\r\n        show={showPromotionModal}\r\n        onHide={() => setShowPromotionModal(false)}\r\n        totalPrice={totalPrice}\r\n        onApplyPromotion={handleApplyPromotionFromModal}\r\n        currentPromotionId={promotionId}\r\n      />\r\n      \r\n      <HotelClosedModal\r\n        show={showModalStatusBooking}\r\n        onClose={() => {\r\n          setShowModalStatusBooking(false);\r\n        }}\r\n      />\r\n\r\n      {/* Promotion Validation Handler */}\r\n      <PromotionValidationHandler\r\n        validationError={validationError}\r\n        show={showValidationModal}\r\n        onClose={clearValidationError}\r\n        onRemovePromotion={handleRemovePromotion}\r\n        onApplyAlternative={handleApplyAlternativePromotion}\r\n        onRetry={handleRetryValidation}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BookingCheckPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,UAAU,QACL,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,0BAA0B,MAAM,gDAAgD;AACvF,OAAOC,sBAAsB,MAAM,uCAAuC;AAC1E,SAASC,SAAS,QAAQ,oCAAoC;AAC9D,SAASC,cAAc,EAAEC,cAAc,QAAQ,sBAAsB;AACrE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,SAAS,MAAM,iCAAiC;AACvD,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC7B,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAE3E,MAAMwC,IAAI,GAAGhB,cAAc,CAAEiB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;EACvD,MAAME,iBAAiB,GAAGlB,cAAc,CACrCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACD,iBAC1B,CAAC;EACD,MAAME,kBAAkB,GAAGpB,cAAc,CACtCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACE,aAC1B,CAAC;EACD,MAAMC,yBAAyB,GAAGtB,cAAc,CAC7CiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACI,gBAC1B,CAAC;EACD,MAAMC,oBAAoB,GAAGxB,cAAc,CACxCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACM,WAC1B,CAAC;EACD,MAAMC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAMiC,QAAQ,GAAG1B,cAAc,CAAC,CAAC;EACjC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,WAAW,CAAC;;EAEzD;EACA,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM;IACJ8D,eAAe;IACfC,YAAY;IACZC,mBAAmB;IACnBC,gBAAgB;IAChBC,iBAAiB;IACjBC,kBAAkB;IAClBC,eAAe;IACfC,oBAAoB;IACpBC,sBAAsB;IACtBC;EACF,CAAC,GAAGjD,sBAAsB,CAAC,CAAC;;EAE5B;EACA,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC;IAC7C6C,aAAa,EAAED,kBAAkB,IAAI,EAAE;IACvCG,gBAAgB,EAAED,yBAAyB,IAAI,EAAE;IACjDG,WAAW,EAAED,oBAAoB,IAAI,IAAI;IACzC0B,UAAU,EAAEhC;EACd,CAAC,CAAC;EAEF,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM4E,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,cAAc,GAAGN,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC;MAC5DT,cAAc,CAACU,cAAc,CAAC;;MAE9B;MACAhC,QAAQ,CAAC;QACPiC,IAAI,EAAEvD,aAAa,CAACwD,mBAAmB;QACvCC,OAAO,EAAE;UACPzC,aAAa,EAAEsC,cAAc,CAACtC,aAAa;UAC3CE,gBAAgB,EAAEoC,cAAc,CAACpC,gBAAgB;UACjDE,WAAW,EAAEkC,cAAc,CAAClC;QAC9B;MACF,CAAC,CAAC;IACJ;IACA2B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,CAACzB,QAAQ,CAAC,CAAC;;EAEd;EACAlD,SAAS,CAAC,MAAM;IACd,IAAI0E,YAAY,EAAE;MAChB,MAAMY,KAAK,GAAGT,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,MAAM,CAAC;MAC3E,IAAIM,KAAK,EAAE;QACThC,gBAAgB,CAACgC,KAAK,CAACjC,aAAa,IAAI,EAAE,CAAC;QAC3CG,oBAAoB,CAAC8B,KAAK,CAAC/B,iBAAiB,IAAI,CAAC,CAAC;QAClDG,mBAAmB,CAAC4B,KAAK,CAAC7B,gBAAgB,IAAI,EAAE,CAAC;QACjDG,cAAc,CAAC0B,KAAK,CAAC3B,WAAW,IAAI,IAAI,CAAC;;QAEzC;QACA,IAAI2B,KAAK,CAAC3B,WAAW,EAAE;UACrBO,kBAAkB,CAAC;YACjBqB,EAAE,EAAED,KAAK,CAAC3B,WAAW;YACrB6B,IAAI,EAAEF,KAAK,CAACjC,aAAa;YACzBoC,QAAQ,EAAEH,KAAK,CAAC/B;UAClB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE,CAACmB,YAAY,EAAER,kBAAkB,CAAC,CAAC;;EAEtC;EACAlE,SAAS,CAAC,MAAM;IACd,IAAI0E,YAAY,EAAE;MAAE;MAClBK,cAAc,CAACW,OAAO,CACpB,eAAe,EACfb,IAAI,CAACc,SAAS,CAAC;QACbtC,aAAa;QACbE,iBAAiB;QACjBE,gBAAgB;QAChBE;MACF,CAAC,CACH,CAAC;IACH;EACF,CAAC,EAAE,CAACN,aAAa,EAAEE,iBAAiB,EAAEE,gBAAgB,EAAEE,WAAW,EAAEe,YAAY,CAAC,CAAC;;EAEnF;EACA,MAAM9B,aAAa,GAAG2B,WAAW,CAAC3B,aAAa;EAC/C,MAAME,gBAAgB,GAAGyB,WAAW,CAACzB,gBAAgB;EACrD,MAAME,WAAW,GAAGuB,WAAW,CAACvB,WAAW;EAC3C,MAAMyB,UAAU,GAAGF,WAAW,CAACE,UAAU;;EAEzC;EACA,MAAMmB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACrB,UAAU,CAACsB,WAAW,CAAC;IAChD,MAAMC,QAAQ,GAAG,IAAIF,IAAI,CAACrB,UAAU,CAACwB,YAAY,CAAC;IAClD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAGH,OAAO,CAAC;IAC7C,MAAMQ,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB,CAAC;EAED,MAAME,YAAY,GAAGX,qBAAqB,CAAC,CAAC;;EAE5C;EACA,MAAMY,cAAc,GAAG5D,aAAa,CAAC6D,MAAM,CACzC,CAACC,KAAK,EAAE;IAAEC,IAAI;IAAEC;EAAO,CAAC,KAAKF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EACvE,CACF,CAAC;EACD,MAAMO,iBAAiB,GAAGhE,gBAAgB,CAAC2D,MAAM,CAAC,CAACC,KAAK,EAAEK,OAAO,KAAK;IACpE,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;IACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC/B,MAAM;IAC/D,OAAOyB,KAAK,GAAGK,OAAO,CAACF,KAAK,GAAGI,eAAe;EAChD,CAAC,EAAE,CAAC,CAAC;EACL,MAAME,UAAU,GAAGX,cAAc,GAAGM,iBAAiB;EACrD,MAAMM,UAAU,GAAGjB,IAAI,CAACkB,GAAG,CAACF,UAAU,GAAG5D,iBAAiB,EAAE,CAAC,CAAC;;EAE9D;EACA+D,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;IAChCf,cAAc;IACdM,iBAAiB;IACjBK,UAAU;IACV5D,iBAAiB;IACjB6D,UAAU;IACV/D;EACF,CAAC,CAAC;;EAEF;EACArD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0E,YAAY,IAAI,CAACrB,aAAa,IAAI,CAACM,WAAW,IAAIJ,iBAAiB,KAAK,CAAC,EAAE;;IAEhF;IACA,MAAMiE,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC,MAAMxD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpC,IAAI;UACF,MAAMyD,GAAG,GAAG,MAAMzH,KAAK,CAAC0H,IAAI,CAAC,4CAA4C,EAAE;YACzEnC,IAAI,EAAEnC,aAAa;YACnBuE,WAAW,EAAET;UACf,CAAC,CAAC;UAEF,IAAI,CAACO,GAAG,CAACG,IAAI,CAACC,KAAK,IAAIJ,GAAG,CAACG,IAAI,CAACpC,QAAQ,KAAKlC,iBAAiB,EAAE;YAC9D;YACAD,gBAAgB,CAAC,EAAE,CAAC;YACpBE,oBAAoB,CAAC,CAAC,CAAC;YACvBE,mBAAmB,CAAC,qDAAqD,CAAC;YAC1EE,cAAc,CAAC,IAAI,CAAC;YACpBmB,cAAc,CAACgD,UAAU,CAAC,eAAe,CAAC;UAC5C;QACF,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZ;UACA1E,gBAAgB,CAAC,EAAE,CAAC;UACpBE,oBAAoB,CAAC,CAAC,CAAC;UACvBE,mBAAmB,CAAC,8BAA8B,CAAC;UACnDE,cAAc,CAAC,IAAI,CAAC;UACpBmB,cAAc,CAACgD,UAAU,CAAC,eAAe,CAAC;QAC5C;MACF,CAAC;MAED9D,iBAAiB,CAAC,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMgE,YAAY,CAACT,SAAS,CAAC;EACtC,CAAC,EAAE,CAAC9C,YAAY,EAAEyC,UAAU,EAAE9D,aAAa,EAAEM,WAAW,EAAEJ,iBAAiB,CAAC,CAAC,CAAC,CAAC;;EAE/E;EACA,MAAM2E,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMtD,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B;MACAL,YAAY,CAACuD,GAAG,CAAC,CAAC;MAClBpD,cAAc,CAACW,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACf,YAAY,CAAC,CAAC;IACtE;IACA3B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMmF,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,KAAK;IACjC,oBACErG,OAAA;MAAKsG,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC1BA,KAAK,GAAGN,MAAM,gBACZrG,OAAA,CAACvB,MAAM;QAAa6H,SAAS,EAAC;MAAa,GAA9BK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2B,CAAC,gBAE9C/G,OAAA,CAACtB,SAAS;QAAa4H,SAAS,EAAC;MAAM,GAAvBK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAE7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlJ,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpJ,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAMqJ,6BAA6B,GAAIC,aAAa,IAAK;IACvD/B,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE8B,aAAa,CAAC;IAExE/F,gBAAgB,CAAC+F,aAAa,CAAC7D,IAAI,CAAC;IACpChC,oBAAoB,CAAC6F,aAAa,CAAC5D,QAAQ,CAAC;IAC5C/B,mBAAmB,CAAC2F,aAAa,CAACC,OAAO,CAAC;IAC1C1F,cAAc,CAACyF,aAAa,CAAC1F,WAAW,CAAC;;IAEzC;IACA,IAAI0F,aAAa,CAAC1F,WAAW,EAAE;MAC7BO,kBAAkB,CAAC;QACjBqB,EAAE,EAAE8D,aAAa,CAAC1F,WAAW;QAC7B6B,IAAI,EAAE6D,aAAa,CAAC7D,IAAI;QACxBC,QAAQ,EAAE4D,aAAa,CAAC5D;MAC1B,CAAC,CAAC;MACF6B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QACnChC,EAAE,EAAE8D,aAAa,CAAC1F,WAAW;QAC7B6B,IAAI,EAAE6D,aAAa,CAAC7D,IAAI;QACxBC,QAAQ,EAAE4D,aAAa,CAAC5D;MAC1B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA6B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCpD,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoF,qBAAqB,GAAGA,CAAA,KAAM;IAClCH,6BAA6B,CAAC;MAC5B5D,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,CAAC;MACX6D,OAAO,EAAE,EAAE;MACX3F,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6F,+BAA+B,GAAG,MAAOC,WAAW,IAAK;IAC7D;IACAL,6BAA6B,CAAC;MAC5B5D,IAAI,EAAEiE,WAAW,CAACjE,IAAI;MACtBC,QAAQ,EAAEgE,WAAW,CAAChE,QAAQ;MAC9B6D,OAAO,EAAE,kCAAkCG,WAAW,CAACC,IAAI,EAAE;MAC7D/F,WAAW,EAAE8F,WAAW,CAAClE;IAC3B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMoE,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAIhG,WAAW,IAAIwD,UAAU,EAAE;MAC7BlD,iBAAiB,CAACN,WAAW,EAAEwD,UAAU,CAAC;IAC5C;EACF,CAAC;EAED,MAAMyC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC1G,QAAQ,CAAC;MACPiC,IAAI,EAAEtD,YAAY,CAACgI,kBAAkB;MACrCxE,OAAO,EAAE;QACPyE,OAAO,EAAE9G,WAAW,CAAC+G,GAAG;QACxBC,MAAM,EAAEzH,IAAI,CAACwH,GAAG;QAChBE,SAAS,EAAE,MAAOC,KAAK,IAAK;UAC1B5C,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE2C,KAAK,CAAC;UACxD,IAAIA,KAAK,CAACC,WAAW,KAAK,QAAQ,EAAE;YAClC,MAAM3D,cAAc,GAAG5D,aAAa,CAAC6D,MAAM,CACzC,CAACC,KAAK,EAAE;cAAEC,IAAI;cAAEC;YAAO,CAAC,KACtBF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EAC5C,CACF,CAAC;YAED,MAAMO,iBAAiB,GAAGhE,gBAAgB,CAAC2D,MAAM,CAC/C,CAACC,KAAK,EAAEK,OAAO,KAAK;cAClB,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;cACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC/B,MAAM;cAC/D,OAAOyB,KAAK,GAAGK,OAAO,CAACF,KAAK,GAAGI,eAAe;YAChD,CAAC,EACD,CACF,CAAC;YAED,MAAME,UAAU,GAAGX,cAAc,GAAGM,iBAAiB;YAErD,MAAMsD,MAAM,GAAG;cACbN,OAAO,EAAE9G,WAAW,CAAC+G,GAAG;cACxBM,YAAY,EAAE5F,UAAU,CAACwB,YAAY;cACrCqE,WAAW,EAAE7F,UAAU,CAACsB,WAAW;cACnCoB,UAAU,EAAEA,UAAU;cAAE;cACxBC,UAAU,EAAEA,UAAU;cAAE;cACxBmD,WAAW,EAAE3H,aAAa,CAAC6F,GAAG,CAAC,CAAC;gBAAE9B,IAAI;gBAAEC;cAAO,CAAC,MAAM;gBACpDD,IAAI,EAAE;kBACJoD,GAAG,EAAEpD,IAAI,CAACoD;gBACZ,CAAC;gBACDnD,MAAM,EAAEA;cACV,CAAC,CAAC,CAAC;cACH4D,cAAc,EAAE1H,gBAAgB,CAAC2F,GAAG,CAAE1B,OAAO;gBAAA,IAAA0D,qBAAA;gBAAA,OAAM;kBACjDV,GAAG,EAAEhD,OAAO,CAACgD,GAAG;kBAChB7C,QAAQ,EACNH,OAAO,CAACG,QAAQ,IAAI,EAAAuD,qBAAA,GAAA1D,OAAO,CAACC,aAAa,cAAAyD,qBAAA,uBAArBA,qBAAA,CAAuBxF,MAAM,KAAI,CAAC,CAAC;kBACzDyF,UAAU,EAAE3D,OAAO,CAACC,aAAa,IAAI;gBACvC,CAAC;cAAA,CAAC,CAAC;cACH;cACA,IAAIrD,WAAW,IAAI;gBAAEA;cAAY,CAAC,CAAC;cACnC,IAAIJ,iBAAiB,GAAG,CAAC,IAAI;gBAAEA;cAAkB,CAAC;YACpD,CAAC;YAED+D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6C,MAAM,CAAC;;YAEjC;YACA,MAAMO,+BAA+B,GAAIC,aAAa,IAAK;cACzD,IAAIA,aAAa,EAAE;gBACjB,MAAMhG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;gBAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;kBAC3BL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC2F,aAAa,GAAGA,aAAa;kBACnE7F,cAAc,CAACW,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACf,YAAY,CAAC,CAAC;gBACtE;cACF;YACF,CAAC;YACD,IAAI;cACF,IAAIgG,aAAa,GAAG,IAAI;cACxB,MAAMhG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;cAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,IAAIL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC2F,aAAa,EAAE;gBAClFA,aAAa,GAAGhG,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC2F,aAAa;cACrE;cACA,MAAMC,QAAQ,GAAG,MAAMnJ,SAAS,CAACoJ,cAAc,CAAC;gBAAE,GAAGV,MAAM;gBAAEQ;cAAc,CAAC,CAAC;cAC7EtD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEsD,QAAQ,CAAC;cACrC,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;gBAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,qBAAA;gBAC5BN,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAG,cAAA,GAARH,QAAQ,CAAEhD,IAAI,cAAAmD,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBG,iBAAiB,cAAAF,qBAAA,uBAAjCA,qBAAA,CAAmClB,GAAG;gBACtDY,+BAA+B,CAACC,aAAa,CAAC;gBAC9C,MAAMQ,mBAAmB,GAAGR,aAAa;gBACzC,MAAMS,gBAAgB,GAAG,MAAM3J,SAAS,CAAC4J,gBAAgB,CACvDF,mBACF,CAAC;gBACD9D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8D,gBAAgB,CAAC;gBACrD,MAAME,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAH,qBAAA,GAAhBG,gBAAgB,CAAExD,IAAI,cAAAqD,qBAAA,uBAAtBA,qBAAA,CAAwBM,UAAU;gBACrD,IAAID,UAAU,EAAE;kBACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;gBACnC;cACF,CAAC,MAAM,IAAI,CAAAV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;gBAAA,IAAAa,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;gBACnClB,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAe,eAAA,GAARf,QAAQ,CAAEhD,IAAI,cAAA+D,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBG,WAAW,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6B9B,GAAG;gBAChDY,+BAA+B,CAACC,aAAa,CAAC;gBAC9C,MAAMS,gBAAgB,GAAG,MAAM3J,SAAS,CAAC4J,gBAAgB,CACvDV,aACF,CAAC;gBACD,MAAMW,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAS,sBAAA,GAAhBT,gBAAgB,CAAExD,IAAI,cAAAiE,sBAAA,uBAAtBA,sBAAA,CAAwBN,UAAU;gBACrD,IAAID,UAAU,EAAE;kBACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;gBACnC;cACF,CAAC,MAAM;gBACLjE,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;cACrC;YACF,CAAC,CAAC,OAAOyE,KAAK,EAAE;cACd1E,OAAO,CAAC0E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;cAE9C;cACA,IAAIA,KAAK,CAACnB,QAAQ,IAAImB,KAAK,CAACnB,QAAQ,CAACE,MAAM,KAAK,GAAG,IAAIiB,KAAK,CAACnB,QAAQ,CAAChD,IAAI,CAACoE,mBAAmB,EAAE;gBAC9F,MAAMC,SAAS,GAAGF,KAAK,CAACnB,QAAQ,CAAChD,IAAI;gBACrCP,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE2E,SAAS,CAAC;;gBAErE;gBACA5H,kBAAkB,CAAC;kBACjBwD,KAAK,EAAE,KAAK;kBACZqE,SAAS,EAAED,SAAS,CAACC,SAAS;kBAC9B7C,OAAO,EAAE4C,SAAS,CAAC5C,OAAO;kBAC1B8C,QAAQ,EAAEF,SAAS,CAACE,QAAQ,IAAI,OAAO;kBACvCC,WAAW,EAAEH,SAAS,CAACG,WAAW,IAAI,KAAK;kBAC3CC,eAAe,EAAEJ,SAAS,CAACI,eAAe,IAAI,kBAAkB;kBAChEC,YAAY,EAAEL,SAAS,CAACK,YAAY,IAAI;gBAC1C,CAAC,CAAC;gBACFlI,sBAAsB,CAAC,IAAI,CAAC;;gBAE5B;gBACA/C,SAAS,CAAC0K,KAAK,CAACE,SAAS,CAAC5C,OAAO,IAAI,8BAA8B,CAAC;gBAEpE,OAAO,CAAC;cACV;;cAEA;cACArG,QAAQ,CAACjC,OAAO,CAACwL,SAAS,CAAC;YAC7B;UACF,CAAC,MAAM;YACLlK,yBAAyB,CAAC,IAAI,CAAC;UACjC;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMjG,cAAc,GAAG5D,aAAa,CAAC6D,MAAM,CACzC,CAACC,KAAK,EAAE;MAAEC,IAAI;MAAEC;IAAO,CAAC,KAAKF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EACvE,CACF,CAAC;IAED,IAAIC,cAAc,GAAG,CAAC,EAAE;MACtBoD,aAAa,CAAC,CAAC;MACf1G,QAAQ,CAAC;QACPiC,IAAI,EAAEvD,aAAa,CAACwD,mBAAmB;QACvCC,OAAO,EAAE;UACPzC,aAAa,EAAE,EAAE;UACjBE,gBAAgB,EAAE,EAAE;UACpBE,WAAW,EAAEA;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM0J,oBAAoB,GAAGA,CAAA,KAAM;IACjCzD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM0D,cAAc,GAAI/F,MAAM,IAAK;IACjC,IAAIA,MAAM,KAAKgG,SAAS,IAAIhG,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;IACxD,OAAO,IAAIiG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACvG,MAAM,CAAC;EACnB,CAAC;;EAED;EACA,IAAI,CAAC5D,WAAW,EAAE;IAChB,oBACEhB,OAAA;MACEsG,SAAS,EAAC,kDAAkD;MAC5DyE,KAAK,EAAE;QAAEK,MAAM,EAAE;MAAQ,CAAE;MAAA7E,QAAA,eAE3BvG,OAAA;QAAKsG,SAAS,EAAC,6BAA6B;QAAC+E,IAAI,EAAC,QAAQ;QAAA9E,QAAA,eACxDvG,OAAA;UAAMsG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/G,OAAA;IACEsG,SAAS,EAAC,+BAA+B;IACzCyE,KAAK,EAAE;MACLO,eAAe,EAAE,OAAOzM,MAAM,GAAG;MACjC0M,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAAjF,QAAA,gBAEFvG,OAAA,CAAClB,MAAM;MAAA8H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACV/G,OAAA;MACEsG,SAAS,EAAC,8EAA8E;MACxFyE,KAAK,EAAE;QAAEU,UAAU,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAO,CAAE;MAAAnF,QAAA,gBAErDvG,OAAA,CAAC9B,SAAS;QAACoI,SAAS,EAAC,MAAM;QAAAC,QAAA,eACzBvG,OAAA,CAAC7B,GAAG;UAACmI,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAErCvG,OAAA,CAAC5B,GAAG;YAACuN,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAArF,QAAA,eAChBvG,OAAA,CAAC3B,IAAI;cACHiI,SAAS,EAAC,yBAAyB;cACnCyE,KAAK,EAAE;gBACLc,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE;cAChB,CAAE;cAAAzF,QAAA,gBAEFvG,OAAA;gBACEsG,SAAS,EAAC,YAAY;gBACtByE,KAAK,EAAE;kBACLkB,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE;gBAChB,CAAE;gBAAA3F,QAAA,eAEFvG,OAAA,CAACoG,UAAU;kBAACC,MAAM,EAAErF,WAAW,CAACmL;gBAAK;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAEN/G,OAAA;gBAAIsG,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAApG,qBAAA,GAC5Ba,WAAW,CAACoL,SAAS,cAAAjM,qBAAA,cAAAA,qBAAA,GAAI;cAAE;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAEL/G,OAAA;gBAAGsG,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GAAAnG,oBAAA,GACpCY,WAAW,CAACqL,OAAO,cAAAjM,oBAAA,cAAAA,oBAAA,GAAI;cAAE;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEJ/G,OAAA;gBACEsG,SAAS,EAAC,sBAAsB;gBAChCyE,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEP/G,OAAA;gBAAIsG,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE7C/G,OAAA,CAAC7B,GAAG;gBAACmI,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvG,OAAA,CAAC5B,GAAG;kBAACmO,EAAE,EAAE,CAAE;kBAAAhG,QAAA,eACTvG,OAAA;oBAAKsG,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtBvG,OAAA;sBACEsG,SAAS,EAAC,oBAAoB;sBAC9ByE,KAAK,EAAE;wBAAEyB,QAAQ,EAAE;sBAAG,CAAE;sBAAAjG,QAAA,EACzB;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN/G,OAAA;sBAAKsG,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClB9G,KAAK,CAACgN,OAAO,CAAChK,UAAU,CAACsB,WAAW,EAAE,CAAC;oBAAC;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/G,OAAA,CAAC5B,GAAG;kBAACmO,EAAE,EAAE,CAAE;kBAAAhG,QAAA,eACTvG,OAAA;oBAAKsG,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvBvG,OAAA;sBACEsG,SAAS,EAAC,oBAAoB;sBAC9ByE,KAAK,EAAE;wBAAEyB,QAAQ,EAAE;sBAAG,CAAE;sBAAAjG,QAAA,EACzB;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN/G,OAAA;sBAAKsG,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClB9G,KAAK,CAACgN,OAAO,CAAChK,UAAU,CAACwB,YAAY,EAAE,CAAC;oBAAC;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN/G,OAAA;gBAAKsG,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BvG,OAAA;kBAAKsG,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDvG,OAAA;oBAAAuG,QAAA,EAAM;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClC/G,OAAA;oBAAMsG,SAAS,EAAC,SAAS;oBAAAC,QAAA,GAAEhC,YAAY,EAAC,QAAM;kBAAA;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAAC,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN/G,OAAA;kBAAKsG,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDvG,OAAA;oBAAAuG,QAAA,EAAM;kBAAuB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpC/G,OAAA;oBAAMsG,SAAS,EAAC,SAAS;oBAAAC,QAAA,GACtB9D,UAAU,CAACiK,MAAM,EAAC,YAAU,EAACjK,UAAU,CAACkK,SAAS,EAAE,GAAG,EAAC,WAE1D;kBAAA;oBAAA/F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN/G,OAAA;gBACEsG,SAAS,EAAC,sBAAsB;gBAChCyE,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEP/G,OAAA;gBAAKsG,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCvG,OAAA;kBAAIsG,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAY;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAErCnG,aAAa,CAAC6F,GAAG,CAAC,CAAC;kBAAE9B,IAAI;kBAAEC;gBAAO,CAAC,kBAClC5E,OAAA;kBAEEsG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElEvG,OAAA;oBAAAuG,QAAA,GACG3B,MAAM,EAAC,KAAG,EAACD,IAAI,CAAC+C,IAAI,EAAC,IAAE,EAACnD,YAAY,EAAC,SACxC;kBAAA;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP/G,OAAA;oBAAMsG,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACtB9G,KAAK,CAACkL,cAAc,CACnBhG,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YACxB;kBAAC;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAVFpC,IAAI,CAACoD,GAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN,CAAC,eAEF/G,OAAA;kBAAKsG,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBvG,OAAA;oBACEsG,SAAS,EAAC,gCAAgC;oBAC1CyE,KAAK,EAAE;sBAAE6B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAE3G,sBAAuB;oBAAAK,QAAA,EACjC;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLjG,gBAAgB,CAACmC,MAAM,GAAG,CAAC,iBAC1BjD,OAAA;gBAAKsG,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCvG,OAAA;kBAAIsG,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAE1CjG,gBAAgB,CAAC2F,GAAG,CAAE1B,OAAO,IAAK;kBACjC,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;kBACjD,MAAMC,eAAe,GACnBF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC/B,MAAM;kBACzC,MAAM6J,YAAY,GAAG/H,OAAO,CAACF,KAAK,GAAGI,eAAe;kBAEpD,oBACEjF,OAAA;oBAEEsG,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,gBAElEvG,OAAA;sBAAAuG,QAAA,GACGxB,OAAO,CAACG,QAAQ,EAAC,KAAG,EAACH,OAAO,CAAC2C,IAAI,EAAC,IACnC,EAAC1C,aAAa,CAAC/B,MAAM,EAAC,SACxB;oBAAA;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP/G,OAAA;sBAAMsG,SAAS,EAAC,SAAS;sBAAAC,QAAA,EACtB9G,KAAK,CAACkL,cAAc,CAACmC,YAAY;oBAAC;sBAAAlG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA,GATFhC,OAAO,CAACgD,GAAG;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUb,CAAC;gBAEV,CAAC,CAAC,eAEF/G,OAAA;kBAAKsG,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBvG,OAAA;oBACEsG,SAAS,EAAC,gCAAgC;oBAC1CyE,KAAK,EAAE;sBAAE6B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAEA,CAAA,KAAM;sBACb3L,QAAQ,CAAC;wBACPiC,IAAI,EAAEvD,aAAa,CAACwD,mBAAmB;wBACvCC,OAAO,EAAE;0BACPzC,aAAa,EAAEA,aAAa;0BAC5BE,gBAAgB,EAAEA,gBAAgB;0BAClCE,WAAW,EAAEA;wBACf;sBACF,CAAC,CAAC;sBACFC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACd,CAAE;oBAAAsF,QAAA,EACH;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAED/G,OAAA;gBACEsG,SAAS,EAAC,sBAAsB;gBAChCyE,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEP/G,OAAA;gBAAKsG,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GAEpChF,iBAAiB,GAAG,CAAC,gBACpBvB,OAAA,CAAC3B,IAAI;kBACHiI,SAAS,EAAC,wBAAwB;kBAClCyE,KAAK,EAAE;oBACLc,eAAe,EAAE,wBAAwB;oBACzCkB,WAAW,EAAE,SAAS;oBACtBC,MAAM,EAAE;kBACV,CAAE;kBAAAzG,QAAA,eAEFvG,OAAA,CAAC3B,IAAI,CAAC4O,IAAI;oBAAC3G,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACzBvG,OAAA;sBAAKsG,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChEvG,OAAA;wBAAAuG,QAAA,gBACEvG,OAAA;0BAAKsG,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxCvG,OAAA,CAACrB,KAAK;4BAAC2H,SAAS,EAAC;0BAAmB;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvC/G,OAAA;4BAAMsG,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAElF;0BAAa;4BAAAuF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1D,CAAC,eACN/G,OAAA;0BAAOsG,SAAS,EAAC,cAAc;0BAAAC,QAAA,GAAC,OACzB,EAAC9G,KAAK,CAACkL,cAAc,CAACpJ,iBAAiB,CAAC;wBAAA;0BAAAqF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN/G,OAAA,CAACzB,MAAM;wBACL2O,OAAO,EAAC,gBAAgB;wBACxBC,IAAI,EAAC,IAAI;wBACTN,OAAO,EAAEA,CAAA,KAAMzF,6BAA6B,CAAC;0BAC3C5D,IAAI,EAAE,EAAE;0BACRC,QAAQ,EAAE,CAAC;0BACX6D,OAAO,EAAE,EAAE;0BACX3F,WAAW,EAAE;wBACf,CAAC,CAAE;wBACH2E,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,gBAErCvG,OAAA,CAACpB,OAAO;0BAAC0H,SAAS,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAE9B;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,gBAEP/G,OAAA;kBAAKsG,SAAS,EAAC,uBAAuB;kBAACyE,KAAK,EAAE;oBAC5CiC,MAAM,EAAE,kCAAkC;oBAC1ClB,YAAY,EAAE,KAAK;oBACnBD,eAAe,EAAE;kBACnB,CAAE;kBAAAtF,QAAA,gBACAvG,OAAA,CAACrB,KAAK;oBAAC2H,SAAS,EAAC,iBAAiB;oBAAC6G,IAAI,EAAE;kBAAG;oBAAAvG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/C/G,OAAA;oBAAKsG,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CACN,eAGD/G,OAAA,CAACzB,MAAM;kBACL2O,OAAO,EAAC,eAAe;kBACvB5G,SAAS,EAAC,wDAAwD;kBAClEuG,OAAO,EAAEA,CAAA,KAAM1F,qBAAqB,CAAC,IAAI,CAAE;kBAC3C4D,KAAK,EAAE;oBACLqC,WAAW,EAAE,QAAQ;oBACrBC,WAAW,EAAE,KAAK;oBAClBtB,OAAO,EAAE;kBACX,CAAE;kBAAAxF,QAAA,gBAEFvG,OAAA,CAACrB,KAAK;oBAAC2H,SAAS,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzBxF,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,kBAAkB;gBAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,EAGRtF,gBAAgB,iBACfzB,OAAA;kBACEsG,SAAS,EAAE,0BACT/E,iBAAiB,GAAG,CAAC,GAAG,cAAc,GAAG,aAAa,EACrD;kBAAAgF,QAAA,EAEF9E;gBAAgB;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/G,OAAA;gBAAKsG,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvG,OAAA;kBAAKsG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChEvG,OAAA;oBAAIsG,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,SACxB,EAAC9G,KAAK,CAACkL,cAAc,CAACvF,UAAU,CAAC;kBAAA;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACN/G,OAAA;kBAAKsG,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGN/G,OAAA,CAAC5B,GAAG;YAACuN,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAArF,QAAA,eAChBvG,OAAA,CAAC3B,IAAI;cACHiI,SAAS,EAAC,WAAW;cACrByE,KAAK,EAAE;gBACLc,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfuB,KAAK,EAAE;cACT,CAAE;cAAA/G,QAAA,gBAEFvG,OAAA;gBAAIsG,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAsB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEhD/G,OAAA,CAAC1B,IAAI;gBAAAiI,QAAA,gBACHvG,OAAA,CAAC1B,IAAI,CAACiP,KAAK;kBAACjH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BvG,OAAA,CAAC1B,IAAI,CAACkP,KAAK;oBAAAjH,QAAA,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClC/G,OAAA,CAAC1B,IAAI,CAACmP,OAAO;oBACXtK,IAAI,EAAC,MAAM;oBACXuK,KAAK,EAAEnN,IAAI,CAACmH,IAAK;oBACjBpB,SAAS,EAAC,2BAA2B;oBACrCyE,KAAK,EAAE;sBACLiC,MAAM,EAAE,iCAAiC;sBACzClB,YAAY,EAAE;oBAChB;kBAAE;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEb/G,OAAA,CAAC1B,IAAI,CAACiP,KAAK;kBAACjH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BvG,OAAA,CAAC1B,IAAI,CAACkP,KAAK;oBAAAjH,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9B/G,OAAA,CAAC1B,IAAI,CAACmP,OAAO;oBACXtK,IAAI,EAAC,OAAO;oBACZuK,KAAK,EAAEnN,IAAI,CAACoN,KAAM;oBAClBC,WAAW,EAAC,sBAAsB;oBAClCtH,SAAS,EAAC,2BAA2B;oBACrCyE,KAAK,EAAE;sBACLiC,MAAM,EAAE,iCAAiC;sBACzClB,YAAY,EAAE;oBAChB;kBAAE;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEb/G,OAAA,CAAC1B,IAAI,CAACiP,KAAK;kBAACjH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BvG,OAAA,CAAC1B,IAAI,CAACkP,KAAK;oBAAAjH,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9B/G,OAAA,CAAC1B,IAAI,CAACmP,OAAO;oBACXtK,IAAI,EAAC,KAAK;oBACVuK,KAAK,EAAEnN,IAAI,CAACsN,WAAY;oBACxBD,WAAW,EAAC,YAAY;oBACxBtH,SAAS,EAAC,2BAA2B;oBACrCyE,KAAK,EAAE;sBACLiC,MAAM,EAAE,iCAAiC;sBACzClB,YAAY,EAAE;oBAChB;kBAAE;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEb/G,OAAA,CAAC1B,IAAI,CAACiP,KAAK;kBAACjH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BvG,OAAA,CAAC1B,IAAI,CAACkP,KAAK;oBAAAjH,QAAA,EAAC;kBAAwB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjD/G,OAAA;oBAAAuG,QAAA,gBACEvG,OAAA,CAAC1B,IAAI,CAACwP,KAAK;sBACT3K,IAAI,EAAC,OAAO;sBACZI,EAAE,EAAC,WAAW;sBACdwK,KAAK,EAAC,oBAAoB;sBAC1BrG,IAAI,EAAC,YAAY;sBACjBsG,OAAO,EAAE7M,UAAU,KAAK,WAAY;sBACpC8M,QAAQ,EAAEA,CAAA,KAAM7M,aAAa,CAAC,WAAW,CAAE;sBAC3CkF,SAAS,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACF/G,OAAA,CAAC1B,IAAI,CAACwP,KAAK;sBACT3K,IAAI,EAAC,OAAO;sBACZI,EAAE,EAAC,aAAa;sBAChBwK,KAAK,EAAC,8BAA8B;sBACpCrG,IAAI,EAAC,YAAY;sBACjBsG,OAAO,EAAE7M,UAAU,KAAK,aAAc;sBACtC8M,QAAQ,EAAEA,CAAA,KAAM7M,aAAa,CAAC,aAAa;oBAAE;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEb/G,OAAA;kBAAKsG,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvG,OAAA,CAACzB,MAAM;oBACL+H,SAAS,EAAC,WAAW;oBACrByE,KAAK,EAAE;sBACLe,YAAY,EAAE,MAAM;sBACpBD,eAAe,EAAE,OAAO;sBACxByB,KAAK,EAAE,SAAS;sBAChBN,MAAM,EAAE,MAAM;sBACdkB,UAAU,EAAE;oBACd,CAAE;oBACFrB,OAAO,EAAEnC,oBAAqB;oBAAAnE,QAAA,EAC/B;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAET/G,OAAA,CAACd,iBAAiB;oBAChBiP,IAAI,EAAEnH,eAAgB;oBACtBoH,MAAM,EAAEA,CAAA,KAAMnH,kBAAkB,CAAC,KAAK,CAAE;oBACxCoH,SAAS,EAAE5D,YAAa;oBACxB6D,KAAK,EAAC,oBAAoB;oBAC1BhH,OAAO,EAAC,wDAAwD;oBAChEiH,iBAAiB,EAAC,QAAQ;oBAC1BpL,IAAI,EAAC;kBAAQ;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZ/G,OAAA;QAAAuG,QAAA,eACEvG,OAAA,CAACL,OAAO;UAAAiH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN/G,OAAA,CAACjB,MAAM;MAAA6H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGV/G,OAAA,CAACb,cAAc;MACbgP,IAAI,EAAEjH,kBAAmB;MACzBkH,MAAM,EAAEA,CAAA,KAAMjH,qBAAqB,CAAC,KAAK,CAAE;MAC3ChC,UAAU,EAAEA,UAAW;MACvBqJ,gBAAgB,EAAEpH,6BAA8B;MAChDqH,kBAAkB,EAAE9M;IAAY;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEF/G,OAAA,CAACF,gBAAgB;MACfqO,IAAI,EAAE9N,sBAAuB;MAC7BqO,OAAO,EAAEA,CAAA,KAAM;QACbpO,yBAAyB,CAAC,KAAK,CAAC;MAClC;IAAE;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGF/G,OAAA,CAACZ,0BAA0B;MACzByC,eAAe,EAAEA,eAAgB;MACjCsM,IAAI,EAAEpM,mBAAoB;MAC1B2M,OAAO,EAAEtM,oBAAqB;MAC9BuM,iBAAiB,EAAEpH,qBAAsB;MACzCqH,kBAAkB,EAAEpH,+BAAgC;MACpDqH,OAAO,EAAElH;IAAsB;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC7G,EAAA,CA12BID,gBAAgB;EAAA,QAGPV,cAAc,EACDA,cAAc,EAGbA,cAAc,EAGPA,cAAc,EAGnBA,cAAc,EAG1BN,WAAW,EACXO,cAAc,EAqB3BH,sBAAsB;AAAA;AAAAyP,EAAA,GAtCtB7O,gBAAgB;AA42BtB,eAAeA,gBAAgB;AAAC,IAAA6O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}