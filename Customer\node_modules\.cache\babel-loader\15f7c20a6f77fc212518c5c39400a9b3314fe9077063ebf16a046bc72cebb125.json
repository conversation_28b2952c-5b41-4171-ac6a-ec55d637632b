{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { socket } from '../socket';\nimport { showToast } from '../components/ToastContainer';\nconst usePromotionValidation = () => {\n  _s();\n  const [validationError, setValidationError] = useState(null);\n  const [isValidating, setIsValidating] = useState(false);\n  const [showValidationModal, setShowValidationModal] = useState(false);\n  const [currentPromotion, setCurrentPromotion] = useState(null);\n  const Auth = useSelector(state => state.Auth);\n\n  // Initialize socket connection for promotion events\n  useEffect(() => {\n    console.log('🔌 usePromotionValidation Auth check:', {\n      userId: Auth._id,\n      isValid: Auth._id && Auth._id !== -1\n    });\n    if (!socket.connected) {\n      console.log('🔌 Connecting to WebSocket...');\n      socket.connect();\n    }\n\n    // Register user for socket events\n    if (Auth._id && Auth._id !== -1) {\n      console.log('✅ Registering user with WebSocket:', Auth._id);\n      socket.emit('register', Auth._id);\n    } else {\n      console.log('❌ No valid user ID, skipping WebSocket registration');\n    }\n\n    // Listen for promotion validation results\n    socket.on('promotion-validation-result', handleValidationResult);\n\n    // Listen for real-time promotion status changes\n    socket.on('promotion-status-changed', handlePromotionStatusChange);\n\n    // Listen for promotion expiration warnings\n    socket.on('promotion-expiration-warning', handleExpirationWarning);\n\n    // Listen for promotion usage warnings\n    socket.on('promotion-usage-warning', handleUsageWarning);\n\n    // Listen for promotion alternatives\n    socket.on('promotion-alternatives', handlePromotionAlternatives);\n\n    // Listen for validation failures during checkout\n    socket.on('promotion-validation-failed', handleValidationFailure);\n    return () => {\n      socket.off('promotion-validation-result', handleValidationResult);\n      socket.off('promotion-status-changed', handlePromotionStatusChange);\n      socket.off('promotion-expiration-warning', handleExpirationWarning);\n      socket.off('promotion-usage-warning', handleUsageWarning);\n      socket.off('promotion-alternatives', handlePromotionAlternatives);\n      socket.off('promotion-validation-failed', handleValidationFailure);\n    };\n  }, [Auth._id]);\n  const handleValidationResult = useCallback(result => {\n    setIsValidating(false);\n    if (!result.valid) {\n      setValidationError(result);\n      setShowValidationModal(true);\n    }\n  }, []);\n  const handlePromotionStatusChange = useCallback(data => {\n    console.log('🔥 Real-time promotion status change received:', data);\n    console.log('🔥 Current promotion:', currentPromotion);\n    const {\n      promotionId,\n      status,\n      reason\n    } = data;\n    if (currentPromotion && currentPromotion.id === promotionId) {\n      console.log('🚨 PROMOTION DEACTIVATED - Showing modal!');\n      setValidationError({\n        valid: false,\n        errorCode: status === 'deactivated' ? 'PROMOTION_DEACTIVATED' : 'PROMOTION_EXPIRED',\n        message: reason || 'This promotion is no longer available',\n        severity: 'ERROR',\n        recoverable: true,\n        suggestedAction: 'REMOVE_AND_SUGGEST_ALTERNATIVES'\n      });\n      setShowValidationModal(true);\n      showToast.warning('Your applied promotion is no longer available');\n    } else {\n      console.log('🔥 Promotion status change ignored - not current promotion');\n    }\n  }, [currentPromotion]);\n  const handleExpirationWarning = useCallback(data => {\n    const {\n      promotionId,\n      minutesRemaining,\n      message\n    } = data;\n    if (currentPromotion && currentPromotion.id === promotionId) {\n      showToast.warning(message);\n    }\n  }, [currentPromotion]);\n  const handleUsageWarning = useCallback(data => {\n    const {\n      promotionId,\n      remainingUses,\n      message\n    } = data;\n    if (currentPromotion && currentPromotion.id === promotionId) {\n      showToast.info(message);\n    }\n  }, [currentPromotion]);\n  const handlePromotionAlternatives = useCallback(data => {\n    const {\n      alternatives,\n      message\n    } = data;\n    if (alternatives && alternatives.length > 0) {\n      setValidationError(prev => ({\n        ...prev,\n        alternatives\n      }));\n      showToast.info(message);\n    }\n  }, []);\n  const handleValidationFailure = useCallback(data => {\n    const {\n      reservationId,\n      ...validationResult\n    } = data;\n    setValidationError(validationResult);\n    setShowValidationModal(true);\n    showToast.error('Promotion validation failed during checkout');\n  }, []);\n\n  // Join promotion room for real-time updates\n  const joinPromotionRoom = useCallback(promotionId => {\n    if (Auth._id && Auth._id !== -1 && promotionId) {\n      console.log('🔗 Joining promotion room:', {\n        userId: Auth._id,\n        promotionId\n      });\n      socket.emit('join-promotion-room', {\n        userId: Auth._id,\n        promotionId\n      });\n    } else {\n      console.log('❌ Cannot join promotion room:', {\n        userId: Auth._id,\n        promotionId\n      });\n    }\n  }, [Auth._id]);\n\n  // Leave promotion room\n  const leavePromotionRoom = useCallback(promotionId => {\n    if (Auth._id && Auth._id !== -1 && promotionId) {\n      socket.emit('leave-promotion-room', {\n        userId: Auth._id,\n        promotionId\n      });\n    }\n  }, [Auth._id]);\n\n  // Validate promotion via socket\n  const validatePromotion = useCallback((promotionId, orderAmount) => {\n    if (!Auth._id || Auth._id === -1) {\n      return;\n    }\n    setIsValidating(true);\n    socket.emit('validate-promotion', {\n      promotionId,\n      orderAmount,\n      userId: Auth._id\n    });\n  }, [Auth._id]);\n\n  // Set current promotion and join its room\n  const setActivePromotion = useCallback(promotion => {\n    console.log('🎯 setActivePromotion called with:', promotion);\n\n    // Leave previous promotion room if exists\n    if (currentPromotion && currentPromotion.id) {\n      console.log('🚪 Leaving previous promotion room:', currentPromotion.id);\n      leavePromotionRoom(currentPromotion.id);\n    }\n    setCurrentPromotion(promotion);\n\n    // Join new promotion room if promotion exists\n    if (promotion && promotion.id) {\n      console.log('🚪 Joining new promotion room:', promotion.id);\n      joinPromotionRoom(promotion.id);\n    }\n  }, [currentPromotion, joinPromotionRoom, leavePromotionRoom]);\n\n  // Clear validation error and close modal\n  const clearValidationError = useCallback(() => {\n    setValidationError(null);\n    setShowValidationModal(false);\n  }, []);\n\n  // Remove current promotion\n  const removePromotion = useCallback(() => {\n    if (currentPromotion && currentPromotion.id) {\n      leavePromotionRoom(currentPromotion.id);\n    }\n    setCurrentPromotion(null);\n    clearValidationError();\n  }, [currentPromotion, leavePromotionRoom, clearValidationError]);\n  return {\n    // State\n    validationError,\n    isValidating,\n    showValidationModal,\n    currentPromotion,\n    // Actions\n    validatePromotion,\n    setActivePromotion,\n    removePromotion,\n    clearValidationError,\n    joinPromotionRoom,\n    leavePromotionRoom,\n    // Modal control\n    setShowValidationModal,\n    setValidationError\n  };\n};\n_s(usePromotionValidation, \"G+qJ9JSpmrlUHvALDfZMZyRDaF8=\", false, function () {\n  return [useSelector];\n});\nexport default usePromotionValidation;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useSelector", "socket", "showToast", "usePromotionValidation", "_s", "validationError", "setValidationError", "isValidating", "setIsValidating", "showValidationModal", "setShowValidationModal", "currentPromotion", "setCurrentPromotion", "<PERSON><PERSON>", "state", "console", "log", "userId", "_id", "<PERSON><PERSON><PERSON><PERSON>", "connected", "connect", "emit", "on", "handleValidationResult", "handlePromotionStatusChange", "handleExpirationWarning", "handleUsageWarning", "handlePromotionAlternatives", "handleValidationFailure", "off", "result", "valid", "data", "promotionId", "status", "reason", "id", "errorCode", "message", "severity", "recoverable", "suggestedAction", "warning", "minutesRemaining", "remainingUses", "info", "alternatives", "length", "prev", "reservationId", "validationResult", "error", "joinPromotionRoom", "leavePromotionRoom", "validatePromotion", "orderAmount", "setActivePromotion", "promotion", "clearValidationError", "removePromotion"], "sources": ["E:/WDP301_UROOM/Customer/src/hooks/usePromotionValidation.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { socket } from '../socket';\nimport { showToast } from '../components/ToastContainer';\n\nconst usePromotionValidation = () => {\n  const [validationError, setValidationError] = useState(null);\n  const [isValidating, setIsValidating] = useState(false);\n  const [showValidationModal, setShowValidationModal] = useState(false);\n  const [currentPromotion, setCurrentPromotion] = useState(null);\n  \n  const Auth = useSelector((state) => state.Auth);\n\n  // Initialize socket connection for promotion events\n  useEffect(() => {\n    console.log('🔌 usePromotionValidation Auth check:', { userId: Auth._id, isValid: Auth._id && Auth._id !== -1 });\n\n    if (!socket.connected) {\n      console.log('🔌 Connecting to WebSocket...');\n      socket.connect();\n    }\n\n    // Register user for socket events\n    if (Auth._id && Auth._id !== -1) {\n      console.log('✅ Registering user with WebSocket:', Auth._id);\n      socket.emit('register', Auth._id);\n    } else {\n      console.log('❌ No valid user ID, skipping WebSocket registration');\n    }\n\n    // Listen for promotion validation results\n    socket.on('promotion-validation-result', handleValidationResult);\n    \n    // Listen for real-time promotion status changes\n    socket.on('promotion-status-changed', handlePromotionStatusChange);\n    \n    // Listen for promotion expiration warnings\n    socket.on('promotion-expiration-warning', handleExpirationWarning);\n    \n    // Listen for promotion usage warnings\n    socket.on('promotion-usage-warning', handleUsageWarning);\n    \n    // Listen for promotion alternatives\n    socket.on('promotion-alternatives', handlePromotionAlternatives);\n    \n    // Listen for validation failures during checkout\n    socket.on('promotion-validation-failed', handleValidationFailure);\n\n    return () => {\n      socket.off('promotion-validation-result', handleValidationResult);\n      socket.off('promotion-status-changed', handlePromotionStatusChange);\n      socket.off('promotion-expiration-warning', handleExpirationWarning);\n      socket.off('promotion-usage-warning', handleUsageWarning);\n      socket.off('promotion-alternatives', handlePromotionAlternatives);\n      socket.off('promotion-validation-failed', handleValidationFailure);\n    };\n  }, [Auth._id]);\n\n  const handleValidationResult = useCallback((result) => {\n    setIsValidating(false);\n    \n    if (!result.valid) {\n      setValidationError(result);\n      setShowValidationModal(true);\n    }\n  }, []);\n\n  const handlePromotionStatusChange = useCallback((data) => {\n    console.log('🔥 Real-time promotion status change received:', data);\n    console.log('🔥 Current promotion:', currentPromotion);\n\n    const { promotionId, status, reason } = data;\n\n    if (currentPromotion && currentPromotion.id === promotionId) {\n      console.log('🚨 PROMOTION DEACTIVATED - Showing modal!');\n\n      setValidationError({\n        valid: false,\n        errorCode: status === 'deactivated' ? 'PROMOTION_DEACTIVATED' : 'PROMOTION_EXPIRED',\n        message: reason || 'This promotion is no longer available',\n        severity: 'ERROR',\n        recoverable: true,\n        suggestedAction: 'REMOVE_AND_SUGGEST_ALTERNATIVES'\n      });\n      setShowValidationModal(true);\n\n      showToast.warning('Your applied promotion is no longer available');\n    } else {\n      console.log('🔥 Promotion status change ignored - not current promotion');\n    }\n  }, [currentPromotion]);\n\n  const handleExpirationWarning = useCallback((data) => {\n    const { promotionId, minutesRemaining, message } = data;\n    \n    if (currentPromotion && currentPromotion.id === promotionId) {\n      showToast.warning(message);\n    }\n  }, [currentPromotion]);\n\n  const handleUsageWarning = useCallback((data) => {\n    const { promotionId, remainingUses, message } = data;\n    \n    if (currentPromotion && currentPromotion.id === promotionId) {\n      showToast.info(message);\n    }\n  }, [currentPromotion]);\n\n  const handlePromotionAlternatives = useCallback((data) => {\n    const { alternatives, message } = data;\n    \n    if (alternatives && alternatives.length > 0) {\n      setValidationError(prev => ({\n        ...prev,\n        alternatives\n      }));\n      showToast.info(message);\n    }\n  }, []);\n\n  const handleValidationFailure = useCallback((data) => {\n    const { reservationId, ...validationResult } = data;\n    \n    setValidationError(validationResult);\n    setShowValidationModal(true);\n    \n    showToast.error('Promotion validation failed during checkout');\n  }, []);\n\n  // Join promotion room for real-time updates\n  const joinPromotionRoom = useCallback((promotionId) => {\n    if (Auth._id && Auth._id !== -1 && promotionId) {\n      console.log('🔗 Joining promotion room:', { userId: Auth._id, promotionId });\n      socket.emit('join-promotion-room', {\n        userId: Auth._id,\n        promotionId\n      });\n    } else {\n      console.log('❌ Cannot join promotion room:', { userId: Auth._id, promotionId });\n    }\n  }, [Auth._id]);\n\n  // Leave promotion room\n  const leavePromotionRoom = useCallback((promotionId) => {\n    if (Auth._id && Auth._id !== -1 && promotionId) {\n      socket.emit('leave-promotion-room', {\n        userId: Auth._id,\n        promotionId\n      });\n    }\n  }, [Auth._id]);\n\n  // Validate promotion via socket\n  const validatePromotion = useCallback((promotionId, orderAmount) => {\n    if (!Auth._id || Auth._id === -1) {\n      return;\n    }\n\n    setIsValidating(true);\n    socket.emit('validate-promotion', {\n      promotionId,\n      orderAmount,\n      userId: Auth._id\n    });\n  }, [Auth._id]);\n\n  // Set current promotion and join its room\n  const setActivePromotion = useCallback((promotion) => {\n    console.log('🎯 setActivePromotion called with:', promotion);\n\n    // Leave previous promotion room if exists\n    if (currentPromotion && currentPromotion.id) {\n      console.log('🚪 Leaving previous promotion room:', currentPromotion.id);\n      leavePromotionRoom(currentPromotion.id);\n    }\n\n    setCurrentPromotion(promotion);\n\n    // Join new promotion room if promotion exists\n    if (promotion && promotion.id) {\n      console.log('🚪 Joining new promotion room:', promotion.id);\n      joinPromotionRoom(promotion.id);\n    }\n  }, [currentPromotion, joinPromotionRoom, leavePromotionRoom]);\n\n  // Clear validation error and close modal\n  const clearValidationError = useCallback(() => {\n    setValidationError(null);\n    setShowValidationModal(false);\n  }, []);\n\n  // Remove current promotion\n  const removePromotion = useCallback(() => {\n    if (currentPromotion && currentPromotion.id) {\n      leavePromotionRoom(currentPromotion.id);\n    }\n    setCurrentPromotion(null);\n    clearValidationError();\n  }, [currentPromotion, leavePromotionRoom, clearValidationError]);\n\n  return {\n    // State\n    validationError,\n    isValidating,\n    showValidationModal,\n    currentPromotion,\n\n    // Actions\n    validatePromotion,\n    setActivePromotion,\n    removePromotion,\n    clearValidationError,\n    joinPromotionRoom,\n    leavePromotionRoom,\n\n    // Modal control\n    setShowValidationModal,\n    setValidationError\n  };\n};\n\nexport default usePromotionValidation;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,WAAW;AAClC,SAASC,SAAS,QAAQ,8BAA8B;AAExD,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACY,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACc,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAMgB,IAAI,GAAGb,WAAW,CAAEc,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAE/C;EACAf,SAAS,CAAC,MAAM;IACdiB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;MAAEC,MAAM,EAAEJ,IAAI,CAACK,GAAG;MAAEC,OAAO,EAAEN,IAAI,CAACK,GAAG,IAAIL,IAAI,CAACK,GAAG,KAAK,CAAC;IAAE,CAAC,CAAC;IAEhH,IAAI,CAACjB,MAAM,CAACmB,SAAS,EAAE;MACrBL,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5Cf,MAAM,CAACoB,OAAO,CAAC,CAAC;IAClB;;IAEA;IACA,IAAIR,IAAI,CAACK,GAAG,IAAIL,IAAI,CAACK,GAAG,KAAK,CAAC,CAAC,EAAE;MAC/BH,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEH,IAAI,CAACK,GAAG,CAAC;MAC3DjB,MAAM,CAACqB,IAAI,CAAC,UAAU,EAAET,IAAI,CAACK,GAAG,CAAC;IACnC,CAAC,MAAM;MACLH,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IACpE;;IAEA;IACAf,MAAM,CAACsB,EAAE,CAAC,6BAA6B,EAAEC,sBAAsB,CAAC;;IAEhE;IACAvB,MAAM,CAACsB,EAAE,CAAC,0BAA0B,EAAEE,2BAA2B,CAAC;;IAElE;IACAxB,MAAM,CAACsB,EAAE,CAAC,8BAA8B,EAAEG,uBAAuB,CAAC;;IAElE;IACAzB,MAAM,CAACsB,EAAE,CAAC,yBAAyB,EAAEI,kBAAkB,CAAC;;IAExD;IACA1B,MAAM,CAACsB,EAAE,CAAC,wBAAwB,EAAEK,2BAA2B,CAAC;;IAEhE;IACA3B,MAAM,CAACsB,EAAE,CAAC,6BAA6B,EAAEM,uBAAuB,CAAC;IAEjE,OAAO,MAAM;MACX5B,MAAM,CAAC6B,GAAG,CAAC,6BAA6B,EAAEN,sBAAsB,CAAC;MACjEvB,MAAM,CAAC6B,GAAG,CAAC,0BAA0B,EAAEL,2BAA2B,CAAC;MACnExB,MAAM,CAAC6B,GAAG,CAAC,8BAA8B,EAAEJ,uBAAuB,CAAC;MACnEzB,MAAM,CAAC6B,GAAG,CAAC,yBAAyB,EAAEH,kBAAkB,CAAC;MACzD1B,MAAM,CAAC6B,GAAG,CAAC,wBAAwB,EAAEF,2BAA2B,CAAC;MACjE3B,MAAM,CAAC6B,GAAG,CAAC,6BAA6B,EAAED,uBAAuB,CAAC;IACpE,CAAC;EACH,CAAC,EAAE,CAAChB,IAAI,CAACK,GAAG,CAAC,CAAC;EAEd,MAAMM,sBAAsB,GAAGzB,WAAW,CAAEgC,MAAM,IAAK;IACrDvB,eAAe,CAAC,KAAK,CAAC;IAEtB,IAAI,CAACuB,MAAM,CAACC,KAAK,EAAE;MACjB1B,kBAAkB,CAACyB,MAAM,CAAC;MAC1BrB,sBAAsB,CAAC,IAAI,CAAC;IAC9B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,2BAA2B,GAAG1B,WAAW,CAAEkC,IAAI,IAAK;IACxDlB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEiB,IAAI,CAAC;IACnElB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEL,gBAAgB,CAAC;IAEtD,MAAM;MAAEuB,WAAW;MAAEC,MAAM;MAAEC;IAAO,CAAC,GAAGH,IAAI;IAE5C,IAAItB,gBAAgB,IAAIA,gBAAgB,CAAC0B,EAAE,KAAKH,WAAW,EAAE;MAC3DnB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAExDV,kBAAkB,CAAC;QACjB0B,KAAK,EAAE,KAAK;QACZM,SAAS,EAAEH,MAAM,KAAK,aAAa,GAAG,uBAAuB,GAAG,mBAAmB;QACnFI,OAAO,EAAEH,MAAM,IAAI,uCAAuC;QAC1DI,QAAQ,EAAE,OAAO;QACjBC,WAAW,EAAE,IAAI;QACjBC,eAAe,EAAE;MACnB,CAAC,CAAC;MACFhC,sBAAsB,CAAC,IAAI,CAAC;MAE5BR,SAAS,CAACyC,OAAO,CAAC,+CAA+C,CAAC;IACpE,CAAC,MAAM;MACL5B,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IAC3E;EACF,CAAC,EAAE,CAACL,gBAAgB,CAAC,CAAC;EAEtB,MAAMe,uBAAuB,GAAG3B,WAAW,CAAEkC,IAAI,IAAK;IACpD,MAAM;MAAEC,WAAW;MAAEU,gBAAgB;MAAEL;IAAQ,CAAC,GAAGN,IAAI;IAEvD,IAAItB,gBAAgB,IAAIA,gBAAgB,CAAC0B,EAAE,KAAKH,WAAW,EAAE;MAC3DhC,SAAS,CAACyC,OAAO,CAACJ,OAAO,CAAC;IAC5B;EACF,CAAC,EAAE,CAAC5B,gBAAgB,CAAC,CAAC;EAEtB,MAAMgB,kBAAkB,GAAG5B,WAAW,CAAEkC,IAAI,IAAK;IAC/C,MAAM;MAAEC,WAAW;MAAEW,aAAa;MAAEN;IAAQ,CAAC,GAAGN,IAAI;IAEpD,IAAItB,gBAAgB,IAAIA,gBAAgB,CAAC0B,EAAE,KAAKH,WAAW,EAAE;MAC3DhC,SAAS,CAAC4C,IAAI,CAACP,OAAO,CAAC;IACzB;EACF,CAAC,EAAE,CAAC5B,gBAAgB,CAAC,CAAC;EAEtB,MAAMiB,2BAA2B,GAAG7B,WAAW,CAAEkC,IAAI,IAAK;IACxD,MAAM;MAAEc,YAAY;MAAER;IAAQ,CAAC,GAAGN,IAAI;IAEtC,IAAIc,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C1C,kBAAkB,CAAC2C,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACPF;MACF,CAAC,CAAC,CAAC;MACH7C,SAAS,CAAC4C,IAAI,CAACP,OAAO,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMV,uBAAuB,GAAG9B,WAAW,CAAEkC,IAAI,IAAK;IACpD,MAAM;MAAEiB,aAAa;MAAE,GAAGC;IAAiB,CAAC,GAAGlB,IAAI;IAEnD3B,kBAAkB,CAAC6C,gBAAgB,CAAC;IACpCzC,sBAAsB,CAAC,IAAI,CAAC;IAE5BR,SAAS,CAACkD,KAAK,CAAC,6CAA6C,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,iBAAiB,GAAGtD,WAAW,CAAEmC,WAAW,IAAK;IACrD,IAAIrB,IAAI,CAACK,GAAG,IAAIL,IAAI,CAACK,GAAG,KAAK,CAAC,CAAC,IAAIgB,WAAW,EAAE;MAC9CnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;QAAEC,MAAM,EAAEJ,IAAI,CAACK,GAAG;QAAEgB;MAAY,CAAC,CAAC;MAC5EjC,MAAM,CAACqB,IAAI,CAAC,qBAAqB,EAAE;QACjCL,MAAM,EAAEJ,IAAI,CAACK,GAAG;QAChBgB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLnB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAAEC,MAAM,EAAEJ,IAAI,CAACK,GAAG;QAAEgB;MAAY,CAAC,CAAC;IACjF;EACF,CAAC,EAAE,CAACrB,IAAI,CAACK,GAAG,CAAC,CAAC;;EAEd;EACA,MAAMoC,kBAAkB,GAAGvD,WAAW,CAAEmC,WAAW,IAAK;IACtD,IAAIrB,IAAI,CAACK,GAAG,IAAIL,IAAI,CAACK,GAAG,KAAK,CAAC,CAAC,IAAIgB,WAAW,EAAE;MAC9CjC,MAAM,CAACqB,IAAI,CAAC,sBAAsB,EAAE;QAClCL,MAAM,EAAEJ,IAAI,CAACK,GAAG;QAChBgB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACrB,IAAI,CAACK,GAAG,CAAC,CAAC;;EAEd;EACA,MAAMqC,iBAAiB,GAAGxD,WAAW,CAAC,CAACmC,WAAW,EAAEsB,WAAW,KAAK;IAClE,IAAI,CAAC3C,IAAI,CAACK,GAAG,IAAIL,IAAI,CAACK,GAAG,KAAK,CAAC,CAAC,EAAE;MAChC;IACF;IAEAV,eAAe,CAAC,IAAI,CAAC;IACrBP,MAAM,CAACqB,IAAI,CAAC,oBAAoB,EAAE;MAChCY,WAAW;MACXsB,WAAW;MACXvC,MAAM,EAAEJ,IAAI,CAACK;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,IAAI,CAACK,GAAG,CAAC,CAAC;;EAEd;EACA,MAAMuC,kBAAkB,GAAG1D,WAAW,CAAE2D,SAAS,IAAK;IACpD3C,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE0C,SAAS,CAAC;;IAE5D;IACA,IAAI/C,gBAAgB,IAAIA,gBAAgB,CAAC0B,EAAE,EAAE;MAC3CtB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEL,gBAAgB,CAAC0B,EAAE,CAAC;MACvEiB,kBAAkB,CAAC3C,gBAAgB,CAAC0B,EAAE,CAAC;IACzC;IAEAzB,mBAAmB,CAAC8C,SAAS,CAAC;;IAE9B;IACA,IAAIA,SAAS,IAAIA,SAAS,CAACrB,EAAE,EAAE;MAC7BtB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0C,SAAS,CAACrB,EAAE,CAAC;MAC3DgB,iBAAiB,CAACK,SAAS,CAACrB,EAAE,CAAC;IACjC;EACF,CAAC,EAAE,CAAC1B,gBAAgB,EAAE0C,iBAAiB,EAAEC,kBAAkB,CAAC,CAAC;;EAE7D;EACA,MAAMK,oBAAoB,GAAG5D,WAAW,CAAC,MAAM;IAC7CO,kBAAkB,CAAC,IAAI,CAAC;IACxBI,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkD,eAAe,GAAG7D,WAAW,CAAC,MAAM;IACxC,IAAIY,gBAAgB,IAAIA,gBAAgB,CAAC0B,EAAE,EAAE;MAC3CiB,kBAAkB,CAAC3C,gBAAgB,CAAC0B,EAAE,CAAC;IACzC;IACAzB,mBAAmB,CAAC,IAAI,CAAC;IACzB+C,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAChD,gBAAgB,EAAE2C,kBAAkB,EAAEK,oBAAoB,CAAC,CAAC;EAEhE,OAAO;IACL;IACAtD,eAAe;IACfE,YAAY;IACZE,mBAAmB;IACnBE,gBAAgB;IAEhB;IACA4C,iBAAiB;IACjBE,kBAAkB;IAClBG,eAAe;IACfD,oBAAoB;IACpBN,iBAAiB;IACjBC,kBAAkB;IAElB;IACA5C,sBAAsB;IACtBJ;EACF,CAAC;AACH,CAAC;AAACF,EAAA,CAtNID,sBAAsB;EAAA,QAMbH,WAAW;AAAA;AAkN1B,eAAeG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}