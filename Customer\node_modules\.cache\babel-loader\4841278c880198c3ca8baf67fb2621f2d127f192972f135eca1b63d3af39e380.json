{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\BookingCheckPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { Container, Row, Col, Card, Form, Button, InputGroup } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\nimport Banner from \"../../../images/banner.jpg\";\nimport Header from \"../Header\";\nimport Footer from \"../Footer\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport ConfirmationModal from \"@components/ConfirmationModal\";\nimport PromotionModal from \"./components/PromotionModal\";\nimport PromotionValidationHandler from \"../../../components/PromotionValidationHandler\";\nimport usePromotionValidation from \"../../../hooks/usePromotionValidation\";\nimport { showToast } from \"../../../components/ToastContainer\";\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\nimport Utils from \"../../../utils/Utils\";\nimport Factories from \"../../../redux/search/factories\";\nimport { ChatBox } from \"./HomePage\";\nimport SearchActions from \"../../../redux/search/actions\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport HotelClosedModal from \"./components/HotelClosedModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookingCheckPage = () => {\n  _s();\n  var _hotelDetail$hotelNam, _hotelDetail$address;\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const SearchInformation = useAppSelector(state => state.Search.SearchInformation);\n  const selectedRoomsTemps = useAppSelector(state => state.Search.selectedRooms);\n  const selectedServicesFromRedux = useAppSelector(state => state.Search.selectedServices);\n  const hotelDetailFromRedux = useAppSelector(state => state.Search.hotelDetail);\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\n\n  // Promotion code state\n  const [promotionCode, setPromotionCode] = useState(\"\");\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\n  const [promotionId, setPromotionId] = useState(null);\n\n  // Real-time promotion validation\n  const {\n    validationError,\n    isValidating,\n    showValidationModal,\n    currentPromotion,\n    validatePromotion,\n    setActivePromotion,\n    removePromotion,\n    clearValidationError,\n    setShowValidationModal,\n    setValidationError\n  } = usePromotionValidation();\n\n  // Force re-render state\n  const [forceUpdate, setForceUpdate] = useState(0);\n\n  // Add state for booking data\n  const [bookingData, setBookingData] = useState({\n    selectedRooms: selectedRoomsTemps || [],\n    selectedServices: selectedServicesFromRedux || [],\n    hotelDetail: hotelDetailFromRedux || null,\n    searchInfo: SearchInformation\n  });\n  const [dataRestored, setDataRestored] = useState(false);\n\n  // Restore data from sessionStorage stack when component mounts\n  useEffect(() => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      const currentBooking = bookingStack[bookingStack.length - 1];\n      setBookingData(currentBooking);\n\n      // Update Redux store with current data\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: currentBooking.selectedRooms,\n          selectedServices: currentBooking.selectedServices,\n          hotelDetail: currentBooking.hotelDetail\n        }\n      });\n    }\n    setDataRestored(true);\n  }, [dispatch]);\n\n  // Load promotion info from sessionStorage AFTER booking data is restored\n  useEffect(() => {\n    if (dataRestored) {\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\n      if (promo) {\n        setPromotionCode(promo.promotionCode || \"\");\n        setPromotionDiscount(promo.promotionDiscount || 0);\n        setPromotionMessage(promo.promotionMessage || \"\");\n        setPromotionId(promo.promotionId || null);\n\n        // Set active promotion for real-time validation when restored from sessionStorage\n        if (promo.promotionId) {\n          setActivePromotion({\n            id: promo.promotionId,\n            code: promo.promotionCode,\n            discount: promo.promotionDiscount\n          });\n        }\n      }\n    }\n  }, [dataRestored, setActivePromotion]);\n\n  // Save promotion info to sessionStorage when any promotion state changes\n  useEffect(() => {\n    if (dataRestored) {\n      // Chỉ save khi đã restore xong data\n      sessionStorage.setItem(\"promotionInfo\", JSON.stringify({\n        promotionCode,\n        promotionDiscount,\n        promotionMessage,\n        promotionId\n      }));\n    }\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored]);\n\n  // Use bookingData instead of Redux state\n  const selectedRooms = bookingData.selectedRooms;\n  const selectedServices = bookingData.selectedServices;\n  const hotelDetail = bookingData.hotelDetail;\n  const searchInfo = bookingData.searchInfo;\n\n  // Calculate number of days between check-in and check-out\n  const calculateNumberOfDays = () => {\n    const checkIn = new Date(searchInfo.checkinDate);\n    const checkOut = new Date(searchInfo.checkoutDate);\n    const diffTime = Math.abs(checkOut - checkIn);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n  const numberOfDays = calculateNumberOfDays();\n\n  // Calculate prices\n  const totalRoomPrice = selectedRooms.reduce((total, {\n    room,\n    amount\n  }) => total + room.price * amount * numberOfDays, 0);\n  const totalServicePrice = selectedServices.reduce((total, service) => {\n    const selectedDates = service.selectedDates || [];\n    const serviceQuantity = service.quantity * selectedDates.length;\n    return total + service.price * serviceQuantity;\n  }, 0);\n  const totalPrice = totalRoomPrice + totalServicePrice;\n  const finalPrice = Math.max(totalPrice - promotionDiscount, 0);\n\n  // Debug: Log price calculations\n  console.log(\"Price calculation:\", {\n    totalRoomPrice,\n    totalServicePrice,\n    totalPrice,\n    promotionDiscount,\n    finalPrice,\n    promotionCode\n  });\n\n  // Validate promotion when data is restored or booking changes\n  useEffect(() => {\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\n\n    // Add a small delay to ensure promotion is fully restored before validation\n    const timeoutId = setTimeout(() => {\n      const validatePromotion = async () => {\n        try {\n          const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\n            code: promotionCode,\n            orderAmount: totalPrice\n          });\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\n            // Promotion is no longer valid or discount changed\n            setPromotionCode(\"\");\n            setPromotionDiscount(0);\n            setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\n            setPromotionId(null);\n            sessionStorage.removeItem(\"promotionInfo\");\n          }\n        } catch (err) {\n          // Promotion validation failed\n          setPromotionCode(\"\");\n          setPromotionDiscount(0);\n          setPromotionMessage(\"Promotion is no longer valid\");\n          setPromotionId(null);\n          sessionStorage.removeItem(\"promotionInfo\");\n        }\n      };\n      validatePromotion();\n    }, 100);\n    return () => clearTimeout(timeoutId);\n  }, [dataRestored, totalPrice, promotionCode, promotionId, promotionDiscount]); // Validate when total price changes or data is restored\n\n  // Handle navigation back to HomeDetailPage\n  const handleBackToHomeDetail = () => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      // Remove the current booking from stack\n      bookingStack.pop();\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n    }\n    navigate(-1);\n  };\n\n  // Star rating component\n  const StarRating = ({\n    rating\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"star-rating\",\n      children: [...Array(5)].map((_, index) => index < rating ? /*#__PURE__*/_jsxDEV(FaStar, {\n        className: \"star filled\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(FaRegStar, {\n        className: \"star\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this);\n  };\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\n\n  // Hàm xử lý áp dụng promotion từ modal\n  const handleApplyPromotionFromModal = promotionData => {\n    console.log(\"handleApplyPromotionFromModal called with:\", promotionData);\n    setPromotionCode(promotionData.code);\n    setPromotionDiscount(promotionData.discount);\n    setPromotionMessage(promotionData.message);\n    setPromotionId(promotionData.promotionId);\n    console.log(\"State updated:\", {\n      code: promotionData.code,\n      discount: promotionData.discount,\n      message: promotionData.message,\n      id: promotionData.promotionId\n    });\n\n    // Set active promotion for real-time validation\n    if (promotionData.promotionId) {\n      setActivePromotion({\n        id: promotionData.promotionId,\n        code: promotionData.code,\n        discount: promotionData.discount\n      });\n      console.log(\"Active promotion set:\", {\n        id: promotionData.promotionId,\n        code: promotionData.code,\n        discount: promotionData.discount\n      });\n    } else {\n      // Remove promotion\n      console.log(\"Removing promotion\");\n      removePromotion();\n    }\n\n    // Save to sessionStorage\n    const promotionInfo = {\n      promotionCode: promotionData.code,\n      promotionDiscount: promotionData.discount,\n      promotionMessage: promotionData.message,\n      promotionId: promotionData.promotionId\n    };\n    sessionStorage.setItem(\"promotionInfo\", JSON.stringify(promotionInfo));\n    console.log(\"Promotion saved to sessionStorage:\", promotionInfo);\n  };\n\n  // Handle promotion validation error actions\n  const handleRemovePromotion = () => {\n    handleApplyPromotionFromModal({\n      code: \"\",\n      discount: 0,\n      message: \"\",\n      promotionId: null\n    });\n  };\n  const handleApplyAlternativePromotion = async alternative => {\n    // Apply the alternative promotion\n    handleApplyPromotionFromModal({\n      code: alternative.code,\n      discount: alternative.discount,\n      message: `Applied alternative promotion: ${alternative.name}`,\n      promotionId: alternative.id\n    });\n  };\n  const handleRetryValidation = () => {\n    if (promotionId && totalPrice) {\n      validatePromotion(promotionId, totalPrice);\n    }\n  };\n  const createBooking = async () => {\n    dispatch({\n      type: HotelActions.FETCH_DETAIL_HOTEL,\n      payload: {\n        hotelId: hotelDetail._id,\n        userId: Auth._id,\n        onSuccess: async hotel => {\n          console.log(\"Hotel detail fetched successfully:\", hotel);\n          if (hotel.ownerStatus === \"ACTIVE\") {\n            const totalRoomPrice = selectedRooms.reduce((total, {\n              room,\n              amount\n            }) => total + room.price * amount * numberOfDays, 0);\n            const totalServicePrice = selectedServices.reduce((total, service) => {\n              const selectedDates = service.selectedDates || [];\n              const serviceQuantity = service.quantity * selectedDates.length;\n              return total + service.price * serviceQuantity;\n            }, 0);\n            const totalPrice = totalRoomPrice + totalServicePrice;\n            const params = {\n              hotelId: hotelDetail._id,\n              checkOutDate: searchInfo.checkoutDate,\n              checkInDate: searchInfo.checkinDate,\n              totalPrice: totalPrice,\n              // giá gốc\n              finalPrice: finalPrice,\n              // giá sau giảm giá\n              roomDetails: selectedRooms.map(({\n                room,\n                amount\n              }) => ({\n                room: {\n                  _id: room._id\n                },\n                amount: amount\n              })),\n              serviceDetails: selectedServices.map(service => {\n                var _service$selectedDate;\n                return {\n                  _id: service._id,\n                  quantity: service.quantity * (((_service$selectedDate = service.selectedDates) === null || _service$selectedDate === void 0 ? void 0 : _service$selectedDate.length) || 0),\n                  selectDate: service.selectedDates || []\n                };\n              }),\n              // Thêm promotionId và promotionDiscount nếu có\n              ...(promotionId && {\n                promotionId\n              }),\n              ...(promotionDiscount > 0 && {\n                promotionDiscount\n              })\n            };\n            console.log(\"params >> \", params);\n\n            // Helper function to save reservationId to bookingStack\n            const saveReservationIdToBookingStack = reservationId => {\n              if (reservationId) {\n                const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n                if (bookingStack.length > 0) {\n                  bookingStack[bookingStack.length - 1].reservationId = reservationId;\n                  sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n                }\n              }\n            };\n            try {\n              let reservationId = null;\n              const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n              if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\n                reservationId = bookingStack[bookingStack.length - 1].reservationId;\n              }\n              const response = await Factories.create_booking({\n                ...params,\n                reservationId\n              });\n              console.log(\"response >> \", response);\n              if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                var _response$data, _response$data$unpaid, _responseCheckout$dat;\n                reservationId = response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : (_response$data$unpaid = _response$data.unpaidReservation) === null || _response$data$unpaid === void 0 ? void 0 : _response$data$unpaid._id;\n                saveReservationIdToBookingStack(reservationId);\n                const unpaidReservationId = reservationId;\n                const responseCheckout = await Factories.checkout_booking(unpaidReservationId);\n                console.log(\"responseCheckout >> \", responseCheckout);\n                const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat = responseCheckout.data) === null || _responseCheckout$dat === void 0 ? void 0 : _responseCheckout$dat.sessionUrl;\n                if (paymentUrl) {\n                  window.location.href = paymentUrl;\n                }\n              } else if ((response === null || response === void 0 ? void 0 : response.status) === 201) {\n                var _response$data2, _response$data2$reser, _responseCheckout$dat2;\n                reservationId = response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$reser = _response$data2.reservation) === null || _response$data2$reser === void 0 ? void 0 : _response$data2$reser._id;\n                saveReservationIdToBookingStack(reservationId);\n                const responseCheckout = await Factories.checkout_booking(reservationId);\n                const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat2 = responseCheckout.data) === null || _responseCheckout$dat2 === void 0 ? void 0 : _responseCheckout$dat2.sessionUrl;\n                if (paymentUrl) {\n                  window.location.href = paymentUrl;\n                }\n              } else {\n                console.log(\"error create booking\");\n              }\n            } catch (error) {\n              console.error(\"Error create payment: \", error);\n\n              // Check if this is a promotion validation error\n              if (error.response && error.response.status === 400 && error.response.data.promotionValidation) {\n                const errorData = error.response.data;\n                console.log(\"Promotion validation failed during booking:\", errorData);\n\n                // Set validation error to show the modal\n                setValidationError({\n                  valid: false,\n                  errorCode: errorData.errorCode,\n                  message: errorData.message,\n                  severity: errorData.severity || 'ERROR',\n                  recoverable: errorData.recoverable || false,\n                  suggestedAction: errorData.suggestedAction || 'REMOVE_PROMOTION',\n                  alternatives: errorData.alternatives || []\n                });\n                setShowValidationModal(true);\n\n                // Show toast notification\n                showToast.error(errorData.message || 'Promotion is no longer valid');\n                return; // Don't navigate to error page\n              }\n\n              // For other errors, navigate to error page\n              navigate(Routers.ErrorPage);\n            }\n          } else {\n            setShowModalStatusBooking(true);\n          }\n        }\n      }\n    });\n  };\n  const handleAccept = () => {\n    const totalRoomPrice = selectedRooms.reduce((total, {\n      room,\n      amount\n    }) => total + room.price * amount * numberOfDays, 0);\n    if (totalRoomPrice > 0) {\n      createBooking();\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: [],\n          selectedServices: [],\n          hotelDetail: hotelDetail\n        }\n      });\n    }\n  };\n  const handleConfirmBooking = () => {\n    setShowAcceptModal(true);\n  };\n  const formatCurrency = amount => {\n    if (amount === undefined || amount === null) return \"$0\";\n    return new Intl.NumberFormat(\"en-US\", {\n      style: \"currency\",\n      currency: \"USD\",\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(amount);\n  };\n\n  // Add null check for hotelDetail\n  if (!hotelDetail) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: \"100vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\",\n      style: {\n        paddingTop: \"65px\",\n        paddingBottom: \"50px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Container, {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"booking-card text-white\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                marginBottom: \"20px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stars mb-2\",\n                style: {\n                  justifyContent: \"flex-start\",\n                  justifyItems: \"self-start\"\n                },\n                children: /*#__PURE__*/_jsxDEV(StarRating, {\n                  rating: hotelDetail.star\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"hotel-name mb-1\",\n                children: (_hotelDetail$hotelNam = hotelDetail.hotelName) !== null && _hotelDetail$hotelNam !== void 0 ? _hotelDetail$hotelNam : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hotel-address small mb-4\",\n                children: (_hotelDetail$address = hotelDetail.address) !== null && _hotelDetail$address !== void 0 ? _hotelDetail$address : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-4\",\n                children: \"Your booking detail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkin\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkinDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkout\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkout\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkoutDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stay-info mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total length of stay:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [numberOfDays, \" night\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 21\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total number of people:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [searchInfo.adults, \" Adults - \", searchInfo.childrens, \" \", \"Childrens\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-room mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-4\",\n                  children: \"You selected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 19\n                }, this), selectedRooms.map(({\n                  room,\n                  amount\n                }) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [amount, \" x \", room.name, \" (\", numberOfDays, \" days):\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(room.price * amount * numberOfDays)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 23\n                  }, this)]\n                }, room._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 21\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: handleBackToHomeDetail,\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 17\n              }, this), selectedServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-services mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-3\",\n                  children: \"Selected Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 21\n                }, this), selectedServices.map(service => {\n                  const selectedDates = service.selectedDates || [];\n                  const serviceQuantity = service.quantity * selectedDates.length;\n                  const serviceTotal = service.price * serviceQuantity;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [service.quantity, \" x \", service.name, \" (\", selectedDates.length, \" days):\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 655,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"fw-bold\",\n                      children: Utils.formatCurrency(serviceTotal)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 27\n                    }, this)]\n                  }, service._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: () => {\n                      dispatch({\n                        type: SearchActions.SAVE_SELECTED_ROOMS,\n                        payload: {\n                          selectedRooms: selectedRooms,\n                          selectedServices: selectedServices,\n                          hotelDetail: hotelDetail\n                        }\n                      });\n                      navigate(-1);\n                    },\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-section mb-3\",\n                children: [promotionDiscount > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-applied mb-3\",\n                  style: {\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n                    borderColor: \"#28a745\",\n                    border: \"2px solid #28a745\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"text-success me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 712,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"fw-bold text-success\",\n                            children: promotionCode\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 713,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 711,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-success\",\n                          children: [\"Save \", Utils.formatCurrency(promotionDiscount)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 715,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 710,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleApplyPromotionFromModal({\n                          code: \"\",\n                          discount: 0,\n                          message: \"\",\n                          promotionId: null\n                        }),\n                        className: \"d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 730,\n                          columnNumber: 29\n                        }, this), \"Remove\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 719,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-3 mb-3\",\n                  style: {\n                    border: \"2px dashed rgba(255,255,255,0.3)\",\n                    borderRadius: \"8px\",\n                    backgroundColor: \"rgba(255,255,255,0.05)\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"text-muted mb-2\",\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-muted small\",\n                    children: \"No promotion applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 743,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"w-100 d-flex align-items-center justify-content-center\",\n                  onClick: () => setShowPromotionModal(true),\n                  style: {\n                    borderStyle: \"dashed\",\n                    borderWidth: \"2px\",\n                    padding: \"12px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 21\n                  }, this), promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 19\n                }, this), promotionMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `mt-2 small text-center ${promotionDiscount > 0 ? \"text-success\" : \"text-danger\"}`,\n                  children: promotionMessage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-danger mb-0\",\n                    children: [\"Total: \", Utils.formatCurrency(finalPrice)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 776,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: \"Includes taxes and fees\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                color: \"white\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-4\",\n                children: \"Check your information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 796,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Full name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: Auth.name,\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 799,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"email\",\n                    value: Auth.email,\n                    placeholder: \"<EMAIL>\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"tel\",\n                    value: Auth.phoneNumber,\n                    placeholder: \"0912345678\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Who are you booking for?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"mainGuest\",\n                      label: \"I'm the main guest\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"mainGuest\",\n                      onChange: () => setBookingFor(\"mainGuest\"),\n                      className: \"mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 843,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"someoneElse\",\n                      label: \"I'm booking for someone else\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"someoneElse\",\n                      onChange: () => setBookingFor(\"someoneElse\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 852,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    className: \"px-4 py-2\",\n                    style: {\n                      borderRadius: \"10px\",\n                      backgroundColor: \"white\",\n                      color: \"#007bff\",\n                      border: \"none\",\n                      fontWeight: \"bold\"\n                    },\n                    onClick: handleConfirmBooking,\n                    children: \"Booking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                    show: showAcceptModal,\n                    onHide: () => setShowAcceptModal(false),\n                    onConfirm: handleAccept,\n                    title: \"Confirm Acceptance\",\n                    message: \"Do you want to proceed with this booking confirmation?\",\n                    confirmButtonText: \"Accept\",\n                    type: \"accept\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 878,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(ChatBox, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 894,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 893,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 897,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionModal, {\n      show: showPromotionModal,\n      onHide: () => setShowPromotionModal(false),\n      totalPrice: totalPrice,\n      onApplyPromotion: handleApplyPromotionFromModal,\n      currentPromotionId: promotionId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 900,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HotelClosedModal, {\n      show: showModalStatusBooking,\n      onClose: () => {\n        setShowModalStatusBooking(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 908,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionValidationHandler, {\n      validationError: validationError,\n      show: showValidationModal,\n      onClose: clearValidationError,\n      onRemovePromotion: handleRemovePromotion,\n      onApplyAlternative: handleApplyAlternativePromotion,\n      onRetry: handleRetryValidation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 916,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 502,\n    columnNumber: 5\n  }, this);\n};\n_s(BookingCheckPage, \"+CaHn/K5C8c6px3izlJrZBuug20=\", false, function () {\n  return [useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useNavigate, useAppDispatch, usePromotionValidation];\n});\n_c = BookingCheckPage;\nexport default BookingCheckPage;\nvar _c;\n$RefreshReg$(_c, \"BookingCheckPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "InputGroup", "FaStar", "FaRegStar", "FaTag", "FaTimes", "Banner", "Header", "Footer", "Routers", "useNavigate", "ConfirmationModal", "PromotionModal", "PromotionValidationHandler", "usePromotionValidation", "showToast", "useAppSelector", "useAppDispatch", "Utils", "Factories", "ChatBox", "SearchActions", "HotelActions", "HotelClosedModal", "jsxDEV", "_jsxDEV", "BookingCheckPage", "_s", "_hotelDetail$hotelNam", "_hotelDetail$address", "showModalStatusBooking", "setShowModalStatusBooking", "<PERSON><PERSON>", "state", "SearchInformation", "Search", "selectedRoomsTemps", "selectedRooms", "selectedServicesFromRedux", "selectedServices", "hotelDetailFromRedux", "hotelDetail", "navigate", "dispatch", "bookingFor", "setBookingFor", "promotionCode", "setPromotionCode", "promotionDiscount", "setPromotionDiscount", "promotionMessage", "setPromotionMessage", "promotionId", "setPromotionId", "validationError", "isValidating", "showValidationModal", "currentPromotion", "validatePromotion", "setActivePromotion", "removePromotion", "clearValidationError", "setShowValidationModal", "setValidationError", "forceUpdate", "setForceUpdate", "bookingData", "setBookingData", "searchInfo", "dataRestored", "setDataRestored", "bookingStack", "JSON", "parse", "sessionStorage", "getItem", "length", "currentBooking", "type", "SAVE_SELECTED_ROOMS", "payload", "promo", "id", "code", "discount", "setItem", "stringify", "calculateNumberOfDays", "checkIn", "Date", "checkinDate", "checkOut", "checkoutDate", "diffTime", "Math", "abs", "diffDays", "ceil", "numberOfDays", "totalRoomPrice", "reduce", "total", "room", "amount", "price", "totalServicePrice", "service", "selectedDates", "serviceQuantity", "quantity", "totalPrice", "finalPrice", "max", "console", "log", "timeoutId", "setTimeout", "res", "post", "orderAmount", "data", "valid", "removeItem", "err", "clearTimeout", "handleBackToHomeDetail", "pop", "StarRating", "rating", "className", "children", "Array", "map", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showAcceptModal", "setShowAcceptModal", "showPromotionModal", "setShowPromotionModal", "handleApplyPromotionFromModal", "promotionData", "message", "promotionInfo", "handleRemovePromotion", "handleApplyAlternativePromotion", "alternative", "name", "handleRetryValidation", "createBooking", "FETCH_DETAIL_HOTEL", "hotelId", "_id", "userId", "onSuccess", "hotel", "ownerStatus", "params", "checkOutDate", "checkInDate", "roomDetails", "serviceDetails", "_service$selectedDate", "selectDate", "saveReservationIdToBookingStack", "reservationId", "response", "create_booking", "status", "_response$data", "_response$data$unpaid", "_responseCheckout$dat", "unpaidReservation", "unpaidReservationId", "responseCheckout", "checkout_booking", "paymentUrl", "sessionUrl", "window", "location", "href", "_response$data2", "_response$data2$reser", "_responseCheckout$dat2", "reservation", "error", "promotionValidation", "errorData", "errorCode", "severity", "recoverable", "suggestedAction", "alternatives", "ErrorPage", "handleAccept", "handleConfirmBooking", "formatCurrency", "undefined", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "height", "role", "backgroundImage", "backgroundSize", "backgroundPosition", "paddingTop", "paddingBottom", "md", "lg", "backgroundColor", "borderRadius", "padding", "marginBottom", "justifyContent", "justifyItems", "star", "hotelName", "address", "margin", "xs", "fontSize", "getDate", "adults", "childrens", "cursor", "onClick", "serviceTotal", "borderColor", "border", "Body", "variant", "size", "borderStyle", "borderWidth", "color", "Group", "Label", "Control", "value", "email", "placeholder", "phoneNumber", "Check", "label", "checked", "onChange", "fontWeight", "show", "onHide", "onConfirm", "title", "confirmButtonText", "onApplyPromotion", "currentPromotionId", "onClose", "onRemovePromotion", "onApplyAlternative", "onRetry", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Customer/src/pages/customer/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Card,\r\n  Form,\r\n  Button,\r\n  InputGroup,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport Header from \"../Header\";\r\nimport Footer from \"../Footer\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport ConfirmationModal from \"@components/ConfirmationModal\";\r\nimport PromotionModal from \"./components/PromotionModal\";\r\nimport PromotionValidationHandler from \"../../../components/PromotionValidationHandler\";\r\nimport usePromotionValidation from \"../../../hooks/usePromotionValidation\";\r\nimport { showToast } from \"../../../components/ToastContainer\";\r\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\r\nimport Utils from \"../../../utils/Utils\";\r\nimport Factories from \"../../../redux/search/factories\";\r\nimport { ChatBox } from \"./HomePage\";\r\nimport SearchActions from \"../../../redux/search/actions\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\nimport HotelClosedModal from \"./components/HotelClosedModal\";\r\n\r\nconst BookingCheckPage = () => {\r\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\r\n\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const SearchInformation = useAppSelector(\r\n    (state) => state.Search.SearchInformation\r\n  );\r\n  const selectedRoomsTemps = useAppSelector(\r\n    (state) => state.Search.selectedRooms\r\n  );\r\n  const selectedServicesFromRedux = useAppSelector(\r\n    (state) => state.Search.selectedServices\r\n  );\r\n  const hotelDetailFromRedux = useAppSelector(\r\n    (state) => state.Search.hotelDetail\r\n  );\r\n  const navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\r\n\r\n  // Promotion code state\r\n  const [promotionCode, setPromotionCode] = useState(\"\");\r\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\r\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\r\n  const [promotionId, setPromotionId] = useState(null);\r\n\r\n  // Real-time promotion validation\r\n  const {\r\n    validationError,\r\n    isValidating,\r\n    showValidationModal,\r\n    currentPromotion,\r\n    validatePromotion,\r\n    setActivePromotion,\r\n    removePromotion,\r\n    clearValidationError,\r\n    setShowValidationModal,\r\n    setValidationError\r\n  } = usePromotionValidation();\r\n\r\n  // Force re-render state\r\n  const [forceUpdate, setForceUpdate] = useState(0);\r\n\r\n  // Add state for booking data\r\n  const [bookingData, setBookingData] = useState({\r\n    selectedRooms: selectedRoomsTemps || [],\r\n    selectedServices: selectedServicesFromRedux || [],\r\n    hotelDetail: hotelDetailFromRedux || null,\r\n    searchInfo: SearchInformation,\r\n  });\r\n\r\n  const [dataRestored, setDataRestored] = useState(false);\r\n\r\n  // Restore data from sessionStorage stack when component mounts\r\n  useEffect(() => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      const currentBooking = bookingStack[bookingStack.length - 1];\r\n      setBookingData(currentBooking);\r\n\r\n      // Update Redux store with current data\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: currentBooking.selectedRooms,\r\n          selectedServices: currentBooking.selectedServices,\r\n          hotelDetail: currentBooking.hotelDetail,\r\n        },\r\n      });\r\n    }\r\n    setDataRestored(true);\r\n  }, [dispatch]);\r\n\r\n  // Load promotion info from sessionStorage AFTER booking data is restored\r\n  useEffect(() => {\r\n    if (dataRestored) {\r\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\r\n      if (promo) {\r\n        setPromotionCode(promo.promotionCode || \"\");\r\n        setPromotionDiscount(promo.promotionDiscount || 0);\r\n        setPromotionMessage(promo.promotionMessage || \"\");\r\n        setPromotionId(promo.promotionId || null);\r\n\r\n        // Set active promotion for real-time validation when restored from sessionStorage\r\n        if (promo.promotionId) {\r\n          setActivePromotion({\r\n            id: promo.promotionId,\r\n            code: promo.promotionCode,\r\n            discount: promo.promotionDiscount\r\n          });\r\n        }\r\n      }\r\n    }\r\n  }, [dataRestored, setActivePromotion]);\r\n\r\n  // Save promotion info to sessionStorage when any promotion state changes\r\n  useEffect(() => {\r\n    if (dataRestored) { // Chỉ save khi đã restore xong data\r\n      sessionStorage.setItem(\r\n        \"promotionInfo\",\r\n        JSON.stringify({\r\n          promotionCode,\r\n          promotionDiscount,\r\n          promotionMessage,\r\n          promotionId,\r\n        })\r\n      );\r\n    }\r\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored]);\r\n\r\n  // Use bookingData instead of Redux state\r\n  const selectedRooms = bookingData.selectedRooms;\r\n  const selectedServices = bookingData.selectedServices;\r\n  const hotelDetail = bookingData.hotelDetail;\r\n  const searchInfo = bookingData.searchInfo;\r\n\r\n  // Calculate number of days between check-in and check-out\r\n  const calculateNumberOfDays = () => {\r\n    const checkIn = new Date(searchInfo.checkinDate);\r\n    const checkOut = new Date(searchInfo.checkoutDate);\r\n    const diffTime = Math.abs(checkOut - checkIn);\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    return diffDays;\r\n  };\r\n\r\n  const numberOfDays = calculateNumberOfDays();\r\n\r\n  // Calculate prices\r\n  const totalRoomPrice = selectedRooms.reduce(\r\n    (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n    0\r\n  );\r\n  const totalServicePrice = selectedServices.reduce((total, service) => {\r\n    const selectedDates = service.selectedDates || [];\r\n    const serviceQuantity = service.quantity * selectedDates.length;\r\n    return total + service.price * serviceQuantity;\r\n  }, 0);\r\n  const totalPrice = totalRoomPrice + totalServicePrice;\r\n  const finalPrice = Math.max(totalPrice - promotionDiscount, 0);\r\n\r\n  // Debug: Log price calculations\r\n  console.log(\"Price calculation:\", {\r\n    totalRoomPrice,\r\n    totalServicePrice,\r\n    totalPrice,\r\n    promotionDiscount,\r\n    finalPrice,\r\n    promotionCode\r\n  });\r\n\r\n  // Validate promotion when data is restored or booking changes\r\n  useEffect(() => {\r\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\r\n\r\n    // Add a small delay to ensure promotion is fully restored before validation\r\n    const timeoutId = setTimeout(() => {\r\n      const validatePromotion = async () => {\r\n        try {\r\n          const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\r\n            code: promotionCode,\r\n            orderAmount: totalPrice,\r\n          });\r\n          \r\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\r\n            // Promotion is no longer valid or discount changed\r\n            setPromotionCode(\"\");\r\n            setPromotionDiscount(0);\r\n            setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\r\n            setPromotionId(null);\r\n            sessionStorage.removeItem(\"promotionInfo\");\r\n          }\r\n        } catch (err) {\r\n          // Promotion validation failed\r\n          setPromotionCode(\"\");\r\n          setPromotionDiscount(0);\r\n          setPromotionMessage(\"Promotion is no longer valid\");\r\n          setPromotionId(null);\r\n          sessionStorage.removeItem(\"promotionInfo\");\r\n        }\r\n      };\r\n\r\n      validatePromotion();\r\n    }, 100);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [dataRestored, totalPrice, promotionCode, promotionId, promotionDiscount]); // Validate when total price changes or data is restored\r\n\r\n  // Handle navigation back to HomeDetailPage\r\n  const handleBackToHomeDetail = () => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      // Remove the current booking from stack\r\n      bookingStack.pop();\r\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n    }\r\n    navigate(-1);\r\n  };\r\n\r\n  // Star rating component\r\n  const StarRating = ({ rating }) => {\r\n    return (\r\n      <div className=\"star-rating\">\r\n        {[...Array(5)].map((_, index) =>\r\n          index < rating ? (\r\n            <FaStar key={index} className=\"star filled\" />\r\n          ) : (\r\n            <FaRegStar key={index} className=\"star\" />\r\n          )\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\r\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\r\n\r\n  // Hàm xử lý áp dụng promotion từ modal\r\n  const handleApplyPromotionFromModal = (promotionData) => {\r\n    console.log(\"handleApplyPromotionFromModal called with:\", promotionData);\r\n\r\n    setPromotionCode(promotionData.code);\r\n    setPromotionDiscount(promotionData.discount);\r\n    setPromotionMessage(promotionData.message);\r\n    setPromotionId(promotionData.promotionId);\r\n\r\n    console.log(\"State updated:\", {\r\n      code: promotionData.code,\r\n      discount: promotionData.discount,\r\n      message: promotionData.message,\r\n      id: promotionData.promotionId\r\n    });\r\n\r\n    // Set active promotion for real-time validation\r\n    if (promotionData.promotionId) {\r\n      setActivePromotion({\r\n        id: promotionData.promotionId,\r\n        code: promotionData.code,\r\n        discount: promotionData.discount\r\n      });\r\n      console.log(\"Active promotion set:\", {\r\n        id: promotionData.promotionId,\r\n        code: promotionData.code,\r\n        discount: promotionData.discount\r\n      });\r\n    } else {\r\n      // Remove promotion\r\n      console.log(\"Removing promotion\");\r\n      removePromotion();\r\n    }\r\n\r\n    // Save to sessionStorage\r\n    const promotionInfo = {\r\n      promotionCode: promotionData.code,\r\n      promotionDiscount: promotionData.discount,\r\n      promotionMessage: promotionData.message,\r\n      promotionId: promotionData.promotionId\r\n    };\r\n    sessionStorage.setItem(\"promotionInfo\", JSON.stringify(promotionInfo));\r\n    console.log(\"Promotion saved to sessionStorage:\", promotionInfo);\r\n  };\r\n\r\n  // Handle promotion validation error actions\r\n  const handleRemovePromotion = () => {\r\n    handleApplyPromotionFromModal({\r\n      code: \"\",\r\n      discount: 0,\r\n      message: \"\",\r\n      promotionId: null\r\n    });\r\n  };\r\n\r\n  const handleApplyAlternativePromotion = async (alternative) => {\r\n    // Apply the alternative promotion\r\n    handleApplyPromotionFromModal({\r\n      code: alternative.code,\r\n      discount: alternative.discount,\r\n      message: `Applied alternative promotion: ${alternative.name}`,\r\n      promotionId: alternative.id\r\n    });\r\n  };\r\n\r\n  const handleRetryValidation = () => {\r\n    if (promotionId && totalPrice) {\r\n      validatePromotion(promotionId, totalPrice);\r\n    }\r\n  };\r\n\r\n  const createBooking = async () => {\r\n    dispatch({\r\n      type: HotelActions.FETCH_DETAIL_HOTEL,\r\n      payload: {\r\n        hotelId: hotelDetail._id,\r\n        userId: Auth._id,\r\n        onSuccess: async (hotel) => {\r\n          console.log(\"Hotel detail fetched successfully:\", hotel);\r\n          if (hotel.ownerStatus === \"ACTIVE\") {\r\n            const totalRoomPrice = selectedRooms.reduce(\r\n              (total, { room, amount }) =>\r\n                total + room.price * amount * numberOfDays,\r\n              0\r\n            );\r\n\r\n            const totalServicePrice = selectedServices.reduce(\r\n              (total, service) => {\r\n                const selectedDates = service.selectedDates || [];\r\n                const serviceQuantity = service.quantity * selectedDates.length;\r\n                return total + service.price * serviceQuantity;\r\n              },\r\n              0\r\n            );\r\n\r\n            const totalPrice = totalRoomPrice + totalServicePrice;\r\n\r\n            const params = {\r\n              hotelId: hotelDetail._id,\r\n              checkOutDate: searchInfo.checkoutDate,\r\n              checkInDate: searchInfo.checkinDate,\r\n              totalPrice: totalPrice, // giá gốc\r\n              finalPrice: finalPrice, // giá sau giảm giá\r\n              roomDetails: selectedRooms.map(({ room, amount }) => ({\r\n                room: {\r\n                  _id: room._id,\r\n                },\r\n                amount: amount,\r\n              })),\r\n              serviceDetails: selectedServices.map((service) => ({\r\n                _id: service._id,\r\n                quantity:\r\n                  service.quantity * (service.selectedDates?.length || 0),\r\n                selectDate: service.selectedDates || [],\r\n              })),\r\n              // Thêm promotionId và promotionDiscount nếu có\r\n              ...(promotionId && { promotionId }),\r\n              ...(promotionDiscount > 0 && { promotionDiscount }),\r\n            };\r\n\r\n            console.log(\"params >> \", params);\r\n\r\n            // Helper function to save reservationId to bookingStack\r\n            const saveReservationIdToBookingStack = (reservationId) => {\r\n              if (reservationId) {\r\n                const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n                if (bookingStack.length > 0) {\r\n                  bookingStack[bookingStack.length - 1].reservationId = reservationId;\r\n                  sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n                }\r\n              }\r\n            };\r\n            try {\r\n              let reservationId = null;\r\n              const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n              if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\r\n                reservationId = bookingStack[bookingStack.length - 1].reservationId;\r\n              }\r\n              const response = await Factories.create_booking({ ...params, reservationId });\r\n              console.log(\"response >> \", response);\r\n              if (response?.status === 200) {\r\n                reservationId = response?.data?.unpaidReservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const unpaidReservationId = reservationId;\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  unpaidReservationId\r\n                );\r\n                console.log(\"responseCheckout >> \", responseCheckout);\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else if (response?.status === 201) {\r\n                reservationId = response?.data?.reservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  reservationId\r\n                );\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else {\r\n                console.log(\"error create booking\");\r\n              }\r\n            } catch (error) {\r\n              console.error(\"Error create payment: \", error);\r\n\r\n              // Check if this is a promotion validation error\r\n              if (error.response && error.response.status === 400 && error.response.data.promotionValidation) {\r\n                const errorData = error.response.data;\r\n                console.log(\"Promotion validation failed during booking:\", errorData);\r\n\r\n                // Set validation error to show the modal\r\n                setValidationError({\r\n                  valid: false,\r\n                  errorCode: errorData.errorCode,\r\n                  message: errorData.message,\r\n                  severity: errorData.severity || 'ERROR',\r\n                  recoverable: errorData.recoverable || false,\r\n                  suggestedAction: errorData.suggestedAction || 'REMOVE_PROMOTION',\r\n                  alternatives: errorData.alternatives || []\r\n                });\r\n                setShowValidationModal(true);\r\n\r\n                // Show toast notification\r\n                showToast.error(errorData.message || 'Promotion is no longer valid');\r\n\r\n                return; // Don't navigate to error page\r\n              }\r\n\r\n              // For other errors, navigate to error page\r\n              navigate(Routers.ErrorPage);\r\n            }\r\n          } else {\r\n            setShowModalStatusBooking(true);\r\n          }\r\n        },\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleAccept = () => {\r\n    const totalRoomPrice = selectedRooms.reduce(\r\n      (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n      0\r\n    );\r\n\r\n    if (totalRoomPrice > 0) {\r\n      createBooking();\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: [],\r\n          selectedServices: [],\r\n          hotelDetail: hotelDetail,\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleConfirmBooking = () => {\r\n    setShowAcceptModal(true);\r\n  };\r\n\r\n  const formatCurrency = (amount) => {\r\n    if (amount === undefined || amount === null) return \"$0\";\r\n    return new Intl.NumberFormat(\"en-US\", {\r\n      style: \"currency\",\r\n      currency: \"USD\",\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 0,\r\n    }).format(amount);\r\n  };\r\n\r\n  // Add null check for hotelDetail\r\n  if (!hotelDetail) {\r\n    return (\r\n      <div\r\n        className=\"d-flex justify-content-center align-items-center\"\r\n        style={{ height: \"100vh\" }}\r\n      >\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"d-flex flex-column min-vh-100\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Header />\r\n      <div\r\n        className=\"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\"\r\n        style={{ paddingTop: \"65px\", paddingBottom: \"50px\" }}\r\n      >\r\n        <Container className=\"mt-4\">\r\n          <Row className=\"justify-content-center\">\r\n            {/* Left Card - Booking Details */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"booking-card text-white\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  marginBottom: \"20px\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"stars mb-2\"\r\n                  style={{\r\n                    justifyContent: \"flex-start\",\r\n                    justifyItems: \"self-start\",\r\n                  }}\r\n                >\r\n                  <StarRating rating={hotelDetail.star} />\r\n                </div>\r\n\r\n                <h4 className=\"hotel-name mb-1\">\r\n                  {hotelDetail.hotelName ?? \"\"}\r\n                </h4>\r\n\r\n                <p className=\"hotel-address small mb-4\">\r\n                  {hotelDetail.address ?? \"\"}\r\n                </p>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <h5 className=\"mb-4\">Your booking detail</h5>\r\n\r\n                <Row className=\"mb-4\">\r\n                  <Col xs={6}>\r\n                    <div className=\"checkin\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkin\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkinDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                  <Col xs={6}>\r\n                    <div className=\"checkout\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkout\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkoutDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n\r\n                <div className=\"stay-info mb-2\">\r\n                  <div className=\"d-flex justify-content-between mb-2\">\r\n                    <span>Total length of stay:</span>\r\n                    <span className=\"fw-bold\">{numberOfDays} night</span>{\" \"}\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between mb-3\">\r\n                    <span>Total number of people:</span>\r\n                    <span className=\"fw-bold\">\r\n                      {searchInfo.adults} Adults - {searchInfo.childrens}{\" \"}\r\n                      Childrens\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"selected-room mb-2\">\r\n                  <h5 className=\"mb-4\">You selected</h5>\r\n\r\n                  {selectedRooms.map(({ room, amount }) => (\r\n                    <div\r\n                      key={room._id}\r\n                      className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                    >\r\n                      <span>\r\n                        {amount} x {room.name} ({numberOfDays} days):\r\n                      </span>\r\n                      <span className=\"fw-bold\">\r\n                        {Utils.formatCurrency(\r\n                          room.price * amount * numberOfDays\r\n                        )}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n\r\n                  <div className=\"small mb-3\">\r\n                    <a\r\n                      className=\"text-blue text-decoration-none\"\r\n                      style={{ cursor: \"pointer\" }}\r\n                      onClick={handleBackToHomeDetail}\r\n                    >\r\n                      Change your selection\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Selected Services Section */}\r\n                {selectedServices.length > 0 && (\r\n                  <div className=\"selected-services mb-2\">\r\n                    <h5 className=\"mb-3\">Selected Services</h5>\r\n\r\n                    {selectedServices.map((service) => {\r\n                      const selectedDates = service.selectedDates || [];\r\n                      const serviceQuantity =\r\n                        service.quantity * selectedDates.length;\r\n                      const serviceTotal = service.price * serviceQuantity;\r\n\r\n                      return (\r\n                        <div\r\n                          key={service._id}\r\n                          className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                        >\r\n                          <span>\r\n                            {service.quantity} x {service.name} (\r\n                            {selectedDates.length} days):\r\n                          </span>\r\n                          <span className=\"fw-bold\">\r\n                            {Utils.formatCurrency(serviceTotal)}\r\n                          </span>\r\n                        </div>\r\n                      );\r\n                    })}\r\n\r\n                    <div className=\"small mb-3\">\r\n                      <a\r\n                        className=\"text-blue text-decoration-none\"\r\n                        style={{ cursor: \"pointer\" }}\r\n                        onClick={() => {\r\n                          dispatch({\r\n                            type: SearchActions.SAVE_SELECTED_ROOMS,\r\n                            payload: {\r\n                              selectedRooms: selectedRooms,\r\n                              selectedServices: selectedServices,\r\n                              hotelDetail: hotelDetail,\r\n                            },\r\n                          });\r\n                          navigate(-1);\r\n                        }}\r\n                      >\r\n                        Change your selection\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"promotion-section mb-3\">\r\n                  {/* Current applied promotion display */}\r\n                  {promotionDiscount > 0 ? (\r\n                    <Card \r\n                      className=\"promotion-applied mb-3\"\r\n                      style={{ \r\n                        backgroundColor: \"rgba(40, 167, 69, 0.2)\", \r\n                        borderColor: \"#28a745\",\r\n                        border: \"2px solid #28a745\"\r\n                      }}\r\n                    >\r\n                      <Card.Body className=\"py-2\">\r\n                        <div className=\"d-flex justify-content-between align-items-center\">\r\n                          <div>\r\n                            <div className=\"d-flex align-items-center\">\r\n                              <FaTag className=\"text-success me-2\" />\r\n                              <span className=\"fw-bold text-success\">{promotionCode}</span>\r\n                            </div>\r\n                            <small className=\"text-success\">\r\n                              Save {Utils.formatCurrency(promotionDiscount)}\r\n                            </small>\r\n                          </div>\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            size=\"sm\"\r\n                            onClick={() => handleApplyPromotionFromModal({\r\n                              code: \"\",\r\n                              discount: 0,\r\n                              message: \"\",\r\n                              promotionId: null\r\n                            })}\r\n                            className=\"d-flex align-items-center\"\r\n                          >\r\n                            <FaTimes className=\"me-1\" />\r\n                            Remove\r\n                          </Button>\r\n                        </div>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  ) : (\r\n                    <div className=\"text-center py-3 mb-3\" style={{ \r\n                      border: \"2px dashed rgba(255,255,255,0.3)\", \r\n                      borderRadius: \"8px\",\r\n                      backgroundColor: \"rgba(255,255,255,0.05)\"\r\n                    }}>\r\n                      <FaTag className=\"text-muted mb-2\" size={24} />\r\n                      <div className=\"text-muted small\">No promotion applied</div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Button to open promotion modal */}\r\n                  <Button\r\n                    variant=\"outline-light\"\r\n                    className=\"w-100 d-flex align-items-center justify-content-center\"\r\n                    onClick={() => setShowPromotionModal(true)}\r\n                    style={{ \r\n                      borderStyle: \"dashed\",\r\n                      borderWidth: \"2px\",\r\n                      padding: \"12px\"\r\n                    }}\r\n                  >\r\n                    <FaTag className=\"me-2\" />\r\n                    {promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"}\r\n                  </Button>\r\n                  \r\n                  {/* Promotion message */}\r\n                  {promotionMessage && (\r\n                    <div\r\n                      className={`mt-2 small text-center ${\r\n                        promotionDiscount > 0 ? \"text-success\" : \"text-danger\"\r\n                      }`}\r\n                    >\r\n                      {promotionMessage}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"total-price\">\r\n                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                    <h5 className=\"text-danger mb-0\">\r\n                      Total: {Utils.formatCurrency(finalPrice)}\r\n                    </h5>\r\n                  </div>\r\n                  <div className=\"small\">Includes taxes and fees</div>\r\n                </div>\r\n              </Card>\r\n            </Col>\r\n\r\n            {/* Right Card - Customer Information */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"info-card\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  color: \"white\",\r\n                }}\r\n              >\r\n                <h4 className=\"mb-4\">Check your information</h4>\r\n\r\n                <Form>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Full name</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={Auth.name}\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Email</Form.Label>\r\n                    <Form.Control\r\n                      type=\"email\"\r\n                      value={Auth.email}\r\n                      placeholder=\"<EMAIL>\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Phone</Form.Label>\r\n                    <Form.Control\r\n                      type=\"tel\"\r\n                      value={Auth.phoneNumber}\r\n                      placeholder=\"0912345678\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Who are you booking for?</Form.Label>\r\n                    <div>\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"mainGuest\"\r\n                        label=\"I'm the main guest\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"mainGuest\"}\r\n                        onChange={() => setBookingFor(\"mainGuest\")}\r\n                        className=\"mb-2\"\r\n                      />\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"someoneElse\"\r\n                        label=\"I'm booking for someone else\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"someoneElse\"}\r\n                        onChange={() => setBookingFor(\"someoneElse\")}\r\n                      />\r\n                    </div>\r\n                  </Form.Group>\r\n\r\n                  <div className=\"text-center\">\r\n                    <Button\r\n                      className=\"px-4 py-2\"\r\n                      style={{\r\n                        borderRadius: \"10px\",\r\n                        backgroundColor: \"white\",\r\n                        color: \"#007bff\",\r\n                        border: \"none\",\r\n                        fontWeight: \"bold\",\r\n                      }}\r\n                      onClick={handleConfirmBooking}\r\n                    >\r\n                      Booking\r\n                    </Button>\r\n                    {/* Accept Confirmation Modal */}\r\n                    <ConfirmationModal\r\n                      show={showAcceptModal}\r\n                      onHide={() => setShowAcceptModal(false)}\r\n                      onConfirm={handleAccept}\r\n                      title=\"Confirm Acceptance\"\r\n                      message=\"Do you want to proceed with this booking confirmation?\"\r\n                      confirmButtonText=\"Accept\"\r\n                      type=\"accept\"\r\n                    />\r\n                  </div>\r\n                </Form>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n        <div>\r\n          <ChatBox />\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n      \r\n      {/* Promotion Modal */}\r\n      <PromotionModal\r\n        show={showPromotionModal}\r\n        onHide={() => setShowPromotionModal(false)}\r\n        totalPrice={totalPrice}\r\n        onApplyPromotion={handleApplyPromotionFromModal}\r\n        currentPromotionId={promotionId}\r\n      />\r\n      \r\n      <HotelClosedModal\r\n        show={showModalStatusBooking}\r\n        onClose={() => {\r\n          setShowModalStatusBooking(false);\r\n        }}\r\n      />\r\n\r\n      {/* Promotion Validation Handler */}\r\n      <PromotionValidationHandler\r\n        validationError={validationError}\r\n        show={showValidationModal}\r\n        onClose={clearValidationError}\r\n        onRemovePromotion={handleRemovePromotion}\r\n        onApplyAlternative={handleApplyAlternativePromotion}\r\n        onRetry={handleRetryValidation}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BookingCheckPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,UAAU,QACL,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,0BAA0B,MAAM,gDAAgD;AACvF,OAAOC,sBAAsB,MAAM,uCAAuC;AAC1E,SAASC,SAAS,QAAQ,oCAAoC;AAC9D,SAASC,cAAc,EAAEC,cAAc,QAAQ,sBAAsB;AACrE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,SAAS,MAAM,iCAAiC;AACvD,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC7B,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAE3E,MAAMwC,IAAI,GAAGhB,cAAc,CAAEiB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;EACvD,MAAME,iBAAiB,GAAGlB,cAAc,CACrCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACD,iBAC1B,CAAC;EACD,MAAME,kBAAkB,GAAGpB,cAAc,CACtCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACE,aAC1B,CAAC;EACD,MAAMC,yBAAyB,GAAGtB,cAAc,CAC7CiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACI,gBAC1B,CAAC;EACD,MAAMC,oBAAoB,GAAGxB,cAAc,CACxCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACM,WAC1B,CAAC;EACD,MAAMC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAMiC,QAAQ,GAAG1B,cAAc,CAAC,CAAC;EACjC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,WAAW,CAAC;;EAEzD;EACA,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM;IACJ8D,eAAe;IACfC,YAAY;IACZC,mBAAmB;IACnBC,gBAAgB;IAChBC,iBAAiB;IACjBC,kBAAkB;IAClBC,eAAe;IACfC,oBAAoB;IACpBC,sBAAsB;IACtBC;EACF,CAAC,GAAGjD,sBAAsB,CAAC,CAAC;;EAE5B;EACA,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;;EAEjD;EACA,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC;IAC7C6C,aAAa,EAAED,kBAAkB,IAAI,EAAE;IACvCG,gBAAgB,EAAED,yBAAyB,IAAI,EAAE;IACjDG,WAAW,EAAED,oBAAoB,IAAI,IAAI;IACzC4B,UAAU,EAAElC;EACd,CAAC,CAAC;EAEF,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM8E,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,cAAc,GAAGN,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC;MAC5DT,cAAc,CAACU,cAAc,CAAC;;MAE9B;MACAlC,QAAQ,CAAC;QACPmC,IAAI,EAAEzD,aAAa,CAAC0D,mBAAmB;QACvCC,OAAO,EAAE;UACP3C,aAAa,EAAEwC,cAAc,CAACxC,aAAa;UAC3CE,gBAAgB,EAAEsC,cAAc,CAACtC,gBAAgB;UACjDE,WAAW,EAAEoC,cAAc,CAACpC;QAC9B;MACF,CAAC,CAAC;IACJ;IACA6B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,CAAC3B,QAAQ,CAAC,CAAC;;EAEd;EACAlD,SAAS,CAAC,MAAM;IACd,IAAI4E,YAAY,EAAE;MAChB,MAAMY,KAAK,GAAGT,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,MAAM,CAAC;MAC3E,IAAIM,KAAK,EAAE;QACTlC,gBAAgB,CAACkC,KAAK,CAACnC,aAAa,IAAI,EAAE,CAAC;QAC3CG,oBAAoB,CAACgC,KAAK,CAACjC,iBAAiB,IAAI,CAAC,CAAC;QAClDG,mBAAmB,CAAC8B,KAAK,CAAC/B,gBAAgB,IAAI,EAAE,CAAC;QACjDG,cAAc,CAAC4B,KAAK,CAAC7B,WAAW,IAAI,IAAI,CAAC;;QAEzC;QACA,IAAI6B,KAAK,CAAC7B,WAAW,EAAE;UACrBO,kBAAkB,CAAC;YACjBuB,EAAE,EAAED,KAAK,CAAC7B,WAAW;YACrB+B,IAAI,EAAEF,KAAK,CAACnC,aAAa;YACzBsC,QAAQ,EAAEH,KAAK,CAACjC;UAClB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE,CAACqB,YAAY,EAAEV,kBAAkB,CAAC,CAAC;;EAEtC;EACAlE,SAAS,CAAC,MAAM;IACd,IAAI4E,YAAY,EAAE;MAAE;MAClBK,cAAc,CAACW,OAAO,CACpB,eAAe,EACfb,IAAI,CAACc,SAAS,CAAC;QACbxC,aAAa;QACbE,iBAAiB;QACjBE,gBAAgB;QAChBE;MACF,CAAC,CACH,CAAC;IACH;EACF,CAAC,EAAE,CAACN,aAAa,EAAEE,iBAAiB,EAAEE,gBAAgB,EAAEE,WAAW,EAAEiB,YAAY,CAAC,CAAC;;EAEnF;EACA,MAAMhC,aAAa,GAAG6B,WAAW,CAAC7B,aAAa;EAC/C,MAAME,gBAAgB,GAAG2B,WAAW,CAAC3B,gBAAgB;EACrD,MAAME,WAAW,GAAGyB,WAAW,CAACzB,WAAW;EAC3C,MAAM2B,UAAU,GAAGF,WAAW,CAACE,UAAU;;EAEzC;EACA,MAAMmB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACrB,UAAU,CAACsB,WAAW,CAAC;IAChD,MAAMC,QAAQ,GAAG,IAAIF,IAAI,CAACrB,UAAU,CAACwB,YAAY,CAAC;IAClD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAGH,OAAO,CAAC;IAC7C,MAAMQ,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB,CAAC;EAED,MAAME,YAAY,GAAGX,qBAAqB,CAAC,CAAC;;EAE5C;EACA,MAAMY,cAAc,GAAG9D,aAAa,CAAC+D,MAAM,CACzC,CAACC,KAAK,EAAE;IAAEC,IAAI;IAAEC;EAAO,CAAC,KAAKF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EACvE,CACF,CAAC;EACD,MAAMO,iBAAiB,GAAGlE,gBAAgB,CAAC6D,MAAM,CAAC,CAACC,KAAK,EAAEK,OAAO,KAAK;IACpE,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;IACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC/B,MAAM;IAC/D,OAAOyB,KAAK,GAAGK,OAAO,CAACF,KAAK,GAAGI,eAAe;EAChD,CAAC,EAAE,CAAC,CAAC;EACL,MAAME,UAAU,GAAGX,cAAc,GAAGM,iBAAiB;EACrD,MAAMM,UAAU,GAAGjB,IAAI,CAACkB,GAAG,CAACF,UAAU,GAAG9D,iBAAiB,EAAE,CAAC,CAAC;;EAE9D;EACAiE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;IAChCf,cAAc;IACdM,iBAAiB;IACjBK,UAAU;IACV9D,iBAAiB;IACjB+D,UAAU;IACVjE;EACF,CAAC,CAAC;;EAEF;EACArD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4E,YAAY,IAAI,CAACvB,aAAa,IAAI,CAACM,WAAW,IAAIJ,iBAAiB,KAAK,CAAC,EAAE;;IAEhF;IACA,MAAMmE,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC,MAAM1D,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpC,IAAI;UACF,MAAM2D,GAAG,GAAG,MAAM3H,KAAK,CAAC4H,IAAI,CAAC,4CAA4C,EAAE;YACzEnC,IAAI,EAAErC,aAAa;YACnByE,WAAW,EAAET;UACf,CAAC,CAAC;UAEF,IAAI,CAACO,GAAG,CAACG,IAAI,CAACC,KAAK,IAAIJ,GAAG,CAACG,IAAI,CAACpC,QAAQ,KAAKpC,iBAAiB,EAAE;YAC9D;YACAD,gBAAgB,CAAC,EAAE,CAAC;YACpBE,oBAAoB,CAAC,CAAC,CAAC;YACvBE,mBAAmB,CAAC,qDAAqD,CAAC;YAC1EE,cAAc,CAAC,IAAI,CAAC;YACpBqB,cAAc,CAACgD,UAAU,CAAC,eAAe,CAAC;UAC5C;QACF,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZ;UACA5E,gBAAgB,CAAC,EAAE,CAAC;UACpBE,oBAAoB,CAAC,CAAC,CAAC;UACvBE,mBAAmB,CAAC,8BAA8B,CAAC;UACnDE,cAAc,CAAC,IAAI,CAAC;UACpBqB,cAAc,CAACgD,UAAU,CAAC,eAAe,CAAC;QAC5C;MACF,CAAC;MAEDhE,iBAAiB,CAAC,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMkE,YAAY,CAACT,SAAS,CAAC;EACtC,CAAC,EAAE,CAAC9C,YAAY,EAAEyC,UAAU,EAAEhE,aAAa,EAAEM,WAAW,EAAEJ,iBAAiB,CAAC,CAAC,CAAC,CAAC;;EAE/E;EACA,MAAM6E,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMtD,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B;MACAL,YAAY,CAACuD,GAAG,CAAC,CAAC;MAClBpD,cAAc,CAACW,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACf,YAAY,CAAC,CAAC;IACtE;IACA7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMqF,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,KAAK;IACjC,oBACEvG,OAAA;MAAKwG,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC1BA,KAAK,GAAGN,MAAM,gBACZvG,OAAA,CAACvB,MAAM;QAAa+H,SAAS,EAAC;MAAa,GAA9BK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2B,CAAC,gBAE9CjH,OAAA,CAACtB,SAAS;QAAa8H,SAAS,EAAC;MAAM,GAAvBK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAE7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpJ,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtJ,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAMuJ,6BAA6B,GAAIC,aAAa,IAAK;IACvD/B,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE8B,aAAa,CAAC;IAExEjG,gBAAgB,CAACiG,aAAa,CAAC7D,IAAI,CAAC;IACpClC,oBAAoB,CAAC+F,aAAa,CAAC5D,QAAQ,CAAC;IAC5CjC,mBAAmB,CAAC6F,aAAa,CAACC,OAAO,CAAC;IAC1C5F,cAAc,CAAC2F,aAAa,CAAC5F,WAAW,CAAC;IAEzC6D,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5B/B,IAAI,EAAE6D,aAAa,CAAC7D,IAAI;MACxBC,QAAQ,EAAE4D,aAAa,CAAC5D,QAAQ;MAChC6D,OAAO,EAAED,aAAa,CAACC,OAAO;MAC9B/D,EAAE,EAAE8D,aAAa,CAAC5F;IACpB,CAAC,CAAC;;IAEF;IACA,IAAI4F,aAAa,CAAC5F,WAAW,EAAE;MAC7BO,kBAAkB,CAAC;QACjBuB,EAAE,EAAE8D,aAAa,CAAC5F,WAAW;QAC7B+B,IAAI,EAAE6D,aAAa,CAAC7D,IAAI;QACxBC,QAAQ,EAAE4D,aAAa,CAAC5D;MAC1B,CAAC,CAAC;MACF6B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QACnChC,EAAE,EAAE8D,aAAa,CAAC5F,WAAW;QAC7B+B,IAAI,EAAE6D,aAAa,CAAC7D,IAAI;QACxBC,QAAQ,EAAE4D,aAAa,CAAC5D;MAC1B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA6B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCtD,eAAe,CAAC,CAAC;IACnB;;IAEA;IACA,MAAMsF,aAAa,GAAG;MACpBpG,aAAa,EAAEkG,aAAa,CAAC7D,IAAI;MACjCnC,iBAAiB,EAAEgG,aAAa,CAAC5D,QAAQ;MACzClC,gBAAgB,EAAE8F,aAAa,CAACC,OAAO;MACvC7F,WAAW,EAAE4F,aAAa,CAAC5F;IAC7B,CAAC;IACDsB,cAAc,CAACW,OAAO,CAAC,eAAe,EAAEb,IAAI,CAACc,SAAS,CAAC4D,aAAa,CAAC,CAAC;IACtEjC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEgC,aAAa,CAAC;EAClE,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClCJ,6BAA6B,CAAC;MAC5B5D,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,CAAC;MACX6D,OAAO,EAAE,EAAE;MACX7F,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgG,+BAA+B,GAAG,MAAOC,WAAW,IAAK;IAC7D;IACAN,6BAA6B,CAAC;MAC5B5D,IAAI,EAAEkE,WAAW,CAAClE,IAAI;MACtBC,QAAQ,EAAEiE,WAAW,CAACjE,QAAQ;MAC9B6D,OAAO,EAAE,kCAAkCI,WAAW,CAACC,IAAI,EAAE;MAC7DlG,WAAW,EAAEiG,WAAW,CAACnE;IAC3B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMqE,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAInG,WAAW,IAAI0D,UAAU,EAAE;MAC7BpD,iBAAiB,CAACN,WAAW,EAAE0D,UAAU,CAAC;IAC5C;EACF,CAAC;EAED,MAAM0C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC7G,QAAQ,CAAC;MACPmC,IAAI,EAAExD,YAAY,CAACmI,kBAAkB;MACrCzE,OAAO,EAAE;QACP0E,OAAO,EAAEjH,WAAW,CAACkH,GAAG;QACxBC,MAAM,EAAE5H,IAAI,CAAC2H,GAAG;QAChBE,SAAS,EAAE,MAAOC,KAAK,IAAK;UAC1B7C,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE4C,KAAK,CAAC;UACxD,IAAIA,KAAK,CAACC,WAAW,KAAK,QAAQ,EAAE;YAClC,MAAM5D,cAAc,GAAG9D,aAAa,CAAC+D,MAAM,CACzC,CAACC,KAAK,EAAE;cAAEC,IAAI;cAAEC;YAAO,CAAC,KACtBF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EAC5C,CACF,CAAC;YAED,MAAMO,iBAAiB,GAAGlE,gBAAgB,CAAC6D,MAAM,CAC/C,CAACC,KAAK,EAAEK,OAAO,KAAK;cAClB,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;cACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC/B,MAAM;cAC/D,OAAOyB,KAAK,GAAGK,OAAO,CAACF,KAAK,GAAGI,eAAe;YAChD,CAAC,EACD,CACF,CAAC;YAED,MAAME,UAAU,GAAGX,cAAc,GAAGM,iBAAiB;YAErD,MAAMuD,MAAM,GAAG;cACbN,OAAO,EAAEjH,WAAW,CAACkH,GAAG;cACxBM,YAAY,EAAE7F,UAAU,CAACwB,YAAY;cACrCsE,WAAW,EAAE9F,UAAU,CAACsB,WAAW;cACnCoB,UAAU,EAAEA,UAAU;cAAE;cACxBC,UAAU,EAAEA,UAAU;cAAE;cACxBoD,WAAW,EAAE9H,aAAa,CAAC+F,GAAG,CAAC,CAAC;gBAAE9B,IAAI;gBAAEC;cAAO,CAAC,MAAM;gBACpDD,IAAI,EAAE;kBACJqD,GAAG,EAAErD,IAAI,CAACqD;gBACZ,CAAC;gBACDpD,MAAM,EAAEA;cACV,CAAC,CAAC,CAAC;cACH6D,cAAc,EAAE7H,gBAAgB,CAAC6F,GAAG,CAAE1B,OAAO;gBAAA,IAAA2D,qBAAA;gBAAA,OAAM;kBACjDV,GAAG,EAAEjD,OAAO,CAACiD,GAAG;kBAChB9C,QAAQ,EACNH,OAAO,CAACG,QAAQ,IAAI,EAAAwD,qBAAA,GAAA3D,OAAO,CAACC,aAAa,cAAA0D,qBAAA,uBAArBA,qBAAA,CAAuBzF,MAAM,KAAI,CAAC,CAAC;kBACzD0F,UAAU,EAAE5D,OAAO,CAACC,aAAa,IAAI;gBACvC,CAAC;cAAA,CAAC,CAAC;cACH;cACA,IAAIvD,WAAW,IAAI;gBAAEA;cAAY,CAAC,CAAC;cACnC,IAAIJ,iBAAiB,GAAG,CAAC,IAAI;gBAAEA;cAAkB,CAAC;YACpD,CAAC;YAEDiE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE8C,MAAM,CAAC;;YAEjC;YACA,MAAMO,+BAA+B,GAAIC,aAAa,IAAK;cACzD,IAAIA,aAAa,EAAE;gBACjB,MAAMjG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;gBAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;kBAC3BL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC4F,aAAa,GAAGA,aAAa;kBACnE9F,cAAc,CAACW,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACf,YAAY,CAAC,CAAC;gBACtE;cACF;YACF,CAAC;YACD,IAAI;cACF,IAAIiG,aAAa,GAAG,IAAI;cACxB,MAAMjG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;cAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,IAAIL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC4F,aAAa,EAAE;gBAClFA,aAAa,GAAGjG,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC4F,aAAa;cACrE;cACA,MAAMC,QAAQ,GAAG,MAAMtJ,SAAS,CAACuJ,cAAc,CAAC;gBAAE,GAAGV,MAAM;gBAAEQ;cAAc,CAAC,CAAC;cAC7EvD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEuD,QAAQ,CAAC;cACrC,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;gBAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,qBAAA;gBAC5BN,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAG,cAAA,GAARH,QAAQ,CAAEjD,IAAI,cAAAoD,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBG,iBAAiB,cAAAF,qBAAA,uBAAjCA,qBAAA,CAAmClB,GAAG;gBACtDY,+BAA+B,CAACC,aAAa,CAAC;gBAC9C,MAAMQ,mBAAmB,GAAGR,aAAa;gBACzC,MAAMS,gBAAgB,GAAG,MAAM9J,SAAS,CAAC+J,gBAAgB,CACvDF,mBACF,CAAC;gBACD/D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+D,gBAAgB,CAAC;gBACrD,MAAME,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAH,qBAAA,GAAhBG,gBAAgB,CAAEzD,IAAI,cAAAsD,qBAAA,uBAAtBA,qBAAA,CAAwBM,UAAU;gBACrD,IAAID,UAAU,EAAE;kBACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;gBACnC;cACF,CAAC,MAAM,IAAI,CAAAV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;gBAAA,IAAAa,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;gBACnClB,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAe,eAAA,GAARf,QAAQ,CAAEjD,IAAI,cAAAgE,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBG,WAAW,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6B9B,GAAG;gBAChDY,+BAA+B,CAACC,aAAa,CAAC;gBAC9C,MAAMS,gBAAgB,GAAG,MAAM9J,SAAS,CAAC+J,gBAAgB,CACvDV,aACF,CAAC;gBACD,MAAMW,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAS,sBAAA,GAAhBT,gBAAgB,CAAEzD,IAAI,cAAAkE,sBAAA,uBAAtBA,sBAAA,CAAwBN,UAAU;gBACrD,IAAID,UAAU,EAAE;kBACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;gBACnC;cACF,CAAC,MAAM;gBACLlE,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;cACrC;YACF,CAAC,CAAC,OAAO0E,KAAK,EAAE;cACd3E,OAAO,CAAC2E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;cAE9C;cACA,IAAIA,KAAK,CAACnB,QAAQ,IAAImB,KAAK,CAACnB,QAAQ,CAACE,MAAM,KAAK,GAAG,IAAIiB,KAAK,CAACnB,QAAQ,CAACjD,IAAI,CAACqE,mBAAmB,EAAE;gBAC9F,MAAMC,SAAS,GAAGF,KAAK,CAACnB,QAAQ,CAACjD,IAAI;gBACrCP,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE4E,SAAS,CAAC;;gBAErE;gBACA/H,kBAAkB,CAAC;kBACjB0D,KAAK,EAAE,KAAK;kBACZsE,SAAS,EAAED,SAAS,CAACC,SAAS;kBAC9B9C,OAAO,EAAE6C,SAAS,CAAC7C,OAAO;kBAC1B+C,QAAQ,EAAEF,SAAS,CAACE,QAAQ,IAAI,OAAO;kBACvCC,WAAW,EAAEH,SAAS,CAACG,WAAW,IAAI,KAAK;kBAC3CC,eAAe,EAAEJ,SAAS,CAACI,eAAe,IAAI,kBAAkB;kBAChEC,YAAY,EAAEL,SAAS,CAACK,YAAY,IAAI;gBAC1C,CAAC,CAAC;gBACFrI,sBAAsB,CAAC,IAAI,CAAC;;gBAE5B;gBACA/C,SAAS,CAAC6K,KAAK,CAACE,SAAS,CAAC7C,OAAO,IAAI,8BAA8B,CAAC;gBAEpE,OAAO,CAAC;cACV;;cAEA;cACAvG,QAAQ,CAACjC,OAAO,CAAC2L,SAAS,CAAC;YAC7B;UACF,CAAC,MAAM;YACLrK,yBAAyB,CAAC,IAAI,CAAC;UACjC;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMlG,cAAc,GAAG9D,aAAa,CAAC+D,MAAM,CACzC,CAACC,KAAK,EAAE;MAAEC,IAAI;MAAEC;IAAO,CAAC,KAAKF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EACvE,CACF,CAAC;IAED,IAAIC,cAAc,GAAG,CAAC,EAAE;MACtBqD,aAAa,CAAC,CAAC;MACf7G,QAAQ,CAAC;QACPmC,IAAI,EAAEzD,aAAa,CAAC0D,mBAAmB;QACvCC,OAAO,EAAE;UACP3C,aAAa,EAAE,EAAE;UACjBE,gBAAgB,EAAE,EAAE;UACpBE,WAAW,EAAEA;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM6J,oBAAoB,GAAGA,CAAA,KAAM;IACjC1D,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM2D,cAAc,GAAIhG,MAAM,IAAK;IACjC,IAAIA,MAAM,KAAKiG,SAAS,IAAIjG,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;IACxD,OAAO,IAAIkG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACxG,MAAM,CAAC;EACnB,CAAC;;EAED;EACA,IAAI,CAAC9D,WAAW,EAAE;IAChB,oBACEhB,OAAA;MACEwG,SAAS,EAAC,kDAAkD;MAC5D0E,KAAK,EAAE;QAAEK,MAAM,EAAE;MAAQ,CAAE;MAAA9E,QAAA,eAE3BzG,OAAA;QAAKwG,SAAS,EAAC,6BAA6B;QAACgF,IAAI,EAAC,QAAQ;QAAA/E,QAAA,eACxDzG,OAAA;UAAMwG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEjH,OAAA;IACEwG,SAAS,EAAC,+BAA+B;IACzC0E,KAAK,EAAE;MACLO,eAAe,EAAE,OAAO5M,MAAM,GAAG;MACjC6M,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAAlF,QAAA,gBAEFzG,OAAA,CAAClB,MAAM;MAAAgI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVjH,OAAA;MACEwG,SAAS,EAAC,8EAA8E;MACxF0E,KAAK,EAAE;QAAEU,UAAU,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAO,CAAE;MAAApF,QAAA,gBAErDzG,OAAA,CAAC9B,SAAS;QAACsI,SAAS,EAAC,MAAM;QAAAC,QAAA,eACzBzG,OAAA,CAAC7B,GAAG;UAACqI,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAErCzG,OAAA,CAAC5B,GAAG;YAAC0N,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAtF,QAAA,eAChBzG,OAAA,CAAC3B,IAAI;cACHmI,SAAS,EAAC,yBAAyB;cACnC0E,KAAK,EAAE;gBACLc,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE;cAChB,CAAE;cAAA1F,QAAA,gBAEFzG,OAAA;gBACEwG,SAAS,EAAC,YAAY;gBACtB0E,KAAK,EAAE;kBACLkB,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE;gBAChB,CAAE;gBAAA5F,QAAA,eAEFzG,OAAA,CAACsG,UAAU;kBAACC,MAAM,EAAEvF,WAAW,CAACsL;gBAAK;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAENjH,OAAA;gBAAIwG,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAAtG,qBAAA,GAC5Ba,WAAW,CAACuL,SAAS,cAAApM,qBAAA,cAAAA,qBAAA,GAAI;cAAE;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAELjH,OAAA;gBAAGwG,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GAAArG,oBAAA,GACpCY,WAAW,CAACwL,OAAO,cAAApM,oBAAA,cAAAA,oBAAA,GAAI;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEJjH,OAAA;gBACEwG,SAAS,EAAC,sBAAsB;gBAChC0E,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPjH,OAAA;gBAAIwG,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE7CjH,OAAA,CAAC7B,GAAG;gBAACqI,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBzG,OAAA,CAAC5B,GAAG;kBAACsO,EAAE,EAAE,CAAE;kBAAAjG,QAAA,eACTzG,OAAA;oBAAKwG,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtBzG,OAAA;sBACEwG,SAAS,EAAC,oBAAoB;sBAC9B0E,KAAK,EAAE;wBAAEyB,QAAQ,EAAE;sBAAG,CAAE;sBAAAlG,QAAA,EACzB;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNjH,OAAA;sBAAKwG,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBhH,KAAK,CAACmN,OAAO,CAACjK,UAAU,CAACsB,WAAW,EAAE,CAAC;oBAAC;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjH,OAAA,CAAC5B,GAAG;kBAACsO,EAAE,EAAE,CAAE;kBAAAjG,QAAA,eACTzG,OAAA;oBAAKwG,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvBzG,OAAA;sBACEwG,SAAS,EAAC,oBAAoB;sBAC9B0E,KAAK,EAAE;wBAAEyB,QAAQ,EAAE;sBAAG,CAAE;sBAAAlG,QAAA,EACzB;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNjH,OAAA;sBAAKwG,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBhH,KAAK,CAACmN,OAAO,CAACjK,UAAU,CAACwB,YAAY,EAAE,CAAC;oBAAC;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENjH,OAAA;gBAAKwG,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BzG,OAAA;kBAAKwG,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDzG,OAAA;oBAAAyG,QAAA,EAAM;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClCjH,OAAA;oBAAMwG,SAAS,EAAC,SAAS;oBAAAC,QAAA,GAAEhC,YAAY,EAAC,QAAM;kBAAA;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAAC,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNjH,OAAA;kBAAKwG,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDzG,OAAA;oBAAAyG,QAAA,EAAM;kBAAuB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpCjH,OAAA;oBAAMwG,SAAS,EAAC,SAAS;oBAAAC,QAAA,GACtB9D,UAAU,CAACkK,MAAM,EAAC,YAAU,EAAClK,UAAU,CAACmK,SAAS,EAAE,GAAG,EAAC,WAE1D;kBAAA;oBAAAhG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENjH,OAAA;gBACEwG,SAAS,EAAC,sBAAsB;gBAChC0E,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPjH,OAAA;gBAAKwG,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCzG,OAAA;kBAAIwG,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAY;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAErCrG,aAAa,CAAC+F,GAAG,CAAC,CAAC;kBAAE9B,IAAI;kBAAEC;gBAAO,CAAC,kBAClC9E,OAAA;kBAEEwG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElEzG,OAAA;oBAAAyG,QAAA,GACG3B,MAAM,EAAC,KAAG,EAACD,IAAI,CAACgD,IAAI,EAAC,IAAE,EAACpD,YAAY,EAAC,SACxC;kBAAA;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPjH,OAAA;oBAAMwG,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACtBhH,KAAK,CAACqL,cAAc,CACnBjG,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YACxB;kBAAC;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAVFpC,IAAI,CAACqD,GAAG;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN,CAAC,eAEFjH,OAAA;kBAAKwG,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBzG,OAAA;oBACEwG,SAAS,EAAC,gCAAgC;oBAC1C0E,KAAK,EAAE;sBAAE6B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAE5G,sBAAuB;oBAAAK,QAAA,EACjC;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLnG,gBAAgB,CAACqC,MAAM,GAAG,CAAC,iBAC1BnD,OAAA;gBAAKwG,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCzG,OAAA;kBAAIwG,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAE1CnG,gBAAgB,CAAC6F,GAAG,CAAE1B,OAAO,IAAK;kBACjC,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;kBACjD,MAAMC,eAAe,GACnBF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC/B,MAAM;kBACzC,MAAM8J,YAAY,GAAGhI,OAAO,CAACF,KAAK,GAAGI,eAAe;kBAEpD,oBACEnF,OAAA;oBAEEwG,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,gBAElEzG,OAAA;sBAAAyG,QAAA,GACGxB,OAAO,CAACG,QAAQ,EAAC,KAAG,EAACH,OAAO,CAAC4C,IAAI,EAAC,IACnC,EAAC3C,aAAa,CAAC/B,MAAM,EAAC,SACxB;oBAAA;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPjH,OAAA;sBAAMwG,SAAS,EAAC,SAAS;sBAAAC,QAAA,EACtBhH,KAAK,CAACqL,cAAc,CAACmC,YAAY;oBAAC;sBAAAnG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA,GATFhC,OAAO,CAACiD,GAAG;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUb,CAAC;gBAEV,CAAC,CAAC,eAEFjH,OAAA;kBAAKwG,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBzG,OAAA;oBACEwG,SAAS,EAAC,gCAAgC;oBAC1C0E,KAAK,EAAE;sBAAE6B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAEA,CAAA,KAAM;sBACb9L,QAAQ,CAAC;wBACPmC,IAAI,EAAEzD,aAAa,CAAC0D,mBAAmB;wBACvCC,OAAO,EAAE;0BACP3C,aAAa,EAAEA,aAAa;0BAC5BE,gBAAgB,EAAEA,gBAAgB;0BAClCE,WAAW,EAAEA;wBACf;sBACF,CAAC,CAAC;sBACFC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACd,CAAE;oBAAAwF,QAAA,EACH;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDjH,OAAA;gBACEwG,SAAS,EAAC,sBAAsB;gBAChC0E,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPjH,OAAA;gBAAKwG,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GAEpClF,iBAAiB,GAAG,CAAC,gBACpBvB,OAAA,CAAC3B,IAAI;kBACHmI,SAAS,EAAC,wBAAwB;kBAClC0E,KAAK,EAAE;oBACLc,eAAe,EAAE,wBAAwB;oBACzCkB,WAAW,EAAE,SAAS;oBACtBC,MAAM,EAAE;kBACV,CAAE;kBAAA1G,QAAA,eAEFzG,OAAA,CAAC3B,IAAI,CAAC+O,IAAI;oBAAC5G,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACzBzG,OAAA;sBAAKwG,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChEzG,OAAA;wBAAAyG,QAAA,gBACEzG,OAAA;0BAAKwG,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxCzG,OAAA,CAACrB,KAAK;4BAAC6H,SAAS,EAAC;0BAAmB;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvCjH,OAAA;4BAAMwG,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAEpF;0BAAa;4BAAAyF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1D,CAAC,eACNjH,OAAA;0BAAOwG,SAAS,EAAC,cAAc;0BAAAC,QAAA,GAAC,OACzB,EAAChH,KAAK,CAACqL,cAAc,CAACvJ,iBAAiB,CAAC;wBAAA;0BAAAuF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNjH,OAAA,CAACzB,MAAM;wBACL8O,OAAO,EAAC,gBAAgB;wBACxBC,IAAI,EAAC,IAAI;wBACTN,OAAO,EAAEA,CAAA,KAAM1F,6BAA6B,CAAC;0BAC3C5D,IAAI,EAAE,EAAE;0BACRC,QAAQ,EAAE,CAAC;0BACX6D,OAAO,EAAE,EAAE;0BACX7F,WAAW,EAAE;wBACf,CAAC,CAAE;wBACH6E,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,gBAErCzG,OAAA,CAACpB,OAAO;0BAAC4H,SAAS,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAE9B;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,gBAEPjH,OAAA;kBAAKwG,SAAS,EAAC,uBAAuB;kBAAC0E,KAAK,EAAE;oBAC5CiC,MAAM,EAAE,kCAAkC;oBAC1ClB,YAAY,EAAE,KAAK;oBACnBD,eAAe,EAAE;kBACnB,CAAE;kBAAAvF,QAAA,gBACAzG,OAAA,CAACrB,KAAK;oBAAC6H,SAAS,EAAC,iBAAiB;oBAAC8G,IAAI,EAAE;kBAAG;oBAAAxG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/CjH,OAAA;oBAAKwG,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CACN,eAGDjH,OAAA,CAACzB,MAAM;kBACL8O,OAAO,EAAC,eAAe;kBACvB7G,SAAS,EAAC,wDAAwD;kBAClEwG,OAAO,EAAEA,CAAA,KAAM3F,qBAAqB,CAAC,IAAI,CAAE;kBAC3C6D,KAAK,EAAE;oBACLqC,WAAW,EAAE,QAAQ;oBACrBC,WAAW,EAAE,KAAK;oBAClBtB,OAAO,EAAE;kBACX,CAAE;kBAAAzF,QAAA,gBAEFzG,OAAA,CAACrB,KAAK;oBAAC6H,SAAS,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzB1F,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,kBAAkB;gBAAA;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,EAGRxF,gBAAgB,iBACfzB,OAAA;kBACEwG,SAAS,EAAE,0BACTjF,iBAAiB,GAAG,CAAC,GAAG,cAAc,GAAG,aAAa,EACrD;kBAAAkF,QAAA,EAEFhF;gBAAgB;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENjH,OAAA;gBAAKwG,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BzG,OAAA;kBAAKwG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChEzG,OAAA;oBAAIwG,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,SACxB,EAAChH,KAAK,CAACqL,cAAc,CAACxF,UAAU,CAAC;kBAAA;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNjH,OAAA;kBAAKwG,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNjH,OAAA,CAAC5B,GAAG;YAAC0N,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAtF,QAAA,eAChBzG,OAAA,CAAC3B,IAAI;cACHmI,SAAS,EAAC,WAAW;cACrB0E,KAAK,EAAE;gBACLc,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfuB,KAAK,EAAE;cACT,CAAE;cAAAhH,QAAA,gBAEFzG,OAAA;gBAAIwG,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAsB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEhDjH,OAAA,CAAC1B,IAAI;gBAAAmI,QAAA,gBACHzG,OAAA,CAAC1B,IAAI,CAACoP,KAAK;kBAAClH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BzG,OAAA,CAAC1B,IAAI,CAACqP,KAAK;oBAAAlH,QAAA,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClCjH,OAAA,CAAC1B,IAAI,CAACsP,OAAO;oBACXvK,IAAI,EAAC,MAAM;oBACXwK,KAAK,EAAEtN,IAAI,CAACsH,IAAK;oBACjBrB,SAAS,EAAC,2BAA2B;oBACrC0E,KAAK,EAAE;sBACLiC,MAAM,EAAE,iCAAiC;sBACzClB,YAAY,EAAE;oBAChB;kBAAE;oBAAAnF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbjH,OAAA,CAAC1B,IAAI,CAACoP,KAAK;kBAAClH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BzG,OAAA,CAAC1B,IAAI,CAACqP,KAAK;oBAAAlH,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BjH,OAAA,CAAC1B,IAAI,CAACsP,OAAO;oBACXvK,IAAI,EAAC,OAAO;oBACZwK,KAAK,EAAEtN,IAAI,CAACuN,KAAM;oBAClBC,WAAW,EAAC,sBAAsB;oBAClCvH,SAAS,EAAC,2BAA2B;oBACrC0E,KAAK,EAAE;sBACLiC,MAAM,EAAE,iCAAiC;sBACzClB,YAAY,EAAE;oBAChB;kBAAE;oBAAAnF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbjH,OAAA,CAAC1B,IAAI,CAACoP,KAAK;kBAAClH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BzG,OAAA,CAAC1B,IAAI,CAACqP,KAAK;oBAAAlH,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BjH,OAAA,CAAC1B,IAAI,CAACsP,OAAO;oBACXvK,IAAI,EAAC,KAAK;oBACVwK,KAAK,EAAEtN,IAAI,CAACyN,WAAY;oBACxBD,WAAW,EAAC,YAAY;oBACxBvH,SAAS,EAAC,2BAA2B;oBACrC0E,KAAK,EAAE;sBACLiC,MAAM,EAAE,iCAAiC;sBACzClB,YAAY,EAAE;oBAChB;kBAAE;oBAAAnF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbjH,OAAA,CAAC1B,IAAI,CAACoP,KAAK;kBAAClH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BzG,OAAA,CAAC1B,IAAI,CAACqP,KAAK;oBAAAlH,QAAA,EAAC;kBAAwB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjDjH,OAAA;oBAAAyG,QAAA,gBACEzG,OAAA,CAAC1B,IAAI,CAAC2P,KAAK;sBACT5K,IAAI,EAAC,OAAO;sBACZI,EAAE,EAAC,WAAW;sBACdyK,KAAK,EAAC,oBAAoB;sBAC1BrG,IAAI,EAAC,YAAY;sBACjBsG,OAAO,EAAEhN,UAAU,KAAK,WAAY;sBACpCiN,QAAQ,EAAEA,CAAA,KAAMhN,aAAa,CAAC,WAAW,CAAE;sBAC3CoF,SAAS,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACFjH,OAAA,CAAC1B,IAAI,CAAC2P,KAAK;sBACT5K,IAAI,EAAC,OAAO;sBACZI,EAAE,EAAC,aAAa;sBAChByK,KAAK,EAAC,8BAA8B;sBACpCrG,IAAI,EAAC,YAAY;sBACjBsG,OAAO,EAAEhN,UAAU,KAAK,aAAc;sBACtCiN,QAAQ,EAAEA,CAAA,KAAMhN,aAAa,CAAC,aAAa;oBAAE;sBAAA0F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbjH,OAAA;kBAAKwG,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BzG,OAAA,CAACzB,MAAM;oBACLiI,SAAS,EAAC,WAAW;oBACrB0E,KAAK,EAAE;sBACLe,YAAY,EAAE,MAAM;sBACpBD,eAAe,EAAE,OAAO;sBACxByB,KAAK,EAAE,SAAS;sBAChBN,MAAM,EAAE,MAAM;sBACdkB,UAAU,EAAE;oBACd,CAAE;oBACFrB,OAAO,EAAEnC,oBAAqB;oBAAApE,QAAA,EAC/B;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAETjH,OAAA,CAACd,iBAAiB;oBAChBoP,IAAI,EAAEpH,eAAgB;oBACtBqH,MAAM,EAAEA,CAAA,KAAMpH,kBAAkB,CAAC,KAAK,CAAE;oBACxCqH,SAAS,EAAE5D,YAAa;oBACxB6D,KAAK,EAAC,oBAAoB;oBAC1BjH,OAAO,EAAC,wDAAwD;oBAChEkH,iBAAiB,EAAC,QAAQ;oBAC1BrL,IAAI,EAAC;kBAAQ;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZjH,OAAA;QAAAyG,QAAA,eACEzG,OAAA,CAACL,OAAO;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNjH,OAAA,CAACjB,MAAM;MAAA+H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVjH,OAAA,CAACb,cAAc;MACbmP,IAAI,EAAElH,kBAAmB;MACzBmH,MAAM,EAAEA,CAAA,KAAMlH,qBAAqB,CAAC,KAAK,CAAE;MAC3ChC,UAAU,EAAEA,UAAW;MACvBsJ,gBAAgB,EAAErH,6BAA8B;MAChDsH,kBAAkB,EAAEjN;IAAY;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEFjH,OAAA,CAACF,gBAAgB;MACfwO,IAAI,EAAEjO,sBAAuB;MAC7BwO,OAAO,EAAEA,CAAA,KAAM;QACbvO,yBAAyB,CAAC,KAAK,CAAC;MAClC;IAAE;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFjH,OAAA,CAACZ,0BAA0B;MACzByC,eAAe,EAAEA,eAAgB;MACjCyM,IAAI,EAAEvM,mBAAoB;MAC1B8M,OAAO,EAAEzM,oBAAqB;MAC9B0M,iBAAiB,EAAEpH,qBAAsB;MACzCqH,kBAAkB,EAAEpH,+BAAgC;MACpDqH,OAAO,EAAElH;IAAsB;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC/G,EAAA,CA93BID,gBAAgB;EAAA,QAGPV,cAAc,EACDA,cAAc,EAGbA,cAAc,EAGPA,cAAc,EAGnBA,cAAc,EAG1BN,WAAW,EACXO,cAAc,EAqB3BH,sBAAsB;AAAA;AAAA4P,EAAA,GAtCtBhP,gBAAgB;AAg4BtB,eAAeA,gBAAgB;AAAC,IAAAgP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}