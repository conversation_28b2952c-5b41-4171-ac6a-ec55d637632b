{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "@stripe/stripe-js": "^7.3.1", "axios": "^1.10.0", "backend": "file:", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cloudinary": "^1.21.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "express-async-handler": "^1.2.0", "express-fileupload": "^1.5.1", "firebase-admin": "^13.4.0", "fs": "^0.0.1-security", "http": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongodb": "^6.17.0", "mongoose": "^8.10.0", "mongoose-sequence": "^6.0.1", "morgan": "^1.10.0", "multer": "^2.0.1", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "nodemon": "^3.1.9", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stripe": "^18.2.1", "validator": "^13.15.0"}}