{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\components\\\\PromotionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge, Spinner } from \"react-bootstrap\";\nimport { FaTag, FaTimes, FaCheck } from \"react-icons/fa\";\nimport axios from \"axios\";\nimport Utils from \"../../../../utils/Utils\";\nimport \"../../../../css/PromotionModal.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PromotionModal = ({\n  show,\n  onHide,\n  totalPrice,\n  onApplyPromotion,\n  currentPromotionId\n}) => {\n  _s();\n  const [promotions, setPromotions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedPromotion, setSelectedPromotion] = useState(null);\n  const [applying, setApplying] = useState(false);\n  useEffect(() => {\n    if (show && totalPrice > 0) {\n      console.log(\"PromotionModal opened with totalPrice:\", totalPrice);\n      fetchPromotions();\n    }\n  }, [show, totalPrice]);\n  const fetchPromotions = async () => {\n    setLoading(true);\n    try {\n      // Thử fetch từ API trước\n      let promotionList = [];\n      try {\n        console.log(\"Fetching promotions from API...\");\n        const response = await axios.get(\"http://localhost:5000/api/promotions\");\n        console.log(\"API Response:\", response.data);\n\n        // Thử nhiều cách để lấy data từ response\n        promotionList = response.data.promotions || response.data.data || response.data || [];\n        console.log(\"Promotion list from API:\", promotionList);\n\n        // Nếu API trả về nhưng không có data, sử dụng mock\n        if (!Array.isArray(promotionList) || promotionList.length === 0) {\n          console.log(\"API returned empty or invalid data, using mock data\");\n          throw new Error(\"No promotions from API\");\n        }\n      } catch (apiError) {\n        // Nếu API không có, sử dụng mock data\n        console.log(\"API Error:\", apiError.message, \"- Using mock promotion data\");\n        promotionList = [{\n          _id: \"1\",\n          code: \"SAVE20\",\n          description: \"Save $20 on orders over $100\",\n          discountType: \"fixed\",\n          discountValue: 20,\n          minOrderAmount: 100,\n          maxDiscount: 20,\n          expiryDate: \"2025-12-31\",\n          isActive: true\n        }, {\n          _id: \"2\",\n          code: \"PERCENT10\",\n          description: \"10% off on all bookings\",\n          discountType: \"percentage\",\n          discountValue: 10,\n          minOrderAmount: 50,\n          maxDiscount: 50,\n          expiryDate: \"2025-12-31\",\n          isActive: true\n        }, {\n          _id: \"3\",\n          code: \"NEWUSER50\",\n          description: \"Special discount for new users\",\n          discountType: \"fixed\",\n          discountValue: 50,\n          minOrderAmount: 200,\n          maxDiscount: 50,\n          expiryDate: \"2025-06-30\",\n          isActive: true\n        }, {\n          _id: \"4\",\n          code: \"EXPIRED\",\n          description: \"This promotion has expired\",\n          discountType: \"fixed\",\n          discountValue: 30,\n          minOrderAmount: 80,\n          maxDiscount: 30,\n          expiryDate: \"2024-12-31\",\n          isActive: false\n        }];\n      }\n      console.log(\"Total price for validation:\", totalPrice);\n      console.log(\"Processing\", promotionList.length, \"promotions\");\n\n      // Validate từng promotion với totalPrice hiện tại\n      const validatedPromotions = await Promise.all(promotionList.map(async (promo, index) => {\n        console.log(`Validating promotion ${index + 1}:`, promo.code);\n        try {\n          const validateRes = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\n            code: promo.code,\n            orderAmount: totalPrice\n          });\n          console.log(`API validation result for ${promo.code}:`, validateRes.data);\n          return {\n            ...promo,\n            isValid: validateRes.data.valid,\n            discount: validateRes.data.discount || 0,\n            message: validateRes.data.message || \"\"\n          };\n        } catch (err) {\n          console.log(`API validation failed for ${promo.code}, using mock validation`);\n\n          // Mock validation logic nếu API không có\n          const now = new Date();\n          const startDate = new Date(promo.startDate);\n          const endDate = new Date(promo.endDate);\n          const isInTimeRange = now >= startDate && now <= endDate;\n          const meetsMinOrder = totalPrice >= (promo.minOrderAmount || 0);\n          const isActive = promo.isActive !== false;\n          const isValid = isInTimeRange && meetsMinOrder && isActive;\n          let discount = 0;\n          let message = \"\";\n          if (isValid) {\n            if (promo.discountType === \"PERCENTAGE\" || promo.discountType === \"percentage\") {\n              discount = Math.min(totalPrice * promo.discountValue / 100, promo.maxDiscountAmount || promo.maxDiscount || Infinity);\n            } else {\n              discount = Math.min(promo.discountValue, promo.maxDiscountAmount || promo.maxDiscount || Infinity);\n            }\n            message = `Save ${discount}`;\n          } else {\n            if (!isInTimeRange) {\n              if (now < startDate) message = \"Promotion has not started yet\";else if (now > endDate) message = \"Promotion has expired\";else message = \"Promotion is not available\";\n            } else if (!meetsMinOrder) message = `Minimum order $${promo.minOrderAmount} required`;else if (!isActive) message = \"Promotion is not active\";else message = \"Not applicable\";\n          }\n          console.log(`Mock validation for ${promo.code}:`, {\n            isValid,\n            discount,\n            message\n          });\n          return {\n            ...promo,\n            isValid,\n            discount,\n            message\n          };\n        }\n      }));\n      console.log(\"Final validated promotions:\", validatedPromotions);\n\n      // Chỉ hiển thị promotion có thể dùng được hoặc sắp có thể dùng (chưa bắt đầu)\n      // Ẩn những promotion đã hết hạn, không đủ điều kiện, hoặc không active\n      const displayPromotions = validatedPromotions.filter(promo => {\n        const now = new Date();\n        const startDate = new Date(promo.startDate || promo.expiryDate || '2025-01-01');\n        const endDate = new Date(promo.endDate || promo.expiryDate || '2025-12-31');\n\n        // Chỉ hiển thị nếu: promotion chưa hết hạn và đang active\n        const notExpired = now <= endDate;\n        const isActive = promo.isActive !== false;\n        return notExpired && isActive;\n      });\n      console.log(\"Display promotions:\", displayPromotions.length, \"of\", validatedPromotions.length);\n      console.log(\"Available now:\", displayPromotions.filter(p => p.isValid).length);\n      console.log(\"Starting soon:\", displayPromotions.filter(p => {\n        var _p$message;\n        return !p.isValid && ((_p$message = p.message) === null || _p$message === void 0 ? void 0 : _p$message.includes(\"not started\"));\n      }).length);\n\n      // Sắp xếp promotions: Available trước, starting soon sau, và theo discount giảm dần\n      const sortedPromotions = displayPromotions.sort((a, b) => {\n        // Available promotions lên trước\n        if (a.isValid && !b.isValid) return -1;\n        if (!a.isValid && b.isValid) return 1;\n\n        // Trong cùng loại, sắp xếp theo discount giảm dần\n        return b.discount - a.discount;\n      });\n      setPromotions(sortedPromotions);\n    } catch (error) {\n      console.error(\"Error fetching promotions:\", error);\n      setPromotions([]);\n    }\n    setLoading(false);\n  };\n  const handleApplyPromotion = async promotion => {\n    if (!promotion.isValid) {\n      console.log(\"Promotion is not valid:\", promotion);\n      return;\n    }\n    console.log(\"Applying promotion:\", promotion);\n    setApplying(true);\n    try {\n      // Thử apply qua API trước\n      try {\n        console.log(\"Calling API with:\", {\n          code: promotion.code,\n          orderAmount: totalPrice\n        });\n        const response = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\n          code: promotion.code,\n          orderAmount: totalPrice\n        });\n        console.log(\"API Response:\", response.data);\n        if (response.data.valid) {\n          const promotionData = {\n            code: promotion.code,\n            discount: response.data.discount,\n            message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,\n            promotionId: response.data.promotionId\n          };\n          console.log(\"Calling onApplyPromotion with:\", promotionData);\n          onApplyPromotion(promotionData);\n          onHide();\n        } else {\n          console.log(\"API returned invalid promotion:\", response.data);\n        }\n      } catch (apiError) {\n        // Nếu API không có, sử dụng mock logic\n        console.log(\"API Error, using mock promotion application:\", apiError.message);\n        const promotionData = {\n          code: promotion.code,\n          discount: promotion.discount,\n          message: `Promotion applied: -${Utils.formatCurrency(promotion.discount)}`,\n          promotionId: promotion._id\n        };\n        console.log(\"Calling onApplyPromotion with mock data:\", promotionData);\n        onApplyPromotion(promotionData);\n        onHide();\n      }\n    } catch (error) {\n      console.error(\"Error applying promotion:\", error);\n    }\n    setApplying(false);\n  };\n  const handleRemovePromotion = () => {\n    onApplyPromotion({\n      code: \"\",\n      discount: 0,\n      message: \"\",\n      promotionId: null\n    });\n    onHide();\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    centered: true,\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        borderColor: \"rgba(255,255,255,0.2)\",\n        color: \"white\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaTag, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), \"Select Promotion\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        color: \"white\",\n        maxHeight: \"60vh\",\n        overflowY: \"auto\"\n      },\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          variant: \"light\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2\",\n          children: \"Loading promotions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [currentPromotionId && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-3\",\n            children: \"Current Applied Promotion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            className: \"promotion-card current-promotion\",\n            style: {\n              backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n              borderColor: \"#28a745\",\n              border: \"2px solid #28a745\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"py-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                    className: \"text-success me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success fw-bold\",\n                    children: \"Applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-danger\",\n                  size: \"sm\",\n                  onClick: handleRemovePromotion,\n                  disabled: applying,\n                  children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 25\n                  }, this), \"Remove\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-3\",\n          children: [\"Available Promotions\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"small ms-2\",\n            style: {\n              color: 'rgba(255,255,255,0.6)'\n            },\n            children: [\"(\", promotions.filter(p => p.isValid).length, \" ready, \", promotions.filter(p => !p.isValid).length, \" starting soon)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this), promotions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          style: {\n            color: 'rgba(255,255,255,0.7)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n            size: 48,\n            className: \"mb-3\",\n            style: {\n              opacity: 0.5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"No promotions available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [promotions.filter(p => p.isValid).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row g-3 mb-4\",\n            children: promotions.filter(p => p.isValid).map(promotion => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: `promotion-card ${currentPromotionId === promotion._id ? 'current' : ''}`,\n                style: {\n                  backgroundColor: currentPromotionId === promotion._id ? \"rgba(40, 167, 69, 0.2)\" : \"rgba(255,255,255,0.1)\",\n                  borderColor: currentPromotionId === promotion._id ? \"#28a745\" : \"rgba(255,255,255,0.3)\",\n                  cursor: \"pointer\",\n                  transition: \"all 0.3s ease\"\n                },\n                onClick: () => {\n                  console.log(\"Promotion card clicked:\", promotion);\n                  handleApplyPromotion(promotion);\n                },\n                children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"py-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-start\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-grow-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                          className: \"me-2 text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 364,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0 fw-bold\",\n                          children: promotion.code\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 365,\n                          columnNumber: 35\n                        }, this), currentPromotionId === promotion._id && /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"success\",\n                          className: \"ms-2\",\n                          children: \"Applied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 367,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"success\",\n                          className: \"ms-2\",\n                          children: \"Available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 369,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mb-2 small\",\n                        style: {\n                          color: 'rgba(255,255,255,0.7)'\n                        },\n                        children: promotion.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 372,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex justify-content-between align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-success fw-bold\",\n                            children: [\"Save \", Utils.formatCurrency(promotion.discount)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 376,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 375,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-end\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"small\",\n                            children: [promotion.minOrderAmount && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-success\",\n                              children: [\"Min: \", Utils.formatCurrency(promotion.minOrderAmount), \" \\u2713\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 384,\n                              columnNumber: 41\n                            }, this), promotion.maxDiscount && /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                color: 'rgba(255,255,255,0.6)'\n                              },\n                              children: [\"Max: \", Utils.formatCurrency(promotion.maxDiscount)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 389,\n                              columnNumber: 41\n                            }, this), promotion.expiryDate && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-success\",\n                              children: [\"Expires: \", new Date(promotion.expiryDate).toLocaleDateString(), \" \\u2713\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 394,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 382,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 381,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 374,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 25\n              }, this)\n            }, promotion._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 23\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 19\n          }, this), promotions.filter(p => !p.isValid).length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-3 text-warning\",\n              children: [\"Starting Soon (\", promotions.filter(p => !p.isValid).length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row g-3\",\n              children: promotions.filter(p => !p.isValid).map(promotion => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-12\",\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-card disabled\",\n                  style: {\n                    backgroundColor: \"rgba(255, 193, 7, 0.1)\",\n                    borderColor: \"rgba(255, 193, 7, 0.5)\",\n                    cursor: \"not-allowed\",\n                    opacity: 0.8,\n                    transition: \"all 0.3s ease\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-start\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-grow-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"me-2 text-warning\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 433,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"mb-0 fw-bold\",\n                            children: promotion.code\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 434,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: \"warning\",\n                            className: \"ms-2\",\n                            style: {\n                              color: 'white'\n                            },\n                            children: \"Starting Soon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 435,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 432,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"mb-2 small\",\n                          style: {\n                            color: 'rgba(255,255,255,0.7)'\n                          },\n                          children: promotion.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 438,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex justify-content-between align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-warning small fw-bold\",\n                              children: promotion.message\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 442,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 441,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-end\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"small\",\n                              children: [promotion.minOrderAmount && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `${totalPrice >= promotion.minOrderAmount ? 'text-success' : 'text-warning'}`,\n                                children: [\"Min: \", Utils.formatCurrency(promotion.minOrderAmount), totalPrice >= promotion.minOrderAmount ? ' ✓' : ' ✗']\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 450,\n                                columnNumber: 43\n                              }, this), promotion.maxDiscount && /*#__PURE__*/_jsxDEV(\"div\", {\n                                style: {\n                                  color: 'rgba(255,255,255,0.6)'\n                                },\n                                children: [\"Max: \", Utils.formatCurrency(promotion.maxDiscount)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 456,\n                                columnNumber: 43\n                              }, this), (promotion.startDate || promotion.expiryDate) && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-warning\",\n                                children: [\"Starts: \", new Date(promotion.startDate || promotion.expiryDate).toLocaleDateString()]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 461,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 448,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 447,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 440,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 431,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 27\n                }, this)\n              }, promotion._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        borderColor: \"rgba(255,255,255,0.2)\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-light\",\n        onClick: onHide,\n        disabled: applying,\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_s(PromotionModal, \"gnDJSDg1qNxH0g+WyWjSxYt9R8k=\");\n_c = PromotionModal;\nexport default PromotionModal;\nvar _c;\n$RefreshReg$(_c, \"PromotionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Modal", "<PERSON><PERSON>", "Card", "Badge", "Spinner", "FaTag", "FaTimes", "FaCheck", "axios", "Utils", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PromotionModal", "show", "onHide", "totalPrice", "onApplyPromotion", "currentPromotionId", "_s", "promotions", "setPromotions", "loading", "setLoading", "selectedPromotion", "setSelectedPromotion", "applying", "setApplying", "console", "log", "fetchPromotions", "promotionList", "response", "get", "data", "Array", "isArray", "length", "Error", "apiError", "message", "_id", "code", "description", "discountType", "discountValue", "minOrderAmount", "maxDiscount", "expiryDate", "isActive", "validatedPromotions", "Promise", "all", "map", "promo", "index", "validateRes", "post", "orderAmount", "<PERSON><PERSON><PERSON><PERSON>", "valid", "discount", "err", "now", "Date", "startDate", "endDate", "isInTimeRange", "meetsMinOrder", "Math", "min", "maxDiscountAmount", "Infinity", "displayPromotions", "filter", "notExpired", "p", "_p$message", "includes", "sortedPromotions", "sort", "a", "b", "error", "handleApplyPromotion", "promotion", "promotionData", "formatCurrency", "promotionId", "handleRemovePromotion", "size", "centered", "children", "Header", "closeButton", "style", "backgroundColor", "borderColor", "color", "Title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "maxHeight", "overflowY", "animation", "variant", "border", "onClick", "disabled", "opacity", "cursor", "transition", "bg", "toLocaleDateString", "Footer", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Customer/src/pages/customer/home/<USER>/PromotionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner } from \"react-bootstrap\";\r\nimport { FaTag, FaTimes, FaCheck } from \"react-icons/fa\";\r\nimport axios from \"axios\";\r\nimport Utils from \"../../../../utils/Utils\";\r\nimport \"../../../../css/PromotionModal.css\";\r\n\r\nconst PromotionModal = ({ show, onHide, totalPrice, onApplyPromotion, currentPromotionId }) => {\r\n  const [promotions, setPromotions] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [selectedPromotion, setSelectedPromotion] = useState(null);\r\n  const [applying, setApplying] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (show && totalPrice > 0) {\r\n      console.log(\"PromotionModal opened with totalPrice:\", totalPrice);\r\n      fetchPromotions();\r\n    }\r\n  }, [show, totalPrice]);\r\n\r\n  const fetchPromotions = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // Thử fetch từ API trước\r\n      let promotionList = [];\r\n      try {\r\n        console.log(\"Fetching promotions from API...\");\r\n        const response = await axios.get(\"http://localhost:5000/api/promotions\");\r\n        console.log(\"API Response:\", response.data);\r\n        \r\n        // Thử nhiều cách để lấy data từ response\r\n        promotionList = response.data.promotions || response.data.data || response.data || [];\r\n        console.log(\"Promotion list from API:\", promotionList);\r\n        \r\n        // Nếu API trả về nhưng không có data, sử dụng mock\r\n        if (!Array.isArray(promotionList) || promotionList.length === 0) {\r\n          console.log(\"API returned empty or invalid data, using mock data\");\r\n          throw new Error(\"No promotions from API\");\r\n        }\r\n      } catch (apiError) {\r\n        // Nếu API không có, sử dụng mock data\r\n        console.log(\"API Error:\", apiError.message, \"- Using mock promotion data\");\r\n        promotionList = [\r\n          {\r\n            _id: \"1\",\r\n            code: \"SAVE20\",\r\n            description: \"Save $20 on orders over $100\",\r\n            discountType: \"fixed\",\r\n            discountValue: 20,\r\n            minOrderAmount: 100,\r\n            maxDiscount: 20,\r\n            expiryDate: \"2025-12-31\",\r\n            isActive: true\r\n          },\r\n          {\r\n            _id: \"2\", \r\n            code: \"PERCENT10\",\r\n            description: \"10% off on all bookings\",\r\n            discountType: \"percentage\",\r\n            discountValue: 10,\r\n            minOrderAmount: 50,\r\n            maxDiscount: 50,\r\n            expiryDate: \"2025-12-31\",\r\n            isActive: true\r\n          },\r\n          {\r\n            _id: \"3\",\r\n            code: \"NEWUSER50\",\r\n            description: \"Special discount for new users\",\r\n            discountType: \"fixed\", \r\n            discountValue: 50,\r\n            minOrderAmount: 200,\r\n            maxDiscount: 50,\r\n            expiryDate: \"2025-06-30\",\r\n            isActive: true\r\n          },\r\n          {\r\n            _id: \"4\",\r\n            code: \"EXPIRED\",\r\n            description: \"This promotion has expired\",\r\n            discountType: \"fixed\",\r\n            discountValue: 30,\r\n            minOrderAmount: 80,\r\n            maxDiscount: 30,\r\n            expiryDate: \"2024-12-31\",\r\n            isActive: false\r\n          }\r\n        ];\r\n      }\r\n      \r\n      console.log(\"Total price for validation:\", totalPrice);\r\n      console.log(\"Processing\", promotionList.length, \"promotions\");\r\n      \r\n      // Validate từng promotion với totalPrice hiện tại\r\n      const validatedPromotions = await Promise.all(\r\n        promotionList.map(async (promo, index) => {\r\n          console.log(`Validating promotion ${index + 1}:`, promo.code);\r\n          \r\n          try {\r\n            const validateRes = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\r\n              code: promo.code,\r\n              orderAmount: totalPrice,\r\n            });\r\n            console.log(`API validation result for ${promo.code}:`, validateRes.data);\r\n            \r\n            return {\r\n              ...promo,\r\n              isValid: validateRes.data.valid,\r\n              discount: validateRes.data.discount || 0,\r\n              message: validateRes.data.message || \"\",\r\n            };\r\n          } catch (err) {\r\n            console.log(`API validation failed for ${promo.code}, using mock validation`);\r\n            \r\n            // Mock validation logic nếu API không có\r\n            const now = new Date();\r\n            const startDate = new Date(promo.startDate);\r\n            const endDate = new Date(promo.endDate);\r\n            \r\n            const isInTimeRange = now >= startDate && now <= endDate;\r\n            const meetsMinOrder = totalPrice >= (promo.minOrderAmount || 0);\r\n            const isActive = promo.isActive !== false;\r\n            \r\n            const isValid = isInTimeRange && meetsMinOrder && isActive;\r\n            \r\n            let discount = 0;\r\n            let message = \"\";\r\n            \r\n            if (isValid) {\r\n              if (promo.discountType === \"PERCENTAGE\" || promo.discountType === \"percentage\") {\r\n                discount = Math.min((totalPrice * promo.discountValue) / 100, promo.maxDiscountAmount || promo.maxDiscount || Infinity);\r\n              } else {\r\n                discount = Math.min(promo.discountValue, promo.maxDiscountAmount || promo.maxDiscount || Infinity);\r\n              }\r\n              message = `Save ${discount}`;\r\n            } else {\r\n              if (!isInTimeRange) {\r\n                if (now < startDate) message = \"Promotion has not started yet\";\r\n                else if (now > endDate) message = \"Promotion has expired\";\r\n                else message = \"Promotion is not available\";\r\n              } else if (!meetsMinOrder) message = `Minimum order $${promo.minOrderAmount} required`;\r\n              else if (!isActive) message = \"Promotion is not active\";\r\n              else message = \"Not applicable\";\r\n            }\r\n            \r\n            console.log(`Mock validation for ${promo.code}:`, { isValid, discount, message });\r\n            \r\n            return {\r\n              ...promo,\r\n              isValid,\r\n              discount,\r\n              message,\r\n            };\r\n          }\r\n        })\r\n      );\r\n      \r\n      console.log(\"Final validated promotions:\", validatedPromotions);\r\n      \r\n      // Chỉ hiển thị promotion có thể dùng được hoặc sắp có thể dùng (chưa bắt đầu)\r\n      // Ẩn những promotion đã hết hạn, không đủ điều kiện, hoặc không active\r\n      const displayPromotions = validatedPromotions.filter(promo => {\r\n        const now = new Date();\r\n        const startDate = new Date(promo.startDate || promo.expiryDate || '2025-01-01');\r\n        const endDate = new Date(promo.endDate || promo.expiryDate || '2025-12-31');\r\n        \r\n        // Chỉ hiển thị nếu: promotion chưa hết hạn và đang active\r\n        const notExpired = now <= endDate;\r\n        const isActive = promo.isActive !== false;\r\n        \r\n        return notExpired && isActive;\r\n      });\r\n      \r\n      console.log(\"Display promotions:\", displayPromotions.length, \"of\", validatedPromotions.length);\r\n      console.log(\"Available now:\", displayPromotions.filter(p => p.isValid).length);\r\n      console.log(\"Starting soon:\", displayPromotions.filter(p => !p.isValid && p.message?.includes(\"not started\")).length);\r\n      \r\n      // Sắp xếp promotions: Available trước, starting soon sau, và theo discount giảm dần\r\n      const sortedPromotions = displayPromotions.sort((a, b) => {\r\n        // Available promotions lên trước\r\n        if (a.isValid && !b.isValid) return -1;\r\n        if (!a.isValid && b.isValid) return 1;\r\n        \r\n        // Trong cùng loại, sắp xếp theo discount giảm dần\r\n        return b.discount - a.discount;\r\n      });\r\n      \r\n      setPromotions(sortedPromotions);\r\n    } catch (error) {\r\n      console.error(\"Error fetching promotions:\", error);\r\n      setPromotions([]);\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  const handleApplyPromotion = async (promotion) => {\r\n    if (!promotion.isValid) {\r\n      console.log(\"Promotion is not valid:\", promotion);\r\n      return;\r\n    }\r\n\r\n    console.log(\"Applying promotion:\", promotion);\r\n    setApplying(true);\r\n\r\n    try {\r\n      // Thử apply qua API trước\r\n      try {\r\n        console.log(\"Calling API with:\", {\r\n          code: promotion.code,\r\n          orderAmount: totalPrice,\r\n        });\r\n\r\n        const response = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\r\n          code: promotion.code,\r\n          orderAmount: totalPrice,\r\n        });\r\n\r\n        console.log(\"API Response:\", response.data);\r\n\r\n        if (response.data.valid) {\r\n          const promotionData = {\r\n            code: promotion.code,\r\n            discount: response.data.discount,\r\n            message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,\r\n            promotionId: response.data.promotionId,\r\n          };\r\n\r\n          console.log(\"Calling onApplyPromotion with:\", promotionData);\r\n          onApplyPromotion(promotionData);\r\n          onHide();\r\n        } else {\r\n          console.log(\"API returned invalid promotion:\", response.data);\r\n        }\r\n      } catch (apiError) {\r\n        // Nếu API không có, sử dụng mock logic\r\n        console.log(\"API Error, using mock promotion application:\", apiError.message);\r\n        const promotionData = {\r\n          code: promotion.code,\r\n          discount: promotion.discount,\r\n          message: `Promotion applied: -${Utils.formatCurrency(promotion.discount)}`,\r\n          promotionId: promotion._id,\r\n        };\r\n\r\n        console.log(\"Calling onApplyPromotion with mock data:\", promotionData);\r\n        onApplyPromotion(promotionData);\r\n        onHide();\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error applying promotion:\", error);\r\n    }\r\n    setApplying(false);\r\n  };\r\n\r\n  const handleRemovePromotion = () => {\r\n    onApplyPromotion({\r\n      code: \"\",\r\n      discount: 0,\r\n      message: \"\",\r\n      promotionId: null,\r\n    });\r\n    onHide();\r\n  };\r\n\r\n  return (\r\n    <Modal show={show} onHide={onHide} size=\"lg\" centered>\r\n      <Modal.Header \r\n        closeButton \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          borderColor: \"rgba(255,255,255,0.2)\",\r\n          color: \"white\"\r\n        }}\r\n      >\r\n        <Modal.Title className=\"d-flex align-items-center\">\r\n          <FaTag className=\"me-2\" />\r\n          Select Promotion\r\n        </Modal.Title>\r\n      </Modal.Header>\r\n      \r\n      <Modal.Body \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          color: \"white\",\r\n          maxHeight: \"60vh\",\r\n          overflowY: \"auto\"\r\n        }}\r\n      >\r\n        {loading ? (\r\n          <div className=\"text-center py-4\">\r\n            <Spinner animation=\"border\" variant=\"light\" />\r\n            <div className=\"mt-2\">Loading promotions...</div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/* Current promotion section */}\r\n            {currentPromotionId && (\r\n              <div className=\"mb-4\">\r\n                <h6 className=\"mb-3\">Current Applied Promotion</h6>\r\n                <Card \r\n                  className=\"promotion-card current-promotion\"\r\n                  style={{ \r\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\", \r\n                    borderColor: \"#28a745\",\r\n                    border: \"2px solid #28a745\"\r\n                  }}\r\n                >\r\n                  <Card.Body className=\"py-3\">\r\n                    <div className=\"d-flex justify-content-between align-items-center\">\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <FaCheck className=\"text-success me-2\" />\r\n                        <span className=\"text-success fw-bold\">Applied</span>\r\n                      </div>\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={handleRemovePromotion}\r\n                        disabled={applying}\r\n                      >\r\n                        <FaTimes className=\"me-1\" />\r\n                        Remove\r\n                      </Button>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n            )}\r\n\r\n            {/* Promotions section */}\r\n            <h6 className=\"mb-3\">\r\n              Available Promotions \r\n              <span className=\"small ms-2\" style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                ({promotions.filter(p => p.isValid).length} ready, {promotions.filter(p => !p.isValid).length} starting soon)\r\n              </span>\r\n            </h6>\r\n            {promotions.length === 0 ? (\r\n              <div className=\"text-center py-4\" style={{color: 'rgba(255,255,255,0.7)'}}>\r\n                <FaTag size={48} className=\"mb-3\" style={{opacity: 0.5}} />\r\n                <div>No promotions available</div>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {/* Available promotions */}\r\n                {promotions.filter(p => p.isValid).length > 0 && (\r\n                  <div className=\"row g-3 mb-4\">\r\n                    {promotions.filter(p => p.isValid).map((promotion) => (\r\n                      <div key={promotion._id} className=\"col-12\">\r\n                        <Card \r\n                          className={`promotion-card ${currentPromotionId === promotion._id ? 'current' : ''}`}\r\n                          style={{ \r\n                            backgroundColor: currentPromotionId === promotion._id ? \"rgba(40, 167, 69, 0.2)\" : \"rgba(255,255,255,0.1)\",\r\n                            borderColor: currentPromotionId === promotion._id ? \"#28a745\" : \"rgba(255,255,255,0.3)\",\r\n                            cursor: \"pointer\",\r\n                            transition: \"all 0.3s ease\"\r\n                          }}\r\n                          onClick={() => {\r\n                            console.log(\"Promotion card clicked:\", promotion);\r\n                            handleApplyPromotion(promotion);\r\n                          }}\r\n                        >\r\n                          <Card.Body className=\"py-3\">\r\n                            <div className=\"d-flex justify-content-between align-items-start\">\r\n                              <div className=\"flex-grow-1\">\r\n                                <div className=\"d-flex align-items-center mb-2\">\r\n                                  <FaTag className=\"me-2 text-primary\" />\r\n                                  <h6 className=\"mb-0 fw-bold\">{promotion.code}</h6>\r\n                                  {currentPromotionId === promotion._id && (\r\n                                    <Badge bg=\"success\" className=\"ms-2\">Applied</Badge>\r\n                                  )}\r\n                                  <Badge bg=\"success\" className=\"ms-2\">Available</Badge>\r\n                                </div>\r\n                                \r\n                                <p className=\"mb-2 small\" style={{color: 'rgba(255,255,255,0.7)'}}>{promotion.description}</p>\r\n                                \r\n                                <div className=\"d-flex justify-content-between align-items-center\">\r\n                                  <div>\r\n                                    <span className=\"text-success fw-bold\">\r\n                                      Save {Utils.formatCurrency(promotion.discount)}\r\n                                    </span>\r\n                                  </div>\r\n                                  \r\n                                  <div className=\"text-end\">\r\n                                    <div className=\"small\">\r\n                                      {promotion.minOrderAmount && (\r\n                                        <div className=\"text-success\">\r\n                                          Min: {Utils.formatCurrency(promotion.minOrderAmount)} ✓\r\n                                        </div>\r\n                                      )}\r\n                                      {promotion.maxDiscount && (\r\n                                        <div style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                                          Max: {Utils.formatCurrency(promotion.maxDiscount)}\r\n                                        </div>\r\n                                      )}\r\n                                      {promotion.expiryDate && (\r\n                                        <div className=\"text-success\">\r\n                                          Expires: {new Date(promotion.expiryDate).toLocaleDateString()} ✓\r\n                                        </div>\r\n                                      )}\r\n                                    </div>\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Starting soon promotions */}\r\n                {promotions.filter(p => !p.isValid).length > 0 && (\r\n                  <>\r\n                    <h6 className=\"mb-3 text-warning\">\r\n                      Starting Soon ({promotions.filter(p => !p.isValid).length})\r\n                    </h6>\r\n                    <div className=\"row g-3\">\r\n                      {promotions.filter(p => !p.isValid).map((promotion) => (\r\n                        <div key={promotion._id} className=\"col-12\">\r\n                          <Card \r\n                            className=\"promotion-card disabled\"\r\n                            style={{ \r\n                              backgroundColor: \"rgba(255, 193, 7, 0.1)\",\r\n                              borderColor: \"rgba(255, 193, 7, 0.5)\",\r\n                              cursor: \"not-allowed\",\r\n                              opacity: 0.8,\r\n                              transition: \"all 0.3s ease\"\r\n                            }}\r\n                          >\r\n                            <Card.Body className=\"py-3\">\r\n                              <div className=\"d-flex justify-content-between align-items-start\">\r\n                                <div className=\"flex-grow-1\">\r\n                                  <div className=\"d-flex align-items-center mb-2\">\r\n                                    <FaTag className=\"me-2 text-warning\" />\r\n                                    <h6 className=\"mb-0 fw-bold\">{promotion.code}</h6>\r\n                                    <Badge bg=\"warning\" className=\"ms-2\" style={{color: 'white'}}>Starting Soon</Badge>\r\n                                  </div>\r\n                                  \r\n                                  <p className=\"mb-2 small\" style={{color: 'rgba(255,255,255,0.7)'}}>{promotion.description}</p>\r\n                                  \r\n                                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                                    <div>\r\n                                      <span className=\"text-warning small fw-bold\">\r\n                                        {promotion.message}\r\n                                      </span>\r\n                                    </div>\r\n                                    \r\n                                    <div className=\"text-end\">\r\n                                      <div className=\"small\">\r\n                                        {promotion.minOrderAmount && (\r\n                                          <div className={`${totalPrice >= promotion.minOrderAmount ? 'text-success' : 'text-warning'}`}>\r\n                                            Min: {Utils.formatCurrency(promotion.minOrderAmount)}\r\n                                            {totalPrice >= promotion.minOrderAmount ? ' ✓' : ' ✗'}\r\n                                          </div>\r\n                                        )}\r\n                                        {promotion.maxDiscount && (\r\n                                          <div style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                                            Max: {Utils.formatCurrency(promotion.maxDiscount)}\r\n                                          </div>\r\n                                        )}\r\n                                        {(promotion.startDate || promotion.expiryDate) && (\r\n                                          <div className=\"text-warning\">\r\n                                            Starts: {new Date(promotion.startDate || promotion.expiryDate).toLocaleDateString()}\r\n                                          </div>\r\n                                        )}\r\n                                      </div>\r\n                                    </div>\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            </Card.Body>\r\n                          </Card>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </>\r\n            )}\r\n          </>\r\n        )}\r\n      </Modal.Body>\r\n      \r\n      <Modal.Footer \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          borderColor: \"rgba(255,255,255,0.2)\"\r\n        }}\r\n      >\r\n        <Button variant=\"outline-light\" onClick={onHide} disabled={applying}>\r\n          Close\r\n        </Button>\r\n      </Modal.Footer>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default PromotionModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACrE,SAASC,KAAK,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AACxD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAO,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,UAAU;EAAEC,gBAAgB;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd,IAAIgB,IAAI,IAAIE,UAAU,GAAG,CAAC,EAAE;MAC1BY,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEb,UAAU,CAAC;MACjEc,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAChB,IAAI,EAAEE,UAAU,CAAC,CAAC;EAEtB,MAAMc,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,IAAIQ,aAAa,GAAG,EAAE;MACtB,IAAI;QACFH,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,MAAMG,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,sCAAsC,CAAC;QACxEL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEG,QAAQ,CAACE,IAAI,CAAC;;QAE3C;QACAH,aAAa,GAAGC,QAAQ,CAACE,IAAI,CAACd,UAAU,IAAIY,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,IAAI,EAAE;QACrFN,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEE,aAAa,CAAC;;QAEtD;QACA,IAAI,CAACI,KAAK,CAACC,OAAO,CAACL,aAAa,CAAC,IAAIA,aAAa,CAACM,MAAM,KAAK,CAAC,EAAE;UAC/DT,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE,MAAM,IAAIS,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF,CAAC,CAAC,OAAOC,QAAQ,EAAE;QACjB;QACAX,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEU,QAAQ,CAACC,OAAO,EAAE,6BAA6B,CAAC;QAC1ET,aAAa,GAAG,CACd;UACEU,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,8BAA8B;UAC3CC,YAAY,EAAE,OAAO;UACrBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,GAAG;UACnBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,EACD;UACER,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,WAAW;UACjBC,WAAW,EAAE,yBAAyB;UACtCC,YAAY,EAAE,YAAY;UAC1BC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,EAAE;UAClBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,EACD;UACER,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,WAAW;UACjBC,WAAW,EAAE,gCAAgC;UAC7CC,YAAY,EAAE,OAAO;UACrBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,GAAG;UACnBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,EACD;UACER,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,SAAS;UACfC,WAAW,EAAE,4BAA4B;UACzCC,YAAY,EAAE,OAAO;UACrBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,EAAE;UAClBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,CACF;MACH;MAEArB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEb,UAAU,CAAC;MACtDY,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEE,aAAa,CAACM,MAAM,EAAE,YAAY,CAAC;;MAE7D;MACA,MAAMa,mBAAmB,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC3CrB,aAAa,CAACsB,GAAG,CAAC,OAAOC,KAAK,EAAEC,KAAK,KAAK;QACxC3B,OAAO,CAACC,GAAG,CAAC,wBAAwB0B,KAAK,GAAG,CAAC,GAAG,EAAED,KAAK,CAACZ,IAAI,CAAC;QAE7D,IAAI;UACF,MAAMc,WAAW,GAAG,MAAMjD,KAAK,CAACkD,IAAI,CAAC,4CAA4C,EAAE;YACjFf,IAAI,EAAEY,KAAK,CAACZ,IAAI;YAChBgB,WAAW,EAAE1C;UACf,CAAC,CAAC;UACFY,OAAO,CAACC,GAAG,CAAC,6BAA6ByB,KAAK,CAACZ,IAAI,GAAG,EAAEc,WAAW,CAACtB,IAAI,CAAC;UAEzE,OAAO;YACL,GAAGoB,KAAK;YACRK,OAAO,EAAEH,WAAW,CAACtB,IAAI,CAAC0B,KAAK;YAC/BC,QAAQ,EAAEL,WAAW,CAACtB,IAAI,CAAC2B,QAAQ,IAAI,CAAC;YACxCrB,OAAO,EAAEgB,WAAW,CAACtB,IAAI,CAACM,OAAO,IAAI;UACvC,CAAC;QACH,CAAC,CAAC,OAAOsB,GAAG,EAAE;UACZlC,OAAO,CAACC,GAAG,CAAC,6BAA6ByB,KAAK,CAACZ,IAAI,yBAAyB,CAAC;;UAE7E;UACA,MAAMqB,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;UACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACV,KAAK,CAACW,SAAS,CAAC;UAC3C,MAAMC,OAAO,GAAG,IAAIF,IAAI,CAACV,KAAK,CAACY,OAAO,CAAC;UAEvC,MAAMC,aAAa,GAAGJ,GAAG,IAAIE,SAAS,IAAIF,GAAG,IAAIG,OAAO;UACxD,MAAME,aAAa,GAAGpD,UAAU,KAAKsC,KAAK,CAACR,cAAc,IAAI,CAAC,CAAC;UAC/D,MAAMG,QAAQ,GAAGK,KAAK,CAACL,QAAQ,KAAK,KAAK;UAEzC,MAAMU,OAAO,GAAGQ,aAAa,IAAIC,aAAa,IAAInB,QAAQ;UAE1D,IAAIY,QAAQ,GAAG,CAAC;UAChB,IAAIrB,OAAO,GAAG,EAAE;UAEhB,IAAImB,OAAO,EAAE;YACX,IAAIL,KAAK,CAACV,YAAY,KAAK,YAAY,IAAIU,KAAK,CAACV,YAAY,KAAK,YAAY,EAAE;cAC9EiB,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAAEtD,UAAU,GAAGsC,KAAK,CAACT,aAAa,GAAI,GAAG,EAAES,KAAK,CAACiB,iBAAiB,IAAIjB,KAAK,CAACP,WAAW,IAAIyB,QAAQ,CAAC;YACzH,CAAC,MAAM;cACLX,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAAChB,KAAK,CAACT,aAAa,EAAES,KAAK,CAACiB,iBAAiB,IAAIjB,KAAK,CAACP,WAAW,IAAIyB,QAAQ,CAAC;YACpG;YACAhC,OAAO,GAAG,QAAQqB,QAAQ,EAAE;UAC9B,CAAC,MAAM;YACL,IAAI,CAACM,aAAa,EAAE;cAClB,IAAIJ,GAAG,GAAGE,SAAS,EAAEzB,OAAO,GAAG,+BAA+B,CAAC,KAC1D,IAAIuB,GAAG,GAAGG,OAAO,EAAE1B,OAAO,GAAG,uBAAuB,CAAC,KACrDA,OAAO,GAAG,4BAA4B;YAC7C,CAAC,MAAM,IAAI,CAAC4B,aAAa,EAAE5B,OAAO,GAAG,kBAAkBc,KAAK,CAACR,cAAc,WAAW,CAAC,KAClF,IAAI,CAACG,QAAQ,EAAET,OAAO,GAAG,yBAAyB,CAAC,KACnDA,OAAO,GAAG,gBAAgB;UACjC;UAEAZ,OAAO,CAACC,GAAG,CAAC,uBAAuByB,KAAK,CAACZ,IAAI,GAAG,EAAE;YAAEiB,OAAO;YAAEE,QAAQ;YAAErB;UAAQ,CAAC,CAAC;UAEjF,OAAO;YACL,GAAGc,KAAK;YACRK,OAAO;YACPE,QAAQ;YACRrB;UACF,CAAC;QACH;MACF,CAAC,CACH,CAAC;MAEDZ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEqB,mBAAmB,CAAC;;MAE/D;MACA;MACA,MAAMuB,iBAAiB,GAAGvB,mBAAmB,CAACwB,MAAM,CAACpB,KAAK,IAAI;QAC5D,MAAMS,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACV,KAAK,CAACW,SAAS,IAAIX,KAAK,CAACN,UAAU,IAAI,YAAY,CAAC;QAC/E,MAAMkB,OAAO,GAAG,IAAIF,IAAI,CAACV,KAAK,CAACY,OAAO,IAAIZ,KAAK,CAACN,UAAU,IAAI,YAAY,CAAC;;QAE3E;QACA,MAAM2B,UAAU,GAAGZ,GAAG,IAAIG,OAAO;QACjC,MAAMjB,QAAQ,GAAGK,KAAK,CAACL,QAAQ,KAAK,KAAK;QAEzC,OAAO0B,UAAU,IAAI1B,QAAQ;MAC/B,CAAC,CAAC;MAEFrB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE4C,iBAAiB,CAACpC,MAAM,EAAE,IAAI,EAAEa,mBAAmB,CAACb,MAAM,CAAC;MAC9FT,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE4C,iBAAiB,CAACC,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACjB,OAAO,CAAC,CAACtB,MAAM,CAAC;MAC9ET,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE4C,iBAAiB,CAACC,MAAM,CAACE,CAAC;QAAA,IAAAC,UAAA;QAAA,OAAI,CAACD,CAAC,CAACjB,OAAO,MAAAkB,UAAA,GAAID,CAAC,CAACpC,OAAO,cAAAqC,UAAA,uBAATA,UAAA,CAAWC,QAAQ,CAAC,aAAa,CAAC;MAAA,EAAC,CAACzC,MAAM,CAAC;;MAErH;MACA,MAAM0C,gBAAgB,GAAGN,iBAAiB,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACxD;QACA,IAAID,CAAC,CAACtB,OAAO,IAAI,CAACuB,CAAC,CAACvB,OAAO,EAAE,OAAO,CAAC,CAAC;QACtC,IAAI,CAACsB,CAAC,CAACtB,OAAO,IAAIuB,CAAC,CAACvB,OAAO,EAAE,OAAO,CAAC;;QAErC;QACA,OAAOuB,CAAC,CAACrB,QAAQ,GAAGoB,CAAC,CAACpB,QAAQ;MAChC,CAAC,CAAC;MAEFxC,aAAa,CAAC0D,gBAAgB,CAAC;IACjC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdvD,OAAO,CAACuD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD9D,aAAa,CAAC,EAAE,CAAC;IACnB;IACAE,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM6D,oBAAoB,GAAG,MAAOC,SAAS,IAAK;IAChD,IAAI,CAACA,SAAS,CAAC1B,OAAO,EAAE;MACtB/B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwD,SAAS,CAAC;MACjD;IACF;IAEAzD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwD,SAAS,CAAC;IAC7C1D,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF;MACA,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;UAC/Ba,IAAI,EAAE2C,SAAS,CAAC3C,IAAI;UACpBgB,WAAW,EAAE1C;QACf,CAAC,CAAC;QAEF,MAAMgB,QAAQ,GAAG,MAAMzB,KAAK,CAACkD,IAAI,CAAC,4CAA4C,EAAE;UAC9Ef,IAAI,EAAE2C,SAAS,CAAC3C,IAAI;UACpBgB,WAAW,EAAE1C;QACf,CAAC,CAAC;QAEFY,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEG,QAAQ,CAACE,IAAI,CAAC;QAE3C,IAAIF,QAAQ,CAACE,IAAI,CAAC0B,KAAK,EAAE;UACvB,MAAM0B,aAAa,GAAG;YACpB5C,IAAI,EAAE2C,SAAS,CAAC3C,IAAI;YACpBmB,QAAQ,EAAE7B,QAAQ,CAACE,IAAI,CAAC2B,QAAQ;YAChCrB,OAAO,EAAE,uBAAuBhC,KAAK,CAAC+E,cAAc,CAACvD,QAAQ,CAACE,IAAI,CAAC2B,QAAQ,CAAC,EAAE;YAC9E2B,WAAW,EAAExD,QAAQ,CAACE,IAAI,CAACsD;UAC7B,CAAC;UAED5D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEyD,aAAa,CAAC;UAC5DrE,gBAAgB,CAACqE,aAAa,CAAC;UAC/BvE,MAAM,CAAC,CAAC;QACV,CAAC,MAAM;UACLa,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEG,QAAQ,CAACE,IAAI,CAAC;QAC/D;MACF,CAAC,CAAC,OAAOK,QAAQ,EAAE;QACjB;QACAX,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEU,QAAQ,CAACC,OAAO,CAAC;QAC7E,MAAM8C,aAAa,GAAG;UACpB5C,IAAI,EAAE2C,SAAS,CAAC3C,IAAI;UACpBmB,QAAQ,EAAEwB,SAAS,CAACxB,QAAQ;UAC5BrB,OAAO,EAAE,uBAAuBhC,KAAK,CAAC+E,cAAc,CAACF,SAAS,CAACxB,QAAQ,CAAC,EAAE;UAC1E2B,WAAW,EAAEH,SAAS,CAAC5C;QACzB,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEyD,aAAa,CAAC;QACtErE,gBAAgB,CAACqE,aAAa,CAAC;QAC/BvE,MAAM,CAAC,CAAC;MACV;IACF,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACdvD,OAAO,CAACuD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;IACAxD,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAM8D,qBAAqB,GAAGA,CAAA,KAAM;IAClCxE,gBAAgB,CAAC;MACfyB,IAAI,EAAE,EAAE;MACRmB,QAAQ,EAAE,CAAC;MACXrB,OAAO,EAAE,EAAE;MACXgD,WAAW,EAAE;IACf,CAAC,CAAC;IACFzE,MAAM,CAAC,CAAC;EACV,CAAC;EAED,oBACEL,OAAA,CAACX,KAAK;IAACe,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAAC2E,IAAI,EAAC,IAAI;IAACC,QAAQ;IAAAC,QAAA,gBACnDlF,OAAA,CAACX,KAAK,CAAC8F,MAAM;MACXC,WAAW;MACXC,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCC,WAAW,EAAE,uBAAuB;QACpCC,KAAK,EAAE;MACT,CAAE;MAAAN,QAAA,eAEFlF,OAAA,CAACX,KAAK,CAACoG,KAAK;QAACC,SAAS,EAAC,2BAA2B;QAAAR,QAAA,gBAChDlF,OAAA,CAACN,KAAK;UAACgG,SAAS,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE5B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEf9F,OAAA,CAACX,KAAK,CAAC0G,IAAI;MACTV,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCE,KAAK,EAAE,OAAO;QACdQ,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE;MACb,CAAE;MAAAf,QAAA,EAEDtE,OAAO,gBACNZ,OAAA;QAAK0F,SAAS,EAAC,kBAAkB;QAAAR,QAAA,gBAC/BlF,OAAA,CAACP,OAAO;UAACyG,SAAS,EAAC,QAAQ;UAACC,OAAO,EAAC;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C9F,OAAA;UAAK0F,SAAS,EAAC,MAAM;UAAAR,QAAA,EAAC;QAAqB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,gBAEN9F,OAAA,CAAAE,SAAA;QAAAgF,QAAA,GAEG1E,kBAAkB,iBACjBR,OAAA;UAAK0F,SAAS,EAAC,MAAM;UAAAR,QAAA,gBACnBlF,OAAA;YAAI0F,SAAS,EAAC,MAAM;YAAAR,QAAA,EAAC;UAAyB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnD9F,OAAA,CAACT,IAAI;YACHmG,SAAS,EAAC,kCAAkC;YAC5CL,KAAK,EAAE;cACLC,eAAe,EAAE,wBAAwB;cACzCC,WAAW,EAAE,SAAS;cACtBa,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,eAEFlF,OAAA,CAACT,IAAI,CAACwG,IAAI;cAACL,SAAS,EAAC,MAAM;cAAAR,QAAA,eACzBlF,OAAA;gBAAK0F,SAAS,EAAC,mDAAmD;gBAAAR,QAAA,gBAChElF,OAAA;kBAAK0F,SAAS,EAAC,2BAA2B;kBAAAR,QAAA,gBACxClF,OAAA,CAACJ,OAAO;oBAAC8F,SAAS,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzC9F,OAAA;oBAAM0F,SAAS,EAAC,sBAAsB;oBAAAR,QAAA,EAAC;kBAAO;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACN9F,OAAA,CAACV,MAAM;kBACL6G,OAAO,EAAC,gBAAgB;kBACxBnB,IAAI,EAAC,IAAI;kBACTqB,OAAO,EAAEtB,qBAAsB;kBAC/BuB,QAAQ,EAAEtF,QAAS;kBAAAkE,QAAA,gBAEnBlF,OAAA,CAACL,OAAO;oBAAC+F,SAAS,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAGD9F,OAAA;UAAI0F,SAAS,EAAC,MAAM;UAAAR,QAAA,GAAC,sBAEnB,eAAAlF,OAAA;YAAM0F,SAAS,EAAC,YAAY;YAACL,KAAK,EAAE;cAACG,KAAK,EAAE;YAAuB,CAAE;YAAAN,QAAA,GAAC,GACnE,EAACxE,UAAU,CAACsD,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACjB,OAAO,CAAC,CAACtB,MAAM,EAAC,UAAQ,EAACjB,UAAU,CAACsD,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAACjB,OAAO,CAAC,CAACtB,MAAM,EAAC,iBAChG;UAAA;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACJpF,UAAU,CAACiB,MAAM,KAAK,CAAC,gBACtB3B,OAAA;UAAK0F,SAAS,EAAC,kBAAkB;UAACL,KAAK,EAAE;YAACG,KAAK,EAAE;UAAuB,CAAE;UAAAN,QAAA,gBACxElF,OAAA,CAACN,KAAK;YAACsF,IAAI,EAAE,EAAG;YAACU,SAAS,EAAC,MAAM;YAACL,KAAK,EAAE;cAACkB,OAAO,EAAE;YAAG;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3D9F,OAAA;YAAAkF,QAAA,EAAK;UAAuB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,gBAEN9F,OAAA,CAAAE,SAAA;UAAAgF,QAAA,GAEGxE,UAAU,CAACsD,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACjB,OAAO,CAAC,CAACtB,MAAM,GAAG,CAAC,iBAC3C3B,OAAA;YAAK0F,SAAS,EAAC,cAAc;YAAAR,QAAA,EAC1BxE,UAAU,CAACsD,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACjB,OAAO,CAAC,CAACN,GAAG,CAAEgC,SAAS,iBAC/C3E,OAAA;cAAyB0F,SAAS,EAAC,QAAQ;cAAAR,QAAA,eACzClF,OAAA,CAACT,IAAI;gBACHmG,SAAS,EAAE,kBAAkBlF,kBAAkB,KAAKmE,SAAS,CAAC5C,GAAG,GAAG,SAAS,GAAG,EAAE,EAAG;gBACrFsD,KAAK,EAAE;kBACLC,eAAe,EAAE9E,kBAAkB,KAAKmE,SAAS,CAAC5C,GAAG,GAAG,wBAAwB,GAAG,uBAAuB;kBAC1GwD,WAAW,EAAE/E,kBAAkB,KAAKmE,SAAS,CAAC5C,GAAG,GAAG,SAAS,GAAG,uBAAuB;kBACvFyE,MAAM,EAAE,SAAS;kBACjBC,UAAU,EAAE;gBACd,CAAE;gBACFJ,OAAO,EAAEA,CAAA,KAAM;kBACbnF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwD,SAAS,CAAC;kBACjDD,oBAAoB,CAACC,SAAS,CAAC;gBACjC,CAAE;gBAAAO,QAAA,eAEFlF,OAAA,CAACT,IAAI,CAACwG,IAAI;kBAACL,SAAS,EAAC,MAAM;kBAAAR,QAAA,eACzBlF,OAAA;oBAAK0F,SAAS,EAAC,kDAAkD;oBAAAR,QAAA,eAC/DlF,OAAA;sBAAK0F,SAAS,EAAC,aAAa;sBAAAR,QAAA,gBAC1BlF,OAAA;wBAAK0F,SAAS,EAAC,gCAAgC;wBAAAR,QAAA,gBAC7ClF,OAAA,CAACN,KAAK;0BAACgG,SAAS,EAAC;wBAAmB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACvC9F,OAAA;0BAAI0F,SAAS,EAAC,cAAc;0BAAAR,QAAA,EAAEP,SAAS,CAAC3C;wBAAI;0BAAA2D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,EACjDtF,kBAAkB,KAAKmE,SAAS,CAAC5C,GAAG,iBACnC/B,OAAA,CAACR,KAAK;0BAACkH,EAAE,EAAC,SAAS;0BAAChB,SAAS,EAAC,MAAM;0BAAAR,QAAA,EAAC;wBAAO;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CACpD,eACD9F,OAAA,CAACR,KAAK;0BAACkH,EAAE,EAAC,SAAS;0BAAChB,SAAS,EAAC,MAAM;0BAAAR,QAAA,EAAC;wBAAS;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CAAC,eAEN9F,OAAA;wBAAG0F,SAAS,EAAC,YAAY;wBAACL,KAAK,EAAE;0BAACG,KAAK,EAAE;wBAAuB,CAAE;wBAAAN,QAAA,EAAEP,SAAS,CAAC1C;sBAAW;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAE9F9F,OAAA;wBAAK0F,SAAS,EAAC,mDAAmD;wBAAAR,QAAA,gBAChElF,OAAA;0BAAAkF,QAAA,eACElF,OAAA;4BAAM0F,SAAS,EAAC,sBAAsB;4BAAAR,QAAA,GAAC,OAChC,EAACpF,KAAK,CAAC+E,cAAc,CAACF,SAAS,CAACxB,QAAQ,CAAC;0BAAA;4BAAAwC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eAEN9F,OAAA;0BAAK0F,SAAS,EAAC,UAAU;0BAAAR,QAAA,eACvBlF,OAAA;4BAAK0F,SAAS,EAAC,OAAO;4BAAAR,QAAA,GACnBP,SAAS,CAACvC,cAAc,iBACvBpC,OAAA;8BAAK0F,SAAS,EAAC,cAAc;8BAAAR,QAAA,GAAC,OACvB,EAACpF,KAAK,CAAC+E,cAAc,CAACF,SAAS,CAACvC,cAAc,CAAC,EAAC,SACvD;4BAAA;8BAAAuD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACN,EACAnB,SAAS,CAACtC,WAAW,iBACpBrC,OAAA;8BAAKqF,KAAK,EAAE;gCAACG,KAAK,EAAE;8BAAuB,CAAE;8BAAAN,QAAA,GAAC,OACvC,EAACpF,KAAK,CAAC+E,cAAc,CAACF,SAAS,CAACtC,WAAW,CAAC;4BAAA;8BAAAsD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9C,CACN,EACAnB,SAAS,CAACrC,UAAU,iBACnBtC,OAAA;8BAAK0F,SAAS,EAAC,cAAc;8BAAAR,QAAA,GAAC,WACnB,EAAC,IAAI5B,IAAI,CAACqB,SAAS,CAACrC,UAAU,CAAC,CAACqE,kBAAkB,CAAC,CAAC,EAAC,SAChE;4BAAA;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACN;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC,GA1DCnB,SAAS,CAAC5C,GAAG;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2DlB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGApF,UAAU,CAACsD,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAACjB,OAAO,CAAC,CAACtB,MAAM,GAAG,CAAC,iBAC5C3B,OAAA,CAAAE,SAAA;YAAAgF,QAAA,gBACElF,OAAA;cAAI0F,SAAS,EAAC,mBAAmB;cAAAR,QAAA,GAAC,iBACjB,EAACxE,UAAU,CAACsD,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAACjB,OAAO,CAAC,CAACtB,MAAM,EAAC,GAC5D;YAAA;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9F,OAAA;cAAK0F,SAAS,EAAC,SAAS;cAAAR,QAAA,EACrBxE,UAAU,CAACsD,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAACjB,OAAO,CAAC,CAACN,GAAG,CAAEgC,SAAS,iBAChD3E,OAAA;gBAAyB0F,SAAS,EAAC,QAAQ;gBAAAR,QAAA,eACzClF,OAAA,CAACT,IAAI;kBACHmG,SAAS,EAAC,yBAAyB;kBACnCL,KAAK,EAAE;oBACLC,eAAe,EAAE,wBAAwB;oBACzCC,WAAW,EAAE,wBAAwB;oBACrCiB,MAAM,EAAE,aAAa;oBACrBD,OAAO,EAAE,GAAG;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAvB,QAAA,eAEFlF,OAAA,CAACT,IAAI,CAACwG,IAAI;oBAACL,SAAS,EAAC,MAAM;oBAAAR,QAAA,eACzBlF,OAAA;sBAAK0F,SAAS,EAAC,kDAAkD;sBAAAR,QAAA,eAC/DlF,OAAA;wBAAK0F,SAAS,EAAC,aAAa;wBAAAR,QAAA,gBAC1BlF,OAAA;0BAAK0F,SAAS,EAAC,gCAAgC;0BAAAR,QAAA,gBAC7ClF,OAAA,CAACN,KAAK;4BAACgG,SAAS,EAAC;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvC9F,OAAA;4BAAI0F,SAAS,EAAC,cAAc;4BAAAR,QAAA,EAAEP,SAAS,CAAC3C;0BAAI;4BAAA2D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAClD9F,OAAA,CAACR,KAAK;4BAACkH,EAAE,EAAC,SAAS;4BAAChB,SAAS,EAAC,MAAM;4BAACL,KAAK,EAAE;8BAACG,KAAK,EAAE;4BAAO,CAAE;4BAAAN,QAAA,EAAC;0BAAa;4BAAAS,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChF,CAAC,eAEN9F,OAAA;0BAAG0F,SAAS,EAAC,YAAY;0BAACL,KAAK,EAAE;4BAACG,KAAK,EAAE;0BAAuB,CAAE;0BAAAN,QAAA,EAAEP,SAAS,CAAC1C;wBAAW;0BAAA0D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAE9F9F,OAAA;0BAAK0F,SAAS,EAAC,mDAAmD;0BAAAR,QAAA,gBAChElF,OAAA;4BAAAkF,QAAA,eACElF,OAAA;8BAAM0F,SAAS,EAAC,4BAA4B;8BAAAR,QAAA,EACzCP,SAAS,CAAC7C;4BAAO;8BAAA6D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACd;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eAEN9F,OAAA;4BAAK0F,SAAS,EAAC,UAAU;4BAAAR,QAAA,eACvBlF,OAAA;8BAAK0F,SAAS,EAAC,OAAO;8BAAAR,QAAA,GACnBP,SAAS,CAACvC,cAAc,iBACvBpC,OAAA;gCAAK0F,SAAS,EAAE,GAAGpF,UAAU,IAAIqE,SAAS,CAACvC,cAAc,GAAG,cAAc,GAAG,cAAc,EAAG;gCAAA8C,QAAA,GAAC,OACxF,EAACpF,KAAK,CAAC+E,cAAc,CAACF,SAAS,CAACvC,cAAc,CAAC,EACnD9B,UAAU,IAAIqE,SAAS,CAACvC,cAAc,GAAG,IAAI,GAAG,IAAI;8BAAA;gCAAAuD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAClD,CACN,EACAnB,SAAS,CAACtC,WAAW,iBACpBrC,OAAA;gCAAKqF,KAAK,EAAE;kCAACG,KAAK,EAAE;gCAAuB,CAAE;gCAAAN,QAAA,GAAC,OACvC,EAACpF,KAAK,CAAC+E,cAAc,CAACF,SAAS,CAACtC,WAAW,CAAC;8BAAA;gCAAAsD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9C,CACN,EACA,CAACnB,SAAS,CAACpB,SAAS,IAAIoB,SAAS,CAACrC,UAAU,kBAC3CtC,OAAA;gCAAK0F,SAAS,EAAC,cAAc;gCAAAR,QAAA,GAAC,UACpB,EAAC,IAAI5B,IAAI,CAACqB,SAAS,CAACpB,SAAS,IAAIoB,SAAS,CAACrC,UAAU,CAAC,CAACqE,kBAAkB,CAAC,CAAC;8BAAA;gCAAAhB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAChF,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC,GArDCnB,SAAS,CAAC5C,GAAG;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsDlB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,eACN,CACH;QAAA,eACD,CACH;MAAA,eACD;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAEb9F,OAAA,CAACX,KAAK,CAACuH,MAAM;MACXvB,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCC,WAAW,EAAE;MACf,CAAE;MAAAL,QAAA,eAEFlF,OAAA,CAACV,MAAM;QAAC6G,OAAO,EAAC,eAAe;QAACE,OAAO,EAAEhG,MAAO;QAACiG,QAAQ,EAAEtF,QAAS;QAAAkE,QAAA,EAAC;MAErE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ,CAAC;AAACrF,EAAA,CAveIN,cAAc;AAAA0G,EAAA,GAAd1G,cAAc;AAyepB,eAAeA,cAAc;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}