{"name": "socket.io-client", "version": "4.8.1", "description": "Realtime application framework client", "keywords": ["realtime", "framework", "websocket", "tcp", "events", "client"], "files": ["dist/", "build/"], "type": "commonjs", "main": "./build/cjs/index.js", "module": "./build/esm/index.js", "exports": {"./package.json": "./package.json", "./dist/socket.io.js": "./dist/socket.io.js", "./dist/socket.io.js.map": "./dist/socket.io.js.map", ".": {"import": {"types": "./build/esm/index.d.ts", "node": "./build/esm-debug/index.js", "default": "./build/esm/index.js"}, "require": {"types": "./build/cjs/index.d.ts", "default": "./build/cjs/index.js"}}, "./debug": {"import": {"types": "./build/esm/index.d.ts", "default": "./build/esm-debug/index.js"}, "require": {"types": "./build/cjs/index.d.ts", "default": "./build/cjs/index.js"}}}, "types": "./build/esm/index.d.ts", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.2", "engine.io-client": "~6.6.1", "socket.io-parser": "~4.2.4"}, "scripts": {"compile": "rimraf ./build && tsc && tsc -p tsconfig.esm.json && ./postcompile.sh", "test": "npm run format:check && npm run compile && if test \"$BROWSERS\" = \"1\" ; then npm run test:browser; else npm run test:node; fi", "test:node": "mocha --require ts-node/register --require test/support/hooks.ts --exit test/index.ts", "test:browser": "ts-node test/browser-runner.ts", "test:types": "tsd", "build": "rollup -c support/rollup.config.umd.js && rollup -c support/rollup.config.esm.js && rollup -c support/rollup.config.umd.msgpack.js", "bundle-size": "node support/bundle-size.js", "format:check": "prettier --check \"*.js\" \"lib/**/*.ts\" \"test/**/*.ts\" \"support/**/*.js\"", "format:fix": "prettier --write \"*.js\" \"lib/**/*.ts\" \"test/**/*.ts\" \"support/**/*.js\"", "prepack": "npm run compile"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/socketio/socket.io/tree/main/packages/socket.io-client#readme", "repository": {"type": "git", "url": "git+https://github.com/socketio/socket.io.git"}, "bugs": {"url": "https://github.com/socketio/socket.io/issues"}, "license": "MIT", "engines": {"node": ">=10.0.0"}, "tsd": {"directory": "test"}, "browser": {"./test/node.ts": false}}