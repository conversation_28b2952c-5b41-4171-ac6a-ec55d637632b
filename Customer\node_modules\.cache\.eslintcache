[{"E:\\WDP301_UROOM\\Customer\\src\\index.js": "1", "E:\\WDP301_UROOM\\Customer\\src\\reportWebVitals.js": "2", "E:\\WDP301_UROOM\\Customer\\src\\App.js": "3", "E:\\WDP301_UROOM\\Customer\\src\\redux\\store.js": "4", "E:\\WDP301_UROOM\\Customer\\src\\utils\\Routes.js": "5", "E:\\WDP301_UROOM\\Customer\\src\\redux\\socket\\socketSlice.js": "6", "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-reducer.js": "7", "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-saga.js": "8", "E:\\WDP301_UROOM\\Customer\\src\\utils\\Utils.js": "9", "E:\\WDP301_UROOM\\Customer\\src\\pages\\BannedPage.jsx": "10", "E:\\WDP301_UROOM\\Customer\\src\\pages\\ErrorPage.jsx": "11", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomePage.jsx": "12", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HotelSearchPage.jsx": "13", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\BookingCheckPage.jsx": "14", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomeDetailPage.jsx": "15", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentFailedPage.jsx": "16", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentPage.jsx": "17", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentSuccessPage.jsx": "18", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\RoomDetailPage.jsx": "19", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ChatPage.jsx": "20", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ReportedFeedback.jsx": "21", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\LoginPage.jsx": "22", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ForgetPasswordPage.jsx": "23", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\RegisterPage.jsx": "24", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ResetPasswordPage.jsx": "25", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodeRegisterPage.jsx": "26", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodePage.jsx": "27", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\MyAccountPage.jsx": "28", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\BookingBill.jsx": "29", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\CreateFeedback.jsx": "30", "E:\\WDP301_UROOM\\Customer\\src\\utils\\qaData.js": "31", "E:\\WDP301_UROOM\\Customer\\src\\utils\\data.js": "32", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Footer.jsx": "33", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Header.jsx": "34", "E:\\WDP301_UROOM\\Customer\\src\\pages\\MapLocation.jsx": "35", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\saga.js": "36", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\reducer.js": "37", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\saga.js": "38", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\reducer.js": "39", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\reducer.js": "40", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\actions.js": "41", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\saga.js": "42", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\saga.js": "43", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\reducer.js": "44", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\saga.js": "45", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\reducer.js": "46", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\reducer.js": "47", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\saga.js": "48", "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\actions.js": "49", "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\reducer.js": "50", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\actions.js": "51", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\factories.js": "52", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\reducer.js": "53", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\saga.js": "54", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\actions.js": "55", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\saga.js": "56", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\factories.js": "57", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\actions.js": "58", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\reducer.js": "59", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\actions.js": "60", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\actions.js": "61", "E:\\WDP301_UROOM\\Customer\\src\\utils\\handleToken.js": "62", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\factories.js": "63", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\actions.js": "64", "E:\\WDP301_UROOM\\Customer\\src\\components\\ConfirmationModal.jsx": "65", "E:\\WDP301_UROOM\\Customer\\src\\components\\Pagination.jsx": "66", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservation\\factories.js": "67", "E:\\WDP301_UROOM\\Customer\\src\\components\\ErrorModal.jsx": "68", "E:\\WDP301_UROOM\\Customer\\src\\components\\ToastContainer.jsx": "69", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\GoogleLogin.jsx": "70", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\HotelClosedModal.jsx": "71", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\actions.js": "72", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFavoriteHotel.jsx": "73", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ChangePassword.jsx": "74", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\BookingHistory.jsx": "75", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFeedback.jsx": "76", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewInformation.jsx": "77", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewAvatar.jsx": "78", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyReportFeedBack.jsx": "79", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\RefundReservation.jsx": "80", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\factories.js": "81", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\factories.js": "82", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\factories.js": "83", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\factories.js": "84", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\factories.js": "85", "E:\\WDP301_UROOM\\Customer\\src\\adapter\\ApiConstants.js": "86", "E:\\WDP301_UROOM\\Customer\\src\\libs\\api\\index.js": "87", "E:\\WDP301_UROOM\\Customer\\src\\libs\\firebaseConfig.js": "88", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\CancelReservationModal.jsx": "89", "E:\\WDP301_UROOM\\Customer\\src\\redux\\refunding_reservation\\factories.js": "90", "E:\\WDP301_UROOM\\Customer\\src\\utils\\fonts.js": "91", "E:\\WDP301_UROOM\\Customer\\src\\services\\NotificationService.js": "92", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionModal.jsx": "93", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyPromotion.jsx": "94", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\reducer.js": "95", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\actions.js": "96", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\saga.js": "97", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\factories.js": "98", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\RoomClosedModal.jsx": "99", "E:\\WDP301_UROOM\\Customer\\src\\hooks\\usePromotionValidation.js": "100", "E:\\WDP301_UROOM\\Customer\\src\\components\\PromotionValidationHandler.jsx": "101", "E:\\WDP301_UROOM\\Customer\\src\\socket.js": "102"}, {"size": 831, "mtime": 1750045388384, "results": "103", "hashOfConfig": "104"}, {"size": 375, "mtime": 1750045388405, "results": "105", "hashOfConfig": "104"}, {"size": 4850, "mtime": 1751248384117, "results": "106", "hashOfConfig": "104"}, {"size": 1274, "mtime": 1750128490071, "results": "107", "hashOfConfig": "104"}, {"size": 1300, "mtime": 1750045388406, "results": "108", "hashOfConfig": "104"}, {"size": 1451, "mtime": 1750045388405, "results": "109", "hashOfConfig": "104"}, {"size": 1006, "mtime": 1751248590570, "results": "110", "hashOfConfig": "104"}, {"size": 731, "mtime": 1751248590570, "results": "111", "hashOfConfig": "104"}, {"size": 3227, "mtime": 1750128490073, "results": "112", "hashOfConfig": "104"}, {"size": 1928, "mtime": 1750045388385, "results": "113", "hashOfConfig": "104"}, {"size": 1374, "mtime": 1750045388385, "results": "114", "hashOfConfig": "104"}, {"size": 41830, "mtime": 1750861714764, "results": "115", "hashOfConfig": "104"}, {"size": 38585, "mtime": 1750861714765, "results": "116", "hashOfConfig": "104"}, {"size": 30023, "mtime": 1751275322962, "results": "117", "hashOfConfig": "104"}, {"size": 88520, "mtime": 1751248627673, "results": "118", "hashOfConfig": "104"}, {"size": 3157, "mtime": 1750712271161, "results": "119", "hashOfConfig": "104"}, {"size": 7041, "mtime": 1750045388389, "results": "120", "hashOfConfig": "104"}, {"size": 4608, "mtime": 1750712271161, "results": "121", "hashOfConfig": "104"}, {"size": 51823, "mtime": 1751248627673, "results": "122", "hashOfConfig": "104"}, {"size": 26015, "mtime": 1750045388387, "results": "123", "hashOfConfig": "104"}, {"size": 13701, "mtime": 1750045388389, "results": "124", "hashOfConfig": "104"}, {"size": 10248, "mtime": 1750045388396, "results": "125", "hashOfConfig": "104"}, {"size": 3742, "mtime": 1750045388396, "results": "126", "hashOfConfig": "104"}, {"size": 7427, "mtime": 1750045388396, "results": "127", "hashOfConfig": "104"}, {"size": 5465, "mtime": 1750045388396, "results": "128", "hashOfConfig": "104"}, {"size": 8504, "mtime": 1750045388397, "results": "129", "hashOfConfig": "104"}, {"size": 7544, "mtime": 1750045388397, "results": "130", "hashOfConfig": "104"}, {"size": 5583, "mtime": 1751248590565, "results": "131", "hashOfConfig": "104"}, {"size": 33077, "mtime": 1750868409422, "results": "132", "hashOfConfig": "104"}, {"size": 16963, "mtime": 1750712271164, "results": "133", "hashOfConfig": "104"}, {"size": 10836, "mtime": 1750860606178, "results": "134", "hashOfConfig": "104"}, {"size": 4408, "mtime": 1751248627675, "results": "135", "hashOfConfig": "104"}, {"size": 2571, "mtime": 1750128490060, "results": "136", "hashOfConfig": "104"}, {"size": 25894, "mtime": 1750128490061, "results": "137", "hashOfConfig": "104"}, {"size": 2587, "mtime": 1750045388385, "results": "138", "hashOfConfig": "104"}, {"size": 1803, "mtime": 1750045388403, "results": "139", "hashOfConfig": "104"}, {"size": 541, "mtime": 1750045388403, "results": "140", "hashOfConfig": "104"}, {"size": 4244, "mtime": 1750045388399, "results": "141", "hashOfConfig": "104"}, {"size": 2230, "mtime": 1750045388398, "results": "142", "hashOfConfig": "104"}, {"size": 1043, "mtime": 1750128490070, "results": "143", "hashOfConfig": "104"}, {"size": 487, "mtime": 1750045388399, "results": "144", "hashOfConfig": "104"}, {"size": 5057, "mtime": 1750045388398, "results": "145", "hashOfConfig": "104"}, {"size": 223, "mtime": 1750045388405, "results": "146", "hashOfConfig": "104"}, {"size": 1136, "mtime": 1750045388398, "results": "147", "hashOfConfig": "104"}, {"size": 9581, "mtime": 1750045388398, "results": "148", "hashOfConfig": "104"}, {"size": 888, "mtime": 1750045388399, "results": "149", "hashOfConfig": "104"}, {"size": 919, "mtime": 1750045388402, "results": "150", "hashOfConfig": "104"}, {"size": 3342, "mtime": 1750045388402, "results": "151", "hashOfConfig": "104"}, {"size": 265, "mtime": 1750128490068, "results": "152", "hashOfConfig": "104"}, {"size": 1693, "mtime": 1750128490069, "results": "153", "hashOfConfig": "104"}, {"size": 235, "mtime": 1750045388403, "results": "154", "hashOfConfig": "104"}, {"size": 1669, "mtime": 1750712271166, "results": "155", "hashOfConfig": "104"}, {"size": 551, "mtime": 1750045388399, "results": "156", "hashOfConfig": "104"}, {"size": 3000, "mtime": 1750045388402, "results": "157", "hashOfConfig": "104"}, {"size": 137, "mtime": 1750045388403, "results": "158", "hashOfConfig": "104"}, {"size": 2085, "mtime": 1750045388401, "results": "159", "hashOfConfig": "104"}, {"size": 1536, "mtime": 1750045388398, "results": "160", "hashOfConfig": "104"}, {"size": 1271, "mtime": 1750045388397, "results": "161", "hashOfConfig": "104"}, {"size": 917, "mtime": 1750045388401, "results": "162", "hashOfConfig": "104"}, {"size": 574, "mtime": 1750045388398, "results": "163", "hashOfConfig": "104"}, {"size": 322, "mtime": 1750045388399, "results": "164", "hashOfConfig": "104"}, {"size": 551, "mtime": 1750045388406, "results": "165", "hashOfConfig": "104"}, {"size": 1439, "mtime": 1750045388397, "results": "166", "hashOfConfig": "104"}, {"size": 426, "mtime": 1750045388401, "results": "167", "hashOfConfig": "104"}, {"size": 2348, "mtime": 1750045388347, "results": "168", "hashOfConfig": "104"}, {"size": 1621, "mtime": 1750045388347, "results": "169", "hashOfConfig": "104"}, {"size": 402, "mtime": 1750045388402, "results": "170", "hashOfConfig": "104"}, {"size": 864, "mtime": 1750045388347, "results": "171", "hashOfConfig": "104"}, {"size": 1493, "mtime": 1750045388347, "results": "172", "hashOfConfig": "104"}, {"size": 2146, "mtime": 1750045388396, "results": "173", "hashOfConfig": "104"}, {"size": 1088, "mtime": 1750045388391, "results": "174", "hashOfConfig": "104"}, {"size": 438, "mtime": 1750045388402, "results": "175", "hashOfConfig": "104"}, {"size": 16495, "mtime": 1750861714769, "results": "176", "hashOfConfig": "104"}, {"size": 8729, "mtime": 1750128490066, "results": "177", "hashOfConfig": "104"}, {"size": 27565, "mtime": 1750868409423, "results": "178", "hashOfConfig": "104"}, {"size": 30766, "mtime": 1751248627674, "results": "179", "hashOfConfig": "104"}, {"size": 9884, "mtime": 1750045388394, "results": "180", "hashOfConfig": "104"}, {"size": 5820, "mtime": 1750045388394, "results": "181", "hashOfConfig": "104"}, {"size": 14855, "mtime": 1751248627674, "results": "182", "hashOfConfig": "104"}, {"size": 21286, "mtime": 1750128490068, "results": "183", "hashOfConfig": "104"}, {"size": 741, "mtime": 1750045388403, "results": "184", "hashOfConfig": "104"}, {"size": 582, "mtime": 1750045388402, "results": "185", "hashOfConfig": "104"}, {"size": 491, "mtime": 1750045388401, "results": "186", "hashOfConfig": "104"}, {"size": 377, "mtime": 1750045388399, "results": "187", "hashOfConfig": "104"}, {"size": 1071, "mtime": 1750045388399, "results": "188", "hashOfConfig": "104"}, {"size": 2819, "mtime": 1751248627671, "results": "189", "hashOfConfig": "104"}, {"size": 2658, "mtime": 1750045388385, "results": "190", "hashOfConfig": "104"}, {"size": 693, "mtime": 1750045388385, "results": "191", "hashOfConfig": "104"}, {"size": 12155, "mtime": 1750861714766, "results": "192", "hashOfConfig": "104"}, {"size": 830, "mtime": 1750128490069, "results": "193", "hashOfConfig": "104"}, {"size": 636, "mtime": 1750128490073, "results": "194", "hashOfConfig": "104"}, {"size": 2015, "mtime": 1750128490072, "results": "195", "hashOfConfig": "104"}, {"size": 20680, "mtime": 1751275320131, "results": "196", "hashOfConfig": "104"}, {"size": 22180, "mtime": 1751248590566, "results": "197", "hashOfConfig": "104"}, {"size": 3385, "mtime": 1751248590568, "results": "198", "hashOfConfig": "104"}, {"size": 1177, "mtime": 1751248590567, "results": "199", "hashOfConfig": "104"}, {"size": 4760, "mtime": 1751248590568, "results": "200", "hashOfConfig": "104"}, {"size": 341, "mtime": 1751248590567, "results": "201", "hashOfConfig": "104"}, {"size": 1048, "mtime": 1751248627673, "results": "202", "hashOfConfig": "104"}, {"size": 7313, "mtime": 1751275136169, "results": "203", "hashOfConfig": "104"}, {"size": 7123, "mtime": 1751266435213, "results": "204", "hashOfConfig": "104"}, {"size": 465, "mtime": 1750045388406, "results": "205", "hashOfConfig": "104"}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xcqp28", {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\WDP301_UROOM\\Customer\\src\\index.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\reportWebVitals.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\App.js", ["512", "513", "514"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\store.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\Routes.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\Utils.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\BannedPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\ErrorPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomePage.jsx", ["515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HotelSearchPage.jsx", ["530"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\BookingCheckPage.jsx", ["531", "532", "533", "534"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomeDetailPage.jsx", ["535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentFailedPage.jsx", ["549", "550"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentPage.jsx", ["551", "552", "553", "554", "555"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentSuccessPage.jsx", ["556", "557"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\RoomDetailPage.jsx", ["558", "559", "560", "561", "562"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ChatPage.jsx", ["563", "564", "565", "566", "567", "568", "569"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ReportedFeedback.jsx", ["570", "571", "572"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\LoginPage.jsx", ["573", "574"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ForgetPasswordPage.jsx", ["575", "576", "577", "578", "579"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\RegisterPage.jsx", ["580", "581"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ResetPasswordPage.jsx", ["582", "583", "584"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodePage.jsx", ["585", "586", "587", "588", "589"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\MyAccountPage.jsx", ["590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\BookingBill.jsx", ["603", "604", "605", "606", "607", "608", "609"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\CreateFeedback.jsx", ["610", "611", "612", "613"], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\qaData.js", ["614"], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\data.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Footer.jsx", ["615", "616", "617"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Header.jsx", ["618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\MapLocation.jsx", ["630"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\saga.js", ["631", "632"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\saga.js", ["633", "634", "635", "636", "637", "638"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\saga.js", ["639"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\handleToken.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\Pagination.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservation\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ErrorModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ToastContainer.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\GoogleLogin.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\HotelClosedModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFavoriteHotel.jsx", ["640", "641"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ChangePassword.jsx", ["642", "643"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\BookingHistory.jsx", ["644", "645", "646", "647", "648", "649", "650", "651", "652"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFeedback.jsx", ["653", "654", "655", "656"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewInformation.jsx", ["657"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewAvatar.jsx", ["658", "659"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyReportFeedBack.jsx", ["660", "661", "662", "663", "664"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\RefundReservation.jsx", ["665", "666", "667", "668", "669"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\factories.js", ["670", "671", "672"], [], "E:\\WDP301_UROOM\\Customer\\src\\adapter\\ApiConstants.js", ["673", "674", "675"], [], "E:\\WDP301_UROOM\\Customer\\src\\libs\\api\\index.js", ["676"], [], "E:\\WDP301_UROOM\\Customer\\src\\libs\\firebaseConfig.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\CancelReservationModal.jsx", ["677", "678"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\refunding_reservation\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\fonts.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\services\\NotificationService.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionModal.jsx", ["679", "680", "681", "682"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyPromotion.jsx", ["683", "684", "685", "686", "687", "688", "689"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\saga.js", ["690", "691", "692"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\RoomClosedModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\hooks\\usePromotionValidation.js", ["693", "694", "695"], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\PromotionValidationHandler.jsx", ["696", "697", "698"], [], "E:\\WDP301_UROOM\\Customer\\src\\socket.js", [], [], {"ruleId": "699", "severity": 1, "message": "700", "line": 3, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 3, "endColumn": 13}, {"ruleId": "703", "severity": 1, "message": "704", "line": 46, "column": 6, "nodeType": "705", "endLine": 46, "endColumn": 17, "suggestions": "706"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 103, "column": 61, "nodeType": "709", "messageId": "710", "endLine": 103, "endColumn": 76}, {"ruleId": "699", "severity": 1, "message": "711", "line": 33, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 33, "endColumn": 14}, {"ruleId": "699", "severity": 1, "message": "712", "line": 34, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 34, "endColumn": 14}, {"ruleId": "699", "severity": 1, "message": "713", "line": 35, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 35, "endColumn": 14}, {"ruleId": "699", "severity": 1, "message": "714", "line": 63, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 63, "endColumn": 19}, {"ruleId": "699", "severity": 1, "message": "700", "line": 64, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 64, "endColumn": 13}, {"ruleId": "699", "severity": 1, "message": "715", "line": 87, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 87, "endColumn": 16}, {"ruleId": "703", "severity": 1, "message": "704", "line": 97, "column": 6, "nodeType": "705", "endLine": 97, "endColumn": 8, "suggestions": "716"}, {"ruleId": "699", "severity": 1, "message": "717", "line": 165, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 165, "endColumn": 16}, {"ruleId": "718", "severity": 1, "message": "719", "line": 638, "column": 24, "nodeType": "720", "messageId": "721", "endLine": 638, "endColumn": 26}, {"ruleId": "718", "severity": 1, "message": "722", "line": 665, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 665, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "722", "line": 675, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 675, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "722", "line": 685, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 685, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "719", "line": 907, "column": 29, "nodeType": "720", "messageId": "721", "endLine": 907, "endColumn": 31}, {"ruleId": "699", "severity": 1, "message": "723", "line": 1004, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 1004, "endColumn": 25}, {"ruleId": "718", "severity": 1, "message": "719", "line": 1203, "column": 39, "nodeType": "720", "messageId": "721", "endLine": 1203, "endColumn": 41}, {"ruleId": "724", "severity": 1, "message": "725", "line": 835, "column": 37, "nodeType": "709", "endLine": 842, "endColumn": 38}, {"ruleId": "699", "severity": 1, "message": "726", "line": 10, "column": 3, "nodeType": "701", "messageId": "702", "endLine": 10, "endColumn": 13}, {"ruleId": "699", "severity": 1, "message": "727", "line": 350, "column": 9, "nodeType": "701", "messageId": "702", "endLine": 350, "endColumn": 23}, {"ruleId": "724", "severity": 1, "message": "725", "line": 502, "column": 21, "nodeType": "709", "endLine": 506, "endColumn": 22}, {"ruleId": "724", "severity": 1, "message": "725", "line": 540, "column": 23, "nodeType": "709", "endLine": 554, "endColumn": 24}, {"ruleId": "699", "severity": 1, "message": "728", "line": 6, "column": 3, "nodeType": "701", "messageId": "702", "endLine": 6, "endColumn": 8}, {"ruleId": "699", "severity": 1, "message": "729", "line": 158, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 158, "endColumn": 21}, {"ruleId": "699", "severity": 1, "message": "730", "line": 158, "column": 23, "nodeType": "701", "messageId": "702", "endLine": 158, "endColumn": 37}, {"ruleId": "703", "severity": 1, "message": "731", "line": 179, "column": 6, "nodeType": "705", "endLine": 179, "endColumn": 8, "suggestions": "732"}, {"ruleId": "703", "severity": 1, "message": "733", "line": 314, "column": 6, "nodeType": "705", "endLine": 314, "endColumn": 25, "suggestions": "734"}, {"ruleId": "699", "severity": 1, "message": "735", "line": 864, "column": 9, "nodeType": "701", "messageId": "702", "endLine": 864, "endColumn": 15}, {"ruleId": "718", "severity": 1, "message": "719", "line": 957, "column": 28, "nodeType": "720", "messageId": "721", "endLine": 957, "endColumn": 30}, {"ruleId": "724", "severity": 1, "message": "725", "line": 1069, "column": 19, "nodeType": "709", "endLine": 1080, "endColumn": 20}, {"ruleId": "736", "severity": 1, "message": "737", "line": 1128, "column": 63, "nodeType": "738", "messageId": "739", "endLine": 1128, "endColumn": 65}, {"ruleId": "718", "severity": 1, "message": "719", "line": 1762, "column": 32, "nodeType": "720", "messageId": "721", "endLine": 1762, "endColumn": 34}, {"ruleId": "718", "severity": 1, "message": "722", "line": 2059, "column": 39, "nodeType": "720", "messageId": "721", "endLine": 2059, "endColumn": 41}, {"ruleId": "718", "severity": 1, "message": "719", "line": 2239, "column": 42, "nodeType": "720", "messageId": "721", "endLine": 2239, "endColumn": 44}, {"ruleId": "724", "severity": 1, "message": "725", "line": 2388, "column": 13, "nodeType": "709", "endLine": 2388, "endColumn": 41}, {"ruleId": "724", "severity": 1, "message": "725", "line": 2389, "column": 32, "nodeType": "709", "endLine": 2389, "endColumn": 60}, {"ruleId": "699", "severity": 1, "message": "740", "line": 1, "column": 17, "nodeType": "701", "messageId": "702", "endLine": 1, "endColumn": 26}, {"ruleId": "699", "severity": 1, "message": "741", "line": 10, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 10, "endColumn": 17}, {"ruleId": "703", "severity": 1, "message": "742", "line": 35, "column": 5, "nodeType": "705", "endLine": 35, "endColumn": 7, "suggestions": "743"}, {"ruleId": "703", "severity": 1, "message": "744", "line": 55, "column": 6, "nodeType": "705", "endLine": 55, "endColumn": 16, "suggestions": "745"}, {"ruleId": "703", "severity": 1, "message": "746", "line": 94, "column": 6, "nodeType": "705", "endLine": 94, "endColumn": 8, "suggestions": "747"}, {"ruleId": "703", "severity": 1, "message": "748", "line": 107, "column": 6, "nodeType": "705", "endLine": 107, "endColumn": 26, "suggestions": "749"}, {"ruleId": "750", "severity": 1, "message": "751", "line": 190, "column": 19, "nodeType": "709", "endLine": 190, "endColumn": 40}, {"ruleId": "699", "severity": 1, "message": "752", "line": 6, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 6, "endColumn": 21}, {"ruleId": "703", "severity": 1, "message": "753", "line": 35, "column": 6, "nodeType": "705", "endLine": 35, "endColumn": 31, "suggestions": "754"}, {"ruleId": "718", "severity": 1, "message": "722", "line": 650, "column": 38, "nodeType": "720", "messageId": "721", "endLine": 650, "endColumn": 40}, {"ruleId": "699", "severity": 1, "message": "755", "line": 667, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 667, "endColumn": 29}, {"ruleId": "718", "severity": 1, "message": "719", "line": 907, "column": 18, "nodeType": "720", "messageId": "721", "endLine": 907, "endColumn": 20}, {"ruleId": "699", "severity": 1, "message": "756", "line": 944, "column": 9, "nodeType": "701", "messageId": "702", "endLine": 944, "endColumn": 19}, {"ruleId": "718", "severity": 1, "message": "722", "line": 1366, "column": 46, "nodeType": "720", "messageId": "721", "endLine": 1366, "endColumn": 48}, {"ruleId": "699", "severity": 1, "message": "757", "line": 35, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 35, "endColumn": 20}, {"ruleId": "703", "severity": 1, "message": "758", "line": 83, "column": 6, "nodeType": "705", "endLine": 83, "endColumn": 8, "suggestions": "759"}, {"ruleId": "703", "severity": 1, "message": "760", "line": 87, "column": 6, "nodeType": "705", "endLine": 87, "endColumn": 20, "suggestions": "761"}, {"ruleId": "718", "severity": 1, "message": "722", "line": 121, "column": 29, "nodeType": "720", "messageId": "721", "endLine": 121, "endColumn": 31}, {"ruleId": "703", "severity": 1, "message": "758", "line": 144, "column": 6, "nodeType": "705", "endLine": 144, "endColumn": 44, "suggestions": "762"}, {"ruleId": "718", "severity": 1, "message": "722", "line": 429, "column": 46, "nodeType": "720", "messageId": "721", "endLine": 429, "endColumn": 48}, {"ruleId": "718", "severity": 1, "message": "722", "line": 442, "column": 38, "nodeType": "720", "messageId": "721", "endLine": 442, "endColumn": 40}, {"ruleId": "699", "severity": 1, "message": "763", "line": 22, "column": 9, "nodeType": "701", "messageId": "702", "endLine": 22, "endColumn": 13}, {"ruleId": "724", "severity": 1, "message": "725", "line": 249, "column": 29, "nodeType": "709", "endLine": 253, "endColumn": 30}, {"ruleId": "724", "severity": 1, "message": "725", "line": 258, "column": 29, "nodeType": "709", "endLine": 266, "endColumn": 30}, {"ruleId": "699", "severity": 1, "message": "764", "line": 4, "column": 29, "nodeType": "701", "messageId": "702", "endLine": 4, "endColumn": 40}, {"ruleId": "724", "severity": 1, "message": "725", "line": 243, "column": 17, "nodeType": "709", "endLine": 247, "endColumn": 52}, {"ruleId": "699", "severity": 1, "message": "764", "line": 4, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 4, "endColumn": 21}, {"ruleId": "699", "severity": 1, "message": "765", "line": 10, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 10, "endColumn": 19}, {"ruleId": "699", "severity": 1, "message": "766", "line": 12, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 12, "endColumn": 13}, {"ruleId": "699", "severity": 1, "message": "767", "line": 15, "column": 9, "nodeType": "701", "messageId": "702", "endLine": 15, "endColumn": 17}, {"ruleId": "699", "severity": 1, "message": "768", "line": 63, "column": 9, "nodeType": "701", "messageId": "702", "endLine": 63, "endColumn": 33}, {"ruleId": "699", "severity": 1, "message": "764", "line": 4, "column": 29, "nodeType": "701", "messageId": "702", "endLine": 4, "endColumn": 40}, {"ruleId": "724", "severity": 1, "message": "725", "line": 215, "column": 17, "nodeType": "709", "endLine": 219, "endColumn": 52}, {"ruleId": "699", "severity": 1, "message": "764", "line": 4, "column": 29, "nodeType": "701", "messageId": "702", "endLine": 4, "endColumn": 40}, {"ruleId": "699", "severity": 1, "message": "728", "line": 6, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 6, "endColumn": 15}, {"ruleId": "699", "severity": 1, "message": "766", "line": 9, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 9, "endColumn": 13}, {"ruleId": "699", "severity": 1, "message": "764", "line": 3, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 3, "endColumn": 21}, {"ruleId": "699", "severity": 1, "message": "766", "line": 10, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 10, "endColumn": 13}, {"ruleId": "699", "severity": 1, "message": "769", "line": 22, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 22, "endColumn": 19}, {"ruleId": "699", "severity": 1, "message": "770", "line": 27, "column": 17, "nodeType": "701", "messageId": "702", "endLine": 27, "endColumn": 25}, {"ruleId": "724", "severity": 1, "message": "771", "line": 203, "column": 17, "nodeType": "709", "endLine": 203, "endColumn": 89}, {"ruleId": "699", "severity": 1, "message": "740", "line": 1, "column": 17, "nodeType": "701", "messageId": "702", "endLine": 1, "endColumn": 26}, {"ruleId": "699", "severity": 1, "message": "772", "line": 1, "column": 28, "nodeType": "701", "messageId": "702", "endLine": 1, "endColumn": 36}, {"ruleId": "699", "severity": 1, "message": "752", "line": 25, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 25, "endColumn": 21}, {"ruleId": "773", "severity": 1, "message": "774", "line": 71, "column": 21, "nodeType": "709", "endLine": 83, "endColumn": 23}, {"ruleId": "718", "severity": 1, "message": "722", "line": 96, "column": 39, "nodeType": "720", "messageId": "721", "endLine": 96, "endColumn": 41}, {"ruleId": "718", "severity": 1, "message": "722", "line": 114, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 114, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "722", "line": 115, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 115, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "722", "line": 117, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 117, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "722", "line": 118, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 118, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "722", "line": 119, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 119, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "722", "line": 120, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 120, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "722", "line": 121, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 121, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "722", "line": 122, "column": 26, "nodeType": "720", "messageId": "721", "endLine": 122, "endColumn": 28}, {"ruleId": "699", "severity": 1, "message": "775", "line": 19, "column": 3, "nodeType": "701", "messageId": "702", "endLine": 19, "endColumn": 10}, {"ruleId": "699", "severity": 1, "message": "776", "line": 20, "column": 3, "nodeType": "701", "messageId": "702", "endLine": 20, "endColumn": 13}, {"ruleId": "699", "severity": 1, "message": "777", "line": 52, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 52, "endColumn": 20}, {"ruleId": "699", "severity": 1, "message": "778", "line": 59, "column": 15, "nodeType": "701", "messageId": "702", "endLine": 59, "endColumn": 21}, {"ruleId": "699", "severity": 1, "message": "779", "line": 60, "column": 15, "nodeType": "701", "messageId": "702", "endLine": 60, "endColumn": 25}, {"ruleId": "703", "severity": 1, "message": "780", "line": 75, "column": 6, "nodeType": "705", "endLine": 75, "endColumn": 10, "suggestions": "781"}, {"ruleId": "703", "severity": 1, "message": "782", "line": 84, "column": 6, "nodeType": "705", "endLine": 84, "endColumn": 25, "suggestions": "783"}, {"ruleId": "699", "severity": 1, "message": "784", "line": 39, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 39, "endColumn": 26}, {"ruleId": "699", "severity": 1, "message": "785", "line": 39, "column": 28, "nodeType": "701", "messageId": "702", "endLine": 39, "endColumn": 47}, {"ruleId": "703", "severity": 1, "message": "780", "line": 235, "column": 6, "nodeType": "705", "endLine": 235, "endColumn": 21, "suggestions": "786"}, {"ruleId": "703", "severity": 1, "message": "787", "line": 277, "column": 6, "nodeType": "705", "endLine": 277, "endColumn": 49, "suggestions": "788"}, {"ruleId": "699", "severity": 1, "message": "789", "line": 78, "column": 27, "nodeType": "701", "messageId": "702", "endLine": 78, "endColumn": 45}, {"ruleId": "724", "severity": 1, "message": "771", "line": 32, "column": 15, "nodeType": "709", "endLine": 52, "endColumn": 16}, {"ruleId": "718", "severity": 1, "message": "719", "line": 35, "column": 33, "nodeType": "720", "messageId": "721", "endLine": 35, "endColumn": 35}, {"ruleId": "724", "severity": 1, "message": "771", "line": 55, "column": 15, "nodeType": "709", "endLine": 55, "endColumn": 27}, {"ruleId": "699", "severity": 1, "message": "790", "line": 17, "column": 66, "nodeType": "701", "messageId": "702", "endLine": 17, "endColumn": 72}, {"ruleId": "699", "severity": 1, "message": "791", "line": 34, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 34, "endColumn": 16}, {"ruleId": "699", "severity": 1, "message": "792", "line": 34, "column": 18, "nodeType": "701", "messageId": "702", "endLine": 34, "endColumn": 27}, {"ruleId": "718", "severity": 1, "message": "719", "line": 256, "column": 32, "nodeType": "720", "messageId": "721", "endLine": 256, "endColumn": 34}, {"ruleId": "718", "severity": 1, "message": "719", "line": 268, "column": 32, "nodeType": "720", "messageId": "721", "endLine": 268, "endColumn": 34}, {"ruleId": "718", "severity": 1, "message": "719", "line": 280, "column": 32, "nodeType": "720", "messageId": "721", "endLine": 280, "endColumn": 34}, {"ruleId": "718", "severity": 1, "message": "719", "line": 292, "column": 32, "nodeType": "720", "messageId": "721", "endLine": 292, "endColumn": 34}, {"ruleId": "718", "severity": 1, "message": "719", "line": 304, "column": 32, "nodeType": "720", "messageId": "721", "endLine": 304, "endColumn": 34}, {"ruleId": "718", "severity": 1, "message": "719", "line": 316, "column": 32, "nodeType": "720", "messageId": "721", "endLine": 316, "endColumn": 34}, {"ruleId": "724", "severity": 1, "message": "725", "line": 479, "column": 21, "nodeType": "709", "endLine": 487, "endColumn": 22}, {"ruleId": "718", "severity": 1, "message": "719", "line": 492, "column": 42, "nodeType": "720", "messageId": "721", "endLine": 492, "endColumn": 44}, {"ruleId": "718", "severity": 1, "message": "719", "line": 492, "column": 68, "nodeType": "720", "messageId": "721", "endLine": 492, "endColumn": 70}, {"ruleId": "703", "severity": 1, "message": "793", "line": 47, "column": 6, "nodeType": "705", "endLine": 47, "endColumn": 8, "suggestions": "794"}, {"ruleId": "699", "severity": 1, "message": "795", "line": 8, "column": 40, "nodeType": "701", "messageId": "702", "endLine": 8, "endColumn": 48}, {"ruleId": "699", "severity": 1, "message": "796", "line": 8, "column": 50, "nodeType": "701", "messageId": "702", "endLine": 8, "endColumn": 57}, {"ruleId": "699", "severity": 1, "message": "797", "line": 1, "column": 14, "nodeType": "701", "messageId": "702", "endLine": 1, "endColumn": 18}, {"ruleId": "699", "severity": 1, "message": "798", "line": 1, "column": 20, "nodeType": "701", "messageId": "702", "endLine": 1, "endColumn": 24}, {"ruleId": "699", "severity": 1, "message": "799", "line": 1, "column": 26, "nodeType": "701", "messageId": "702", "endLine": 1, "endColumn": 29}, {"ruleId": "699", "severity": 1, "message": "800", "line": 1, "column": 31, "nodeType": "701", "messageId": "702", "endLine": 1, "endColumn": 40}, {"ruleId": "699", "severity": 1, "message": "765", "line": 2, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 2, "endColumn": 19}, {"ruleId": "699", "severity": 1, "message": "741", "line": 3, "column": 8, "nodeType": "701", "messageId": "702", "endLine": 3, "endColumn": 17}, {"ruleId": "699", "severity": 1, "message": "796", "line": 72, "column": 44, "nodeType": "701", "messageId": "702", "endLine": 72, "endColumn": 51}, {"ruleId": "703", "severity": 1, "message": "801", "line": 101, "column": 6, "nodeType": "705", "endLine": 101, "endColumn": 12, "suggestions": "802"}, {"ruleId": "703", "severity": 1, "message": "803", "line": 136, "column": 6, "nodeType": "705", "endLine": 136, "endColumn": 46, "suggestions": "804"}, {"ruleId": "699", "severity": 1, "message": "805", "line": 2, "column": 3, "nodeType": "701", "messageId": "702", "endLine": 2, "endColumn": 12}, {"ruleId": "699", "severity": 1, "message": "726", "line": 8, "column": 3, "nodeType": "701", "messageId": "702", "endLine": 8, "endColumn": 13}, {"ruleId": "718", "severity": 1, "message": "722", "line": 49, "column": 18, "nodeType": "720", "messageId": "721", "endLine": 49, "endColumn": 20}, {"ruleId": "699", "severity": 1, "message": "806", "line": 53, "column": 24, "nodeType": "701", "messageId": "702", "endLine": 53, "endColumn": 39}, {"ruleId": "703", "severity": 1, "message": "807", "line": 122, "column": 6, "nodeType": "705", "endLine": 122, "endColumn": 16, "suggestions": "808"}, {"ruleId": "703", "severity": 1, "message": "809", "line": 211, "column": 6, "nodeType": "705", "endLine": 211, "endColumn": 46, "suggestions": "810"}, {"ruleId": "703", "severity": 1, "message": "811", "line": 260, "column": 6, "nodeType": "705", "endLine": 260, "endColumn": 32, "suggestions": "812"}, {"ruleId": "813", "severity": 1, "message": "814", "line": 309, "column": 12, "nodeType": "701", "messageId": "815", "endLine": 309, "endColumn": 25}, {"ruleId": "699", "severity": 1, "message": "816", "line": 339, "column": 9, "nodeType": "701", "messageId": "702", "endLine": 339, "endColumn": 30}, {"ruleId": "699", "severity": 1, "message": "817", "line": 379, "column": 9, "nodeType": "701", "messageId": "702", "endLine": 379, "endColumn": 28}, {"ruleId": "718", "severity": 1, "message": "722", "line": 574, "column": 37, "nodeType": "720", "messageId": "721", "endLine": 574, "endColumn": 39}, {"ruleId": "703", "severity": 1, "message": "818", "line": 93, "column": 6, "nodeType": "705", "endLine": 93, "endColumn": 40, "suggestions": "819"}, {"ruleId": "703", "severity": 1, "message": "820", "line": 98, "column": 6, "nodeType": "705", "endLine": 98, "endColumn": 40, "suggestions": "821"}, {"ruleId": "703", "severity": 1, "message": "822", "line": 184, "column": 6, "nodeType": "705", "endLine": 184, "endColumn": 41, "suggestions": "823"}, {"ruleId": "699", "severity": 1, "message": "824", "line": 372, "column": 9, "nodeType": "701", "messageId": "702", "endLine": 372, "endColumn": 19}, {"ruleId": "699", "severity": 1, "message": "740", "line": 2, "column": 17, "nodeType": "701", "messageId": "702", "endLine": 2, "endColumn": 26}, {"ruleId": "718", "severity": 1, "message": "719", "line": 67, "column": 31, "nodeType": "720", "messageId": "721", "endLine": 67, "endColumn": 33}, {"ruleId": "718", "severity": 1, "message": "719", "line": 141, "column": 33, "nodeType": "720", "messageId": "721", "endLine": 141, "endColumn": 35}, {"ruleId": "699", "severity": 1, "message": "825", "line": 17, "column": 26, "nodeType": "701", "messageId": "702", "endLine": 17, "endColumn": 27}, {"ruleId": "699", "severity": 1, "message": "826", "line": 17, "column": 29, "nodeType": "701", "messageId": "702", "endLine": 17, "endColumn": 35}, {"ruleId": "699", "severity": 1, "message": "827", "line": 17, "column": 37, "nodeType": "701", "messageId": "702", "endLine": 17, "endColumn": 42}, {"ruleId": "703", "severity": 1, "message": "828", "line": 36, "column": 6, "nodeType": "705", "endLine": 36, "endColumn": 16, "suggestions": "829"}, {"ruleId": "718", "severity": 1, "message": "722", "line": 226, "column": 33, "nodeType": "720", "messageId": "721", "endLine": 226, "endColumn": 35}, {"ruleId": "699", "severity": 1, "message": "830", "line": 2, "column": 52, "nodeType": "701", "messageId": "702", "endLine": 2, "endColumn": 57}, {"ruleId": "699", "severity": 1, "message": "831", "line": 20, "column": 9, "nodeType": "701", "messageId": "702", "endLine": 20, "endColumn": 13}, {"ruleId": "703", "severity": 1, "message": "832", "line": 67, "column": 6, "nodeType": "705", "endLine": 67, "endColumn": 30, "suggestions": "833"}, {"ruleId": "718", "severity": 1, "message": "722", "line": 353, "column": 36, "nodeType": "720", "messageId": "721", "endLine": 353, "endColumn": 38}, {"ruleId": "718", "severity": 1, "message": "722", "line": 361, "column": 36, "nodeType": "720", "messageId": "721", "endLine": 361, "endColumn": 38}, {"ruleId": "718", "severity": 1, "message": "719", "line": 12, "column": 33, "nodeType": "720", "messageId": "721", "endLine": 12, "endColumn": 35}, {"ruleId": "718", "severity": 1, "message": "719", "line": 15, "column": 37, "nodeType": "720", "messageId": "721", "endLine": 15, "endColumn": 39}, {"ruleId": "718", "severity": 1, "message": "719", "line": 18, "column": 33, "nodeType": "720", "messageId": "721", "endLine": 18, "endColumn": 35}, {"ruleId": "834", "severity": 1, "message": "835", "line": 10, "column": 3, "nodeType": "836", "messageId": "721", "endLine": 10, "endColumn": 17}, {"ruleId": "834", "severity": 1, "message": "837", "line": 11, "column": 3, "nodeType": "836", "messageId": "721", "endLine": 11, "endColumn": 18}, {"ruleId": "834", "severity": 1, "message": "838", "line": 48, "column": 3, "nodeType": "836", "messageId": "721", "endLine": 48, "endColumn": 28}, {"ruleId": "839", "severity": 1, "message": "840", "line": 37, "column": 1, "nodeType": "841", "endLine": 108, "endColumn": 3}, {"ruleId": "703", "severity": 1, "message": "842", "line": 105, "column": 6, "nodeType": "705", "endLine": 105, "endColumn": 8, "suggestions": "843"}, {"ruleId": "718", "severity": 1, "message": "722", "line": 227, "column": 49, "nodeType": "720", "messageId": "721", "endLine": 227, "endColumn": 51}, {"ruleId": "699", "severity": 1, "message": "844", "line": 11, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 11, "endColumn": 27}, {"ruleId": "699", "severity": 1, "message": "845", "line": 11, "column": 29, "nodeType": "701", "messageId": "702", "endLine": 11, "endColumn": 49}, {"ruleId": "703", "severity": 1, "message": "846", "line": 18, "column": 6, "nodeType": "705", "endLine": 18, "endColumn": 24, "suggestions": "847"}, {"ruleId": "699", "severity": 1, "message": "848", "line": 163, "column": 15, "nodeType": "701", "messageId": "702", "endLine": 163, "endColumn": 24}, {"ruleId": "699", "severity": 1, "message": "849", "line": 2, "column": 41, "nodeType": "701", "messageId": "702", "endLine": 2, "endColumn": 48}, {"ruleId": "699", "severity": 1, "message": "850", "line": 3, "column": 10, "nodeType": "701", "messageId": "702", "endLine": 3, "endColumn": 15}, {"ruleId": "699", "severity": 1, "message": "851", "line": 3, "column": 68, "nodeType": "701", "messageId": "702", "endLine": 3, "endColumn": 76}, {"ruleId": "699", "severity": 1, "message": "852", "line": 3, "column": 78, "nodeType": "701", "messageId": "702", "endLine": 3, "endColumn": 84}, {"ruleId": "703", "severity": 1, "message": "846", "line": 74, "column": 6, "nodeType": "705", "endLine": 74, "endColumn": 16, "suggestions": "853"}, {"ruleId": "703", "severity": 1, "message": "854", "line": 88, "column": 6, "nodeType": "705", "endLine": 88, "endColumn": 39, "suggestions": "855"}, {"ruleId": "699", "severity": 1, "message": "856", "line": 304, "column": 32, "nodeType": "701", "messageId": "702", "endLine": 304, "endColumn": 50}, {"ruleId": "699", "severity": 1, "message": "857", "line": 8, "column": 13, "nodeType": "701", "messageId": "702", "endLine": 8, "endColumn": 17}, {"ruleId": "699", "severity": 1, "message": "858", "line": 8, "column": 19, "nodeType": "701", "messageId": "702", "endLine": 8, "endColumn": 24}, {"ruleId": "699", "severity": 1, "message": "859", "line": 8, "column": 42, "nodeType": "701", "messageId": "702", "endLine": 8, "endColumn": 48}, {"ruleId": "703", "severity": 1, "message": "860", "line": 58, "column": 6, "nodeType": "705", "endLine": 58, "endColumn": 16, "suggestions": "861"}, {"ruleId": "699", "severity": 1, "message": "862", "line": 95, "column": 26, "nodeType": "701", "messageId": "702", "endLine": 95, "endColumn": 42}, {"ruleId": "699", "severity": 1, "message": "863", "line": 103, "column": 26, "nodeType": "701", "messageId": "702", "endLine": 103, "endColumn": 39}, {"ruleId": "699", "severity": 1, "message": "740", "line": 1, "column": 27, "nodeType": "701", "messageId": "702", "endLine": 1, "endColumn": 36}, {"ruleId": "699", "severity": 1, "message": "864", "line": 3, "column": 33, "nodeType": "701", "messageId": "702", "endLine": 3, "endColumn": 46}, {"ruleId": "699", "severity": 1, "message": "865", "line": 3, "column": 48, "nodeType": "701", "messageId": "702", "endLine": 3, "endColumn": 55}, "no-unused-vars", "'Utils' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["866"], "react/jsx-pascal-case", "Imported JSX component Home_detail must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "'image4' is defined but never used.", "'image5' is defined but never used.", "'image6' is defined but never used.", "'RoomActions' is defined but never used.", "'hotels' is assigned a value but never used.", ["867"], "'errors' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "Expected '===' and instead saw '=='.", "'CustomerReviews' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'InputGroup' is defined but never used.", "'formatCurrency' is assigned a value but never used.", "'Route' is defined but never used.", "'isSearching' is assigned a value but never used.", "'setIsSearching' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleBackToBooking'. Either include it or remove the dependency array.", ["868"], "React Hook useEffect has a missing dependency: 'searchParams'. Either include it or remove the dependency array.", ["869"], "'status' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "'useEffect' is defined but never used.", "'Factories' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReservation'. Either include it or remove the dependency array.", ["870"], "React Hook useEffect has missing dependencies: 'createdAt', 'idReservation', 'messageError', 'messageSuccess', 'navigate', and 'totalPrice'. Either include them or remove the dependency array.", ["871"], "React Hook useEffect has a missing dependency: 'handleAccept'. Either include it or remove the dependency array.", ["872"], "React Hook useEffect has a missing dependency: 'handleDelete'. Either include it or remove the dependency array.", ["873"], "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'useLocation' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleConfirm'. Either include it or remove the dependency array.", ["874"], "'currentServiceIndex' is assigned a value but never used.", "'totalPrice' is assigned a value but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["875"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["876"], ["877"], "'User' is assigned a value but never used.", "'FaArrowLeft' is defined but never used.", "'AuthActions' is defined but never used.", "'axios' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'useState' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'FaPhone' is defined but never used.", "'FaEnvelope' is defined but never used.", "'fontLoaded' is assigned a value but never used.", "'pdfLib' is assigned a value but never used.", "'pdfFontLib' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchReservationDetail'. Either include it or remove the dependency array.", ["878"], "React Hook useEffect has a missing dependency: 'fetchHotelDetails'. Either include it or remove the dependency array.", ["879"], "'userReservations' is assigned a value but never used.", "'setUserReservations' is assigned a value but never used.", ["880"], "React Hook useEffect has missing dependencies: 'dispatch' and 'fetchHotelDetails'. Either include them or remove the dependency array.", ["881"], "'setSearchParamsObj' is assigned a value but never used.", "'FaUser' is defined but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'addressMap'. Either include it or remove the dependency array.", ["882"], "'onFailed' is assigned a value but never used.", "'onError' is assigned a value but never used.", "'call' is defined but never used.", "'fork' is defined but never used.", "'put' is defined but never used.", "'takeEvery' is defined but never used.", "React Hook useEffect has a missing dependency: 'activePage'. Either include it or remove the dependency array.", ["883"], "React Hook useEffect has missing dependencies: 'activePage' and 'updateURL'. Either include them or remove the dependency array.", ["884"], "'Container' is defined but never used.", "'setItemsPerPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserReservations'. Either include it or remove the dependency array.", ["885"], "React Hook useEffect has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["886"], "React Hook useEffect has missing dependencies: 'activePage' and 'totalPages'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setActivePage' needs the current value of 'totalPages'.", ["887"], "no-redeclare", "'parseCurrency' is already defined.", "redeclared", "'calculateRefundPolicy' is assigned a value but never used.", "'calculateTotalPrice' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'activePage', 'sortOption', and 'starFilter'. Either include them or remove the dependency array.", ["888"], "React Hook useEffect has a missing dependency: 'fetchUserFeedbacks'. Either include it or remove the dependency array.", ["889"], "React Hook useEffect has missing dependencies: 'activePage', 'getFilteredFeedbacks', and 'updateURL'. Either include them or remove the dependency array.", ["890"], "'formatDate' is assigned a value but never used.", "'X' is defined but never used.", "'Pencil' is defined but never used.", "'Trash' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUserReports'. Either include it or remove the dependency array.", ["891"], "'Toast' is defined but never used.", "'Auth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["892"], "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has missing dependencies: 'calculateRefundPolicy' and 'setRefundAmount'. Either include them or remove the dependency array. If 'setRefundAmount' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["893"], "'selectedPromotion' is assigned a value but never used.", "'setSelectedPromotion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPromotions'. Either include it or remove the dependency array.", ["894"], "'startDate' is assigned a value but never used.", "'Spinner' is defined but never used.", "'FaTag' is defined but never used.", "'FaFilter' is defined but never used.", "'FaSync' is defined but never used.", ["895"], "React Hook useEffect has missing dependencies: 'getFilteredPromotions' and 'updateURL'. Either include them or remove the dependency array.", ["896"], "'totalFilteredCount' is assigned a value but never used.", "'page' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'userId' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleExpirationWarning', 'handlePromotionAlternatives', 'handlePromotionStatusChange', 'handleUsageWarning', 'handleValidationFailure', and 'handleValidationResult'. Either include them or remove the dependency array.", ["897"], "'minutesRemaining' is assigned a value but never used.", "'remainingUses' is assigned a value but never used.", "'FaCheckCircle' is defined but never used.", "'FaTimes' is defined but never used.", {"desc": "898", "fix": "899"}, {"desc": "900", "fix": "901"}, {"desc": "902", "fix": "903"}, {"desc": "904", "fix": "905"}, {"desc": "906", "fix": "907"}, {"desc": "908", "fix": "909"}, {"desc": "910", "fix": "911"}, {"desc": "912", "fix": "913"}, {"desc": "914", "fix": "915"}, {"desc": "916", "fix": "917"}, {"desc": "918", "fix": "919"}, {"desc": "920", "fix": "921"}, {"desc": "922", "fix": "923"}, {"desc": "924", "fix": "925"}, {"desc": "926", "fix": "927"}, {"desc": "928", "fix": "929"}, {"desc": "930", "fix": "931"}, {"desc": "932", "fix": "933"}, {"desc": "934", "fix": "935"}, {"desc": "936", "fix": "937"}, {"desc": "938", "fix": "939"}, {"desc": "940", "fix": "941"}, {"desc": "942", "fix": "943"}, {"desc": "944", "fix": "945"}, {"desc": "946", "fix": "947"}, {"desc": "948", "fix": "949"}, {"desc": "950", "fix": "951"}, {"desc": "952", "fix": "953"}, {"desc": "954", "fix": "955"}, {"desc": "956", "fix": "957"}, {"desc": "958", "fix": "959"}, {"desc": "960", "fix": "961"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "962", "text": "963"}, "Update the dependencies array to be: [dispatch]", {"range": "964", "text": "965"}, "Update the dependencies array to be: [handleBackToBooking]", {"range": "966", "text": "967"}, "Update the dependencies array to be: [hotelId, dispatch, searchParams]", {"range": "968", "text": "969"}, "Update the dependencies array to be: [fetchReservation]", {"range": "970", "text": "971"}, "Update the dependencies array to be: [createdAt, idReservation, location, messageError, messageSuccess, navigate, totalPrice]", {"range": "972", "text": "973"}, "Update the dependencies array to be: [handleAccept]", {"range": "974", "text": "975"}, "Update the dependencies array to be: [timeLeft, navigate, handleDelete]", {"range": "976", "text": "977"}, "Update the dependencies array to be: [reservationId, navigate, handleConfirm]", {"range": "978", "text": "979"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "980", "text": "981"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "982", "text": "983"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "984", "text": "985"}, "Update the dependencies array to be: [fetchReservationDetail, id]", {"range": "986", "text": "987"}, "Update the dependencies array to be: [fetchHotelDetails, reservationDetail]", {"range": "988", "text": "989"}, "Update the dependencies array to be: [fetchReservationDetail, reservationId]", {"range": "990", "text": "991"}, "Update the dependencies array to be: [Auth, reservationId, hotelId, hotelDetail, dispatch, fetchHotelDetails]", {"range": "992", "text": "993"}, "Update the dependencies array to be: [addressMap]", {"range": "994", "text": "995"}, "Update the dependencies array to be: [activePage, page]", {"range": "996", "text": "997"}, "Update the dependencies array to be: [dispatch, Auth?.favorites, paramsQuery, activePage, updateURL]", {"range": "998", "text": "999"}, "Update the dependencies array to be: [dispatch, fetchUserReservations]", {"range": "1000", "text": "1001"}, "Update the dependencies array to be: [activeFilter, reservations, dateFilter, filters]", {"range": "1002", "text": "1003"}, "Update the dependencies array to be: [activePage, filterBill, itemsPerPage, totalPages]", {"range": "1004", "text": "1005"}, "Update the dependencies array to be: [activePage, pageParam, sortOption, sortParam, starFilter, starsParam]", {"range": "1006", "text": "1007"}, "Update the dependencies array to be: [dispatch, fetchUserFeedbacks, sortOption, starFilter]", {"range": "1008", "text": "1009"}, "Update the dependencies array to be: [feedbacks, starFilter, sortOption, getFilteredFeedbacks, activePage, updateURL]", {"range": "1010", "text": "1011"}, "Update the dependencies array to be: [Auth._id, fetchUserReports]", {"range": "1012", "text": "1013"}, "Update the dependencies array to be: [dispatch, activeStatus, fetchData]", {"range": "1014", "text": "1015"}, "Update the dependencies array to be: [calculateRefundPolicy, setRefundAmount]", {"range": "1016", "text": "1017"}, "Update the dependencies array to be: [fetchPromotions, show, totalPrice]", {"range": "1018", "text": "1019"}, "Update the dependencies array to be: [dispatch, fetchPromotions]", {"range": "1020", "text": "1021"}, "Update the dependencies array to be: [promotions, filters, activePage, getFilteredPromotions, updateURL]", {"range": "1022", "text": "1023"}, "Update the dependencies array to be: [Auth._id, handleExpirationWarning, handlePromotionAlternatives, handlePromotionStatusChange, handleUsageWarning, handleValidationFailure, handleValidationResult]", {"range": "1024", "text": "1025"}, [2163, 2174], "[Auth?._id, dispatch]", [3226, 3228], "[dispatch]", [6150, 6152], "[handleBackToBooking]", [9886, 9905], "[hotelId, dispatch, searchParams]", [1405, 1407], "[fetchReservation]", [1841, 1851], "[createdAt, idReservation, location, messageError, messageSuccess, navigate, totalPrice]", [3122, 3124], "[handleAccept]", [3447, 3467], "[timeLeft, navigate, handleDelete]", [1197, 1222], "[reservationId, navigate, handleConfirm]", [2913, 2915], "[fetchAllUser]", [2972, 2986], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4575, 4613], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]", [2124, 2128], "[fetchReservationDetail, id]", [2328, 2347], "[fetchHotelDetails, reservationDetail]", [7500, 7515], "[fetchReservationDetail, reservationId]", [9032, 9075], "[Auth, reservationId, hotelId, hotelDetail, dispatch, fetchHotelDetails]", [1416, 1418], "[addressMap]", [3659, 3665], "[activePage, page]", [4664, 4704], "[dispatch, Auth?.favorites, paramsQuery, activePage, updateURL]", [4692, 4702], "[dispatch, fetchUserReservations]", [7726, 7766], "[activeFilter, reservations, dateFilter, filters]", [9200, 9226], "[activePage, filterBill, itemsPerPage, totalPages]", [3136, 3170], "[activePage, pageParam, sortOption, sortParam, starFilter, starsParam]", [3276, 3310], "[dispatch, fetchUserFeedbacks, sortOption, starFilter]", [6005, 6040], "[feedbacks, starFilter, sortOption, getFilteredFeedbacks, activePage, updateURL]", [1363, 1373], "[Auth._id, fetchUserReports]", [2318, 2342], "[dispatch, activeStatus, fetchData]", [3197, 3199], "[calculateRefundPolicy, setRefundAmount]", [724, 742], "[fetchPromotions, show, totalPrice]", [2718, 2728], "[dispatch, fetchPromotions]", [3198, 3231], "[promotions, filters, activePage, getFilteredPromotions, updateURL]", [2410, 2420], "[Auth._id, handleExpirationWarning, handlePromotionAlternatives, handlePromotionStatusChange, handleUsageWarning, handleValidationFailure, handleValidationResult]"]