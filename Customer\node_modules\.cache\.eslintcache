[{"E:\\WDP301_UROOM\\Customer\\src\\index.js": "1", "E:\\WDP301_UROOM\\Customer\\src\\reportWebVitals.js": "2", "E:\\WDP301_UROOM\\Customer\\src\\App.js": "3", "E:\\WDP301_UROOM\\Customer\\src\\redux\\store.js": "4", "E:\\WDP301_UROOM\\Customer\\src\\utils\\Routes.js": "5", "E:\\WDP301_UROOM\\Customer\\src\\redux\\socket\\socketSlice.js": "6", "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-reducer.js": "7", "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-saga.js": "8", "E:\\WDP301_UROOM\\Customer\\src\\utils\\Utils.js": "9", "E:\\WDP301_UROOM\\Customer\\src\\pages\\BannedPage.jsx": "10", "E:\\WDP301_UROOM\\Customer\\src\\pages\\ErrorPage.jsx": "11", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomePage.jsx": "12", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HotelSearchPage.jsx": "13", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\BookingCheckPage.jsx": "14", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomeDetailPage.jsx": "15", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentFailedPage.jsx": "16", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentPage.jsx": "17", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentSuccessPage.jsx": "18", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\RoomDetailPage.jsx": "19", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ChatPage.jsx": "20", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ReportedFeedback.jsx": "21", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\LoginPage.jsx": "22", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ForgetPasswordPage.jsx": "23", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\RegisterPage.jsx": "24", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ResetPasswordPage.jsx": "25", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodeRegisterPage.jsx": "26", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodePage.jsx": "27", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\MyAccountPage.jsx": "28", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\BookingBill.jsx": "29", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\CreateFeedback.jsx": "30", "E:\\WDP301_UROOM\\Customer\\src\\utils\\qaData.js": "31", "E:\\WDP301_UROOM\\Customer\\src\\utils\\data.js": "32", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Footer.jsx": "33", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Header.jsx": "34", "E:\\WDP301_UROOM\\Customer\\src\\pages\\MapLocation.jsx": "35", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\saga.js": "36", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\reducer.js": "37", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\saga.js": "38", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\reducer.js": "39", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\reducer.js": "40", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\actions.js": "41", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\saga.js": "42", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\saga.js": "43", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\reducer.js": "44", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\saga.js": "45", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\reducer.js": "46", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\reducer.js": "47", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\saga.js": "48", "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\actions.js": "49", "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\reducer.js": "50", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\actions.js": "51", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\factories.js": "52", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\reducer.js": "53", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\saga.js": "54", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\actions.js": "55", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\saga.js": "56", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\factories.js": "57", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\actions.js": "58", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\reducer.js": "59", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\actions.js": "60", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\actions.js": "61", "E:\\WDP301_UROOM\\Customer\\src\\utils\\handleToken.js": "62", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\factories.js": "63", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\actions.js": "64", "E:\\WDP301_UROOM\\Customer\\src\\components\\ConfirmationModal.jsx": "65", "E:\\WDP301_UROOM\\Customer\\src\\components\\Pagination.jsx": "66", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservation\\factories.js": "67", "E:\\WDP301_UROOM\\Customer\\src\\components\\ErrorModal.jsx": "68", "E:\\WDP301_UROOM\\Customer\\src\\components\\ToastContainer.jsx": "69", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\GoogleLogin.jsx": "70", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\HotelClosedModal.jsx": "71", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\actions.js": "72", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFavoriteHotel.jsx": "73", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ChangePassword.jsx": "74", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\BookingHistory.jsx": "75", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFeedback.jsx": "76", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewInformation.jsx": "77", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewAvatar.jsx": "78", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyReportFeedBack.jsx": "79", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\RefundReservation.jsx": "80", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\factories.js": "81", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\factories.js": "82", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\factories.js": "83", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\factories.js": "84", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\factories.js": "85", "E:\\WDP301_UROOM\\Customer\\src\\adapter\\ApiConstants.js": "86", "E:\\WDP301_UROOM\\Customer\\src\\libs\\api\\index.js": "87", "E:\\WDP301_UROOM\\Customer\\src\\libs\\firebaseConfig.js": "88", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\CancelReservationModal.jsx": "89", "E:\\WDP301_UROOM\\Customer\\src\\redux\\refunding_reservation\\factories.js": "90", "E:\\WDP301_UROOM\\Customer\\src\\utils\\fonts.js": "91", "E:\\WDP301_UROOM\\Customer\\src\\services\\NotificationService.js": "92", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionModal.jsx": "93", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyPromotion.jsx": "94", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\reducer.js": "95", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\actions.js": "96", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\saga.js": "97", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\factories.js": "98", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\RoomClosedModal.jsx": "99", "E:\\WDP301_UROOM\\Customer\\src\\socket.js": "100"}, {"size": 831, "mtime": 1750045388384, "results": "101", "hashOfConfig": "102"}, {"size": 375, "mtime": 1750045388405, "results": "103", "hashOfConfig": "102"}, {"size": 4850, "mtime": 1751248384117, "results": "104", "hashOfConfig": "102"}, {"size": 1274, "mtime": 1750128490071, "results": "105", "hashOfConfig": "102"}, {"size": 1300, "mtime": 1750045388406, "results": "106", "hashOfConfig": "102"}, {"size": 1451, "mtime": 1750045388405, "results": "107", "hashOfConfig": "102"}, {"size": 1006, "mtime": 1751248590570, "results": "108", "hashOfConfig": "102"}, {"size": 731, "mtime": 1751248590570, "results": "109", "hashOfConfig": "102"}, {"size": 3227, "mtime": 1750128490073, "results": "110", "hashOfConfig": "102"}, {"size": 1928, "mtime": 1750045388385, "results": "111", "hashOfConfig": "102"}, {"size": 1374, "mtime": 1750045388385, "results": "112", "hashOfConfig": "102"}, {"size": 41830, "mtime": 1750861714764, "results": "113", "hashOfConfig": "102"}, {"size": 38585, "mtime": 1750861714765, "results": "114", "hashOfConfig": "102"}, {"size": 31950, "mtime": 1751275777236, "results": "115", "hashOfConfig": "102"}, {"size": 88520, "mtime": 1751248627673, "results": "116", "hashOfConfig": "102"}, {"size": 3157, "mtime": 1750712271161, "results": "117", "hashOfConfig": "102"}, {"size": 7041, "mtime": 1750045388389, "results": "118", "hashOfConfig": "102"}, {"size": 4608, "mtime": 1750712271161, "results": "119", "hashOfConfig": "102"}, {"size": 51823, "mtime": 1751248627673, "results": "120", "hashOfConfig": "102"}, {"size": 26015, "mtime": 1750045388387, "results": "121", "hashOfConfig": "102"}, {"size": 13701, "mtime": 1750045388389, "results": "122", "hashOfConfig": "102"}, {"size": 10248, "mtime": 1750045388396, "results": "123", "hashOfConfig": "102"}, {"size": 3742, "mtime": 1750045388396, "results": "124", "hashOfConfig": "102"}, {"size": 7427, "mtime": 1750045388396, "results": "125", "hashOfConfig": "102"}, {"size": 5465, "mtime": 1750045388396, "results": "126", "hashOfConfig": "102"}, {"size": 8504, "mtime": 1750045388397, "results": "127", "hashOfConfig": "102"}, {"size": 7544, "mtime": 1750045388397, "results": "128", "hashOfConfig": "102"}, {"size": 5583, "mtime": 1751248590565, "results": "129", "hashOfConfig": "102"}, {"size": 33077, "mtime": 1750868409422, "results": "130", "hashOfConfig": "102"}, {"size": 16963, "mtime": 1750712271164, "results": "131", "hashOfConfig": "102"}, {"size": 10836, "mtime": 1750860606178, "results": "132", "hashOfConfig": "102"}, {"size": 4408, "mtime": 1751248627675, "results": "133", "hashOfConfig": "102"}, {"size": 2571, "mtime": 1750128490060, "results": "134", "hashOfConfig": "102"}, {"size": 25894, "mtime": 1750128490061, "results": "135", "hashOfConfig": "102"}, {"size": 2587, "mtime": 1750045388385, "results": "136", "hashOfConfig": "102"}, {"size": 1803, "mtime": 1750045388403, "results": "137", "hashOfConfig": "102"}, {"size": 541, "mtime": 1750045388403, "results": "138", "hashOfConfig": "102"}, {"size": 4244, "mtime": 1750045388399, "results": "139", "hashOfConfig": "102"}, {"size": 2230, "mtime": 1750045388398, "results": "140", "hashOfConfig": "102"}, {"size": 1043, "mtime": 1750128490070, "results": "141", "hashOfConfig": "102"}, {"size": 487, "mtime": 1750045388399, "results": "142", "hashOfConfig": "102"}, {"size": 5057, "mtime": 1750045388398, "results": "143", "hashOfConfig": "102"}, {"size": 223, "mtime": 1750045388405, "results": "144", "hashOfConfig": "102"}, {"size": 1136, "mtime": 1750045388398, "results": "145", "hashOfConfig": "102"}, {"size": 9581, "mtime": 1750045388398, "results": "146", "hashOfConfig": "102"}, {"size": 888, "mtime": 1750045388399, "results": "147", "hashOfConfig": "102"}, {"size": 919, "mtime": 1750045388402, "results": "148", "hashOfConfig": "102"}, {"size": 3342, "mtime": 1750045388402, "results": "149", "hashOfConfig": "102"}, {"size": 265, "mtime": 1750128490068, "results": "150", "hashOfConfig": "102"}, {"size": 1693, "mtime": 1750128490069, "results": "151", "hashOfConfig": "102"}, {"size": 235, "mtime": 1750045388403, "results": "152", "hashOfConfig": "102"}, {"size": 1669, "mtime": 1750712271166, "results": "153", "hashOfConfig": "102"}, {"size": 551, "mtime": 1750045388399, "results": "154", "hashOfConfig": "102"}, {"size": 3000, "mtime": 1750045388402, "results": "155", "hashOfConfig": "102"}, {"size": 137, "mtime": 1750045388403, "results": "156", "hashOfConfig": "102"}, {"size": 2085, "mtime": 1750045388401, "results": "157", "hashOfConfig": "102"}, {"size": 1536, "mtime": 1750045388398, "results": "158", "hashOfConfig": "102"}, {"size": 1271, "mtime": 1750045388397, "results": "159", "hashOfConfig": "102"}, {"size": 917, "mtime": 1750045388401, "results": "160", "hashOfConfig": "102"}, {"size": 574, "mtime": 1750045388398, "results": "161", "hashOfConfig": "102"}, {"size": 322, "mtime": 1750045388399, "results": "162", "hashOfConfig": "102"}, {"size": 551, "mtime": 1750045388406, "results": "163", "hashOfConfig": "102"}, {"size": 1439, "mtime": 1750045388397, "results": "164", "hashOfConfig": "102"}, {"size": 426, "mtime": 1750045388401, "results": "165", "hashOfConfig": "102"}, {"size": 2348, "mtime": 1750045388347, "results": "166", "hashOfConfig": "102"}, {"size": 1621, "mtime": 1750045388347, "results": "167", "hashOfConfig": "102"}, {"size": 402, "mtime": 1750045388402, "results": "168", "hashOfConfig": "102"}, {"size": 864, "mtime": 1750045388347, "results": "169", "hashOfConfig": "102"}, {"size": 1493, "mtime": 1750045388347, "results": "170", "hashOfConfig": "102"}, {"size": 2146, "mtime": 1750045388396, "results": "171", "hashOfConfig": "102"}, {"size": 1088, "mtime": 1750045388391, "results": "172", "hashOfConfig": "102"}, {"size": 438, "mtime": 1750045388402, "results": "173", "hashOfConfig": "102"}, {"size": 16495, "mtime": 1750861714769, "results": "174", "hashOfConfig": "102"}, {"size": 8729, "mtime": 1750128490066, "results": "175", "hashOfConfig": "102"}, {"size": 27565, "mtime": 1750868409423, "results": "176", "hashOfConfig": "102"}, {"size": 30766, "mtime": 1751248627674, "results": "177", "hashOfConfig": "102"}, {"size": 9884, "mtime": 1750045388394, "results": "178", "hashOfConfig": "102"}, {"size": 5820, "mtime": 1750045388394, "results": "179", "hashOfConfig": "102"}, {"size": 14855, "mtime": 1751248627674, "results": "180", "hashOfConfig": "102"}, {"size": 21286, "mtime": 1750128490068, "results": "181", "hashOfConfig": "102"}, {"size": 741, "mtime": 1750045388403, "results": "182", "hashOfConfig": "102"}, {"size": 582, "mtime": 1750045388402, "results": "183", "hashOfConfig": "102"}, {"size": 491, "mtime": 1750045388401, "results": "184", "hashOfConfig": "102"}, {"size": 377, "mtime": 1750045388399, "results": "185", "hashOfConfig": "102"}, {"size": 1071, "mtime": 1750045388399, "results": "186", "hashOfConfig": "102"}, {"size": 2819, "mtime": 1751248627671, "results": "187", "hashOfConfig": "102"}, {"size": 2658, "mtime": 1750045388385, "results": "188", "hashOfConfig": "102"}, {"size": 693, "mtime": 1750045388385, "results": "189", "hashOfConfig": "102"}, {"size": 12155, "mtime": 1750861714766, "results": "190", "hashOfConfig": "102"}, {"size": 830, "mtime": 1750128490069, "results": "191", "hashOfConfig": "102"}, {"size": 636, "mtime": 1750128490073, "results": "192", "hashOfConfig": "102"}, {"size": 2015, "mtime": 1750128490072, "results": "193", "hashOfConfig": "102"}, {"size": 20680, "mtime": 1751275320131, "results": "194", "hashOfConfig": "102"}, {"size": 22180, "mtime": 1751248590566, "results": "195", "hashOfConfig": "102"}, {"size": 3385, "mtime": 1751248590568, "results": "196", "hashOfConfig": "102"}, {"size": 1177, "mtime": 1751248590567, "results": "197", "hashOfConfig": "102"}, {"size": 4760, "mtime": 1751248590568, "results": "198", "hashOfConfig": "102"}, {"size": 341, "mtime": 1751248590567, "results": "199", "hashOfConfig": "102"}, {"size": 1048, "mtime": 1751248627673, "results": "200", "hashOfConfig": "102"}, {"size": 465, "mtime": 1750045388406, "results": "201", "hashOfConfig": "102"}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xcqp28", {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\WDP301_UROOM\\Customer\\src\\index.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\reportWebVitals.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\App.js", ["502", "503", "504"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\store.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\Routes.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\Utils.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\BannedPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\ErrorPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomePage.jsx", ["505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HotelSearchPage.jsx", ["520"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\BookingCheckPage.jsx", ["521", "522", "523", "524"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomeDetailPage.jsx", ["525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentFailedPage.jsx", ["539", "540"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentPage.jsx", ["541", "542", "543", "544", "545"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentSuccessPage.jsx", ["546", "547"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\RoomDetailPage.jsx", ["548", "549", "550", "551", "552"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ChatPage.jsx", ["553", "554", "555", "556", "557", "558", "559"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ReportedFeedback.jsx", ["560", "561", "562"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\LoginPage.jsx", ["563", "564"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ForgetPasswordPage.jsx", ["565", "566", "567", "568", "569"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\RegisterPage.jsx", ["570", "571"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ResetPasswordPage.jsx", ["572", "573", "574"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodePage.jsx", ["575", "576", "577", "578", "579"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\MyAccountPage.jsx", ["580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\BookingBill.jsx", ["593", "594", "595", "596", "597", "598", "599"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\CreateFeedback.jsx", ["600", "601", "602", "603"], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\qaData.js", ["604"], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\data.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Footer.jsx", ["605", "606", "607"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Header.jsx", ["608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\MapLocation.jsx", ["620"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\saga.js", ["621", "622"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\saga.js", ["623", "624", "625", "626", "627", "628"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\saga.js", ["629"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\handleToken.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\Pagination.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservation\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ErrorModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ToastContainer.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\GoogleLogin.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\HotelClosedModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFavoriteHotel.jsx", ["630", "631"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ChangePassword.jsx", ["632", "633"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\BookingHistory.jsx", ["634", "635", "636", "637", "638", "639", "640", "641", "642"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFeedback.jsx", ["643", "644", "645", "646"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewInformation.jsx", ["647"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewAvatar.jsx", ["648", "649"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyReportFeedBack.jsx", ["650", "651", "652", "653", "654"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\RefundReservation.jsx", ["655", "656", "657", "658", "659"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\factories.js", ["660", "661", "662"], [], "E:\\WDP301_UROOM\\Customer\\src\\adapter\\ApiConstants.js", ["663", "664", "665"], [], "E:\\WDP301_UROOM\\Customer\\src\\libs\\api\\index.js", ["666"], [], "E:\\WDP301_UROOM\\Customer\\src\\libs\\firebaseConfig.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\CancelReservationModal.jsx", ["667", "668"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\refunding_reservation\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\fonts.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\services\\NotificationService.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionModal.jsx", ["669", "670", "671", "672"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyPromotion.jsx", ["673", "674", "675", "676", "677", "678", "679"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\saga.js", ["680", "681", "682"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\RoomClosedModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\socket.js", [], [], {"ruleId": "683", "severity": 1, "message": "684", "line": 3, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 3, "endColumn": 13}, {"ruleId": "687", "severity": 1, "message": "688", "line": 46, "column": 6, "nodeType": "689", "endLine": 46, "endColumn": 17, "suggestions": "690"}, {"ruleId": "691", "severity": 1, "message": "692", "line": 103, "column": 61, "nodeType": "693", "messageId": "694", "endLine": 103, "endColumn": 76}, {"ruleId": "683", "severity": 1, "message": "695", "line": 33, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 33, "endColumn": 14}, {"ruleId": "683", "severity": 1, "message": "696", "line": 34, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 34, "endColumn": 14}, {"ruleId": "683", "severity": 1, "message": "697", "line": 35, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 35, "endColumn": 14}, {"ruleId": "683", "severity": 1, "message": "698", "line": 63, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 63, "endColumn": 19}, {"ruleId": "683", "severity": 1, "message": "684", "line": 64, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 64, "endColumn": 13}, {"ruleId": "683", "severity": 1, "message": "699", "line": 87, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 87, "endColumn": 16}, {"ruleId": "687", "severity": 1, "message": "688", "line": 97, "column": 6, "nodeType": "689", "endLine": 97, "endColumn": 8, "suggestions": "700"}, {"ruleId": "683", "severity": 1, "message": "701", "line": 165, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 165, "endColumn": 16}, {"ruleId": "702", "severity": 1, "message": "703", "line": 638, "column": 24, "nodeType": "704", "messageId": "705", "endLine": 638, "endColumn": 26}, {"ruleId": "702", "severity": 1, "message": "706", "line": 665, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 665, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "706", "line": 675, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 675, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "706", "line": 685, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 685, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "703", "line": 907, "column": 29, "nodeType": "704", "messageId": "705", "endLine": 907, "endColumn": 31}, {"ruleId": "683", "severity": 1, "message": "707", "line": 1004, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 1004, "endColumn": 25}, {"ruleId": "702", "severity": 1, "message": "703", "line": 1203, "column": 39, "nodeType": "704", "messageId": "705", "endLine": 1203, "endColumn": 41}, {"ruleId": "708", "severity": 1, "message": "709", "line": 835, "column": 37, "nodeType": "693", "endLine": 842, "endColumn": 38}, {"ruleId": "683", "severity": 1, "message": "710", "line": 10, "column": 3, "nodeType": "685", "messageId": "686", "endLine": 10, "endColumn": 13}, {"ruleId": "683", "severity": 1, "message": "711", "line": 379, "column": 9, "nodeType": "685", "messageId": "686", "endLine": 379, "endColumn": 23}, {"ruleId": "708", "severity": 1, "message": "709", "line": 531, "column": 21, "nodeType": "693", "endLine": 535, "endColumn": 22}, {"ruleId": "708", "severity": 1, "message": "709", "line": 569, "column": 23, "nodeType": "693", "endLine": 583, "endColumn": 24}, {"ruleId": "683", "severity": 1, "message": "712", "line": 6, "column": 3, "nodeType": "685", "messageId": "686", "endLine": 6, "endColumn": 8}, {"ruleId": "683", "severity": 1, "message": "713", "line": 158, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 158, "endColumn": 21}, {"ruleId": "683", "severity": 1, "message": "714", "line": 158, "column": 23, "nodeType": "685", "messageId": "686", "endLine": 158, "endColumn": 37}, {"ruleId": "687", "severity": 1, "message": "715", "line": 179, "column": 6, "nodeType": "689", "endLine": 179, "endColumn": 8, "suggestions": "716"}, {"ruleId": "687", "severity": 1, "message": "717", "line": 314, "column": 6, "nodeType": "689", "endLine": 314, "endColumn": 25, "suggestions": "718"}, {"ruleId": "683", "severity": 1, "message": "719", "line": 864, "column": 9, "nodeType": "685", "messageId": "686", "endLine": 864, "endColumn": 15}, {"ruleId": "702", "severity": 1, "message": "703", "line": 957, "column": 28, "nodeType": "704", "messageId": "705", "endLine": 957, "endColumn": 30}, {"ruleId": "708", "severity": 1, "message": "709", "line": 1069, "column": 19, "nodeType": "693", "endLine": 1080, "endColumn": 20}, {"ruleId": "720", "severity": 1, "message": "721", "line": 1128, "column": 63, "nodeType": "722", "messageId": "723", "endLine": 1128, "endColumn": 65}, {"ruleId": "702", "severity": 1, "message": "703", "line": 1762, "column": 32, "nodeType": "704", "messageId": "705", "endLine": 1762, "endColumn": 34}, {"ruleId": "702", "severity": 1, "message": "706", "line": 2059, "column": 39, "nodeType": "704", "messageId": "705", "endLine": 2059, "endColumn": 41}, {"ruleId": "702", "severity": 1, "message": "703", "line": 2239, "column": 42, "nodeType": "704", "messageId": "705", "endLine": 2239, "endColumn": 44}, {"ruleId": "708", "severity": 1, "message": "709", "line": 2388, "column": 13, "nodeType": "693", "endLine": 2388, "endColumn": 41}, {"ruleId": "708", "severity": 1, "message": "709", "line": 2389, "column": 32, "nodeType": "693", "endLine": 2389, "endColumn": 60}, {"ruleId": "683", "severity": 1, "message": "724", "line": 1, "column": 17, "nodeType": "685", "messageId": "686", "endLine": 1, "endColumn": 26}, {"ruleId": "683", "severity": 1, "message": "725", "line": 10, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 10, "endColumn": 17}, {"ruleId": "687", "severity": 1, "message": "726", "line": 35, "column": 5, "nodeType": "689", "endLine": 35, "endColumn": 7, "suggestions": "727"}, {"ruleId": "687", "severity": 1, "message": "728", "line": 55, "column": 6, "nodeType": "689", "endLine": 55, "endColumn": 16, "suggestions": "729"}, {"ruleId": "687", "severity": 1, "message": "730", "line": 94, "column": 6, "nodeType": "689", "endLine": 94, "endColumn": 8, "suggestions": "731"}, {"ruleId": "687", "severity": 1, "message": "732", "line": 107, "column": 6, "nodeType": "689", "endLine": 107, "endColumn": 26, "suggestions": "733"}, {"ruleId": "734", "severity": 1, "message": "735", "line": 190, "column": 19, "nodeType": "693", "endLine": 190, "endColumn": 40}, {"ruleId": "683", "severity": 1, "message": "736", "line": 6, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 6, "endColumn": 21}, {"ruleId": "687", "severity": 1, "message": "737", "line": 35, "column": 6, "nodeType": "689", "endLine": 35, "endColumn": 31, "suggestions": "738"}, {"ruleId": "702", "severity": 1, "message": "706", "line": 650, "column": 38, "nodeType": "704", "messageId": "705", "endLine": 650, "endColumn": 40}, {"ruleId": "683", "severity": 1, "message": "739", "line": 667, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 667, "endColumn": 29}, {"ruleId": "702", "severity": 1, "message": "703", "line": 907, "column": 18, "nodeType": "704", "messageId": "705", "endLine": 907, "endColumn": 20}, {"ruleId": "683", "severity": 1, "message": "740", "line": 944, "column": 9, "nodeType": "685", "messageId": "686", "endLine": 944, "endColumn": 19}, {"ruleId": "702", "severity": 1, "message": "706", "line": 1366, "column": 46, "nodeType": "704", "messageId": "705", "endLine": 1366, "endColumn": 48}, {"ruleId": "683", "severity": 1, "message": "741", "line": 35, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 35, "endColumn": 20}, {"ruleId": "687", "severity": 1, "message": "742", "line": 83, "column": 6, "nodeType": "689", "endLine": 83, "endColumn": 8, "suggestions": "743"}, {"ruleId": "687", "severity": 1, "message": "744", "line": 87, "column": 6, "nodeType": "689", "endLine": 87, "endColumn": 20, "suggestions": "745"}, {"ruleId": "702", "severity": 1, "message": "706", "line": 121, "column": 29, "nodeType": "704", "messageId": "705", "endLine": 121, "endColumn": 31}, {"ruleId": "687", "severity": 1, "message": "742", "line": 144, "column": 6, "nodeType": "689", "endLine": 144, "endColumn": 44, "suggestions": "746"}, {"ruleId": "702", "severity": 1, "message": "706", "line": 429, "column": 46, "nodeType": "704", "messageId": "705", "endLine": 429, "endColumn": 48}, {"ruleId": "702", "severity": 1, "message": "706", "line": 442, "column": 38, "nodeType": "704", "messageId": "705", "endLine": 442, "endColumn": 40}, {"ruleId": "683", "severity": 1, "message": "747", "line": 22, "column": 9, "nodeType": "685", "messageId": "686", "endLine": 22, "endColumn": 13}, {"ruleId": "708", "severity": 1, "message": "709", "line": 249, "column": 29, "nodeType": "693", "endLine": 253, "endColumn": 30}, {"ruleId": "708", "severity": 1, "message": "709", "line": 258, "column": 29, "nodeType": "693", "endLine": 266, "endColumn": 30}, {"ruleId": "683", "severity": 1, "message": "748", "line": 4, "column": 29, "nodeType": "685", "messageId": "686", "endLine": 4, "endColumn": 40}, {"ruleId": "708", "severity": 1, "message": "709", "line": 243, "column": 17, "nodeType": "693", "endLine": 247, "endColumn": 52}, {"ruleId": "683", "severity": 1, "message": "748", "line": 4, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 4, "endColumn": 21}, {"ruleId": "683", "severity": 1, "message": "749", "line": 10, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 10, "endColumn": 19}, {"ruleId": "683", "severity": 1, "message": "750", "line": 12, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 12, "endColumn": 13}, {"ruleId": "683", "severity": 1, "message": "751", "line": 15, "column": 9, "nodeType": "685", "messageId": "686", "endLine": 15, "endColumn": 17}, {"ruleId": "683", "severity": 1, "message": "752", "line": 63, "column": 9, "nodeType": "685", "messageId": "686", "endLine": 63, "endColumn": 33}, {"ruleId": "683", "severity": 1, "message": "748", "line": 4, "column": 29, "nodeType": "685", "messageId": "686", "endLine": 4, "endColumn": 40}, {"ruleId": "708", "severity": 1, "message": "709", "line": 215, "column": 17, "nodeType": "693", "endLine": 219, "endColumn": 52}, {"ruleId": "683", "severity": 1, "message": "748", "line": 4, "column": 29, "nodeType": "685", "messageId": "686", "endLine": 4, "endColumn": 40}, {"ruleId": "683", "severity": 1, "message": "712", "line": 6, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 6, "endColumn": 15}, {"ruleId": "683", "severity": 1, "message": "750", "line": 9, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 9, "endColumn": 13}, {"ruleId": "683", "severity": 1, "message": "748", "line": 3, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 3, "endColumn": 21}, {"ruleId": "683", "severity": 1, "message": "750", "line": 10, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 10, "endColumn": 13}, {"ruleId": "683", "severity": 1, "message": "753", "line": 22, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 22, "endColumn": 19}, {"ruleId": "683", "severity": 1, "message": "754", "line": 27, "column": 17, "nodeType": "685", "messageId": "686", "endLine": 27, "endColumn": 25}, {"ruleId": "708", "severity": 1, "message": "755", "line": 203, "column": 17, "nodeType": "693", "endLine": 203, "endColumn": 89}, {"ruleId": "683", "severity": 1, "message": "724", "line": 1, "column": 17, "nodeType": "685", "messageId": "686", "endLine": 1, "endColumn": 26}, {"ruleId": "683", "severity": 1, "message": "756", "line": 1, "column": 28, "nodeType": "685", "messageId": "686", "endLine": 1, "endColumn": 36}, {"ruleId": "683", "severity": 1, "message": "736", "line": 25, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 25, "endColumn": 21}, {"ruleId": "757", "severity": 1, "message": "758", "line": 71, "column": 21, "nodeType": "693", "endLine": 83, "endColumn": 23}, {"ruleId": "702", "severity": 1, "message": "706", "line": 96, "column": 39, "nodeType": "704", "messageId": "705", "endLine": 96, "endColumn": 41}, {"ruleId": "702", "severity": 1, "message": "706", "line": 114, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 114, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "706", "line": 115, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 115, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "706", "line": 117, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 117, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "706", "line": 118, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 118, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "706", "line": 119, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 119, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "706", "line": 120, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 120, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "706", "line": 121, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 121, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "706", "line": 122, "column": 26, "nodeType": "704", "messageId": "705", "endLine": 122, "endColumn": 28}, {"ruleId": "683", "severity": 1, "message": "759", "line": 19, "column": 3, "nodeType": "685", "messageId": "686", "endLine": 19, "endColumn": 10}, {"ruleId": "683", "severity": 1, "message": "760", "line": 20, "column": 3, "nodeType": "685", "messageId": "686", "endLine": 20, "endColumn": 13}, {"ruleId": "683", "severity": 1, "message": "761", "line": 52, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 52, "endColumn": 20}, {"ruleId": "683", "severity": 1, "message": "762", "line": 59, "column": 15, "nodeType": "685", "messageId": "686", "endLine": 59, "endColumn": 21}, {"ruleId": "683", "severity": 1, "message": "763", "line": 60, "column": 15, "nodeType": "685", "messageId": "686", "endLine": 60, "endColumn": 25}, {"ruleId": "687", "severity": 1, "message": "764", "line": 75, "column": 6, "nodeType": "689", "endLine": 75, "endColumn": 10, "suggestions": "765"}, {"ruleId": "687", "severity": 1, "message": "766", "line": 84, "column": 6, "nodeType": "689", "endLine": 84, "endColumn": 25, "suggestions": "767"}, {"ruleId": "683", "severity": 1, "message": "768", "line": 39, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 39, "endColumn": 26}, {"ruleId": "683", "severity": 1, "message": "769", "line": 39, "column": 28, "nodeType": "685", "messageId": "686", "endLine": 39, "endColumn": 47}, {"ruleId": "687", "severity": 1, "message": "764", "line": 235, "column": 6, "nodeType": "689", "endLine": 235, "endColumn": 21, "suggestions": "770"}, {"ruleId": "687", "severity": 1, "message": "771", "line": 277, "column": 6, "nodeType": "689", "endLine": 277, "endColumn": 49, "suggestions": "772"}, {"ruleId": "683", "severity": 1, "message": "773", "line": 78, "column": 27, "nodeType": "685", "messageId": "686", "endLine": 78, "endColumn": 45}, {"ruleId": "708", "severity": 1, "message": "755", "line": 32, "column": 15, "nodeType": "693", "endLine": 52, "endColumn": 16}, {"ruleId": "702", "severity": 1, "message": "703", "line": 35, "column": 33, "nodeType": "704", "messageId": "705", "endLine": 35, "endColumn": 35}, {"ruleId": "708", "severity": 1, "message": "755", "line": 55, "column": 15, "nodeType": "693", "endLine": 55, "endColumn": 27}, {"ruleId": "683", "severity": 1, "message": "774", "line": 17, "column": 66, "nodeType": "685", "messageId": "686", "endLine": 17, "endColumn": 72}, {"ruleId": "683", "severity": 1, "message": "775", "line": 34, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 34, "endColumn": 16}, {"ruleId": "683", "severity": 1, "message": "776", "line": 34, "column": 18, "nodeType": "685", "messageId": "686", "endLine": 34, "endColumn": 27}, {"ruleId": "702", "severity": 1, "message": "703", "line": 256, "column": 32, "nodeType": "704", "messageId": "705", "endLine": 256, "endColumn": 34}, {"ruleId": "702", "severity": 1, "message": "703", "line": 268, "column": 32, "nodeType": "704", "messageId": "705", "endLine": 268, "endColumn": 34}, {"ruleId": "702", "severity": 1, "message": "703", "line": 280, "column": 32, "nodeType": "704", "messageId": "705", "endLine": 280, "endColumn": 34}, {"ruleId": "702", "severity": 1, "message": "703", "line": 292, "column": 32, "nodeType": "704", "messageId": "705", "endLine": 292, "endColumn": 34}, {"ruleId": "702", "severity": 1, "message": "703", "line": 304, "column": 32, "nodeType": "704", "messageId": "705", "endLine": 304, "endColumn": 34}, {"ruleId": "702", "severity": 1, "message": "703", "line": 316, "column": 32, "nodeType": "704", "messageId": "705", "endLine": 316, "endColumn": 34}, {"ruleId": "708", "severity": 1, "message": "709", "line": 479, "column": 21, "nodeType": "693", "endLine": 487, "endColumn": 22}, {"ruleId": "702", "severity": 1, "message": "703", "line": 492, "column": 42, "nodeType": "704", "messageId": "705", "endLine": 492, "endColumn": 44}, {"ruleId": "702", "severity": 1, "message": "703", "line": 492, "column": 68, "nodeType": "704", "messageId": "705", "endLine": 492, "endColumn": 70}, {"ruleId": "687", "severity": 1, "message": "777", "line": 47, "column": 6, "nodeType": "689", "endLine": 47, "endColumn": 8, "suggestions": "778"}, {"ruleId": "683", "severity": 1, "message": "779", "line": 8, "column": 40, "nodeType": "685", "messageId": "686", "endLine": 8, "endColumn": 48}, {"ruleId": "683", "severity": 1, "message": "780", "line": 8, "column": 50, "nodeType": "685", "messageId": "686", "endLine": 8, "endColumn": 57}, {"ruleId": "683", "severity": 1, "message": "781", "line": 1, "column": 14, "nodeType": "685", "messageId": "686", "endLine": 1, "endColumn": 18}, {"ruleId": "683", "severity": 1, "message": "782", "line": 1, "column": 20, "nodeType": "685", "messageId": "686", "endLine": 1, "endColumn": 24}, {"ruleId": "683", "severity": 1, "message": "783", "line": 1, "column": 26, "nodeType": "685", "messageId": "686", "endLine": 1, "endColumn": 29}, {"ruleId": "683", "severity": 1, "message": "784", "line": 1, "column": 31, "nodeType": "685", "messageId": "686", "endLine": 1, "endColumn": 40}, {"ruleId": "683", "severity": 1, "message": "749", "line": 2, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 2, "endColumn": 19}, {"ruleId": "683", "severity": 1, "message": "725", "line": 3, "column": 8, "nodeType": "685", "messageId": "686", "endLine": 3, "endColumn": 17}, {"ruleId": "683", "severity": 1, "message": "780", "line": 72, "column": 44, "nodeType": "685", "messageId": "686", "endLine": 72, "endColumn": 51}, {"ruleId": "687", "severity": 1, "message": "785", "line": 101, "column": 6, "nodeType": "689", "endLine": 101, "endColumn": 12, "suggestions": "786"}, {"ruleId": "687", "severity": 1, "message": "787", "line": 136, "column": 6, "nodeType": "689", "endLine": 136, "endColumn": 46, "suggestions": "788"}, {"ruleId": "683", "severity": 1, "message": "789", "line": 2, "column": 3, "nodeType": "685", "messageId": "686", "endLine": 2, "endColumn": 12}, {"ruleId": "683", "severity": 1, "message": "710", "line": 8, "column": 3, "nodeType": "685", "messageId": "686", "endLine": 8, "endColumn": 13}, {"ruleId": "702", "severity": 1, "message": "706", "line": 49, "column": 18, "nodeType": "704", "messageId": "705", "endLine": 49, "endColumn": 20}, {"ruleId": "683", "severity": 1, "message": "790", "line": 53, "column": 24, "nodeType": "685", "messageId": "686", "endLine": 53, "endColumn": 39}, {"ruleId": "687", "severity": 1, "message": "791", "line": 122, "column": 6, "nodeType": "689", "endLine": 122, "endColumn": 16, "suggestions": "792"}, {"ruleId": "687", "severity": 1, "message": "793", "line": 211, "column": 6, "nodeType": "689", "endLine": 211, "endColumn": 46, "suggestions": "794"}, {"ruleId": "687", "severity": 1, "message": "795", "line": 260, "column": 6, "nodeType": "689", "endLine": 260, "endColumn": 32, "suggestions": "796"}, {"ruleId": "797", "severity": 1, "message": "798", "line": 309, "column": 12, "nodeType": "685", "messageId": "799", "endLine": 309, "endColumn": 25}, {"ruleId": "683", "severity": 1, "message": "800", "line": 339, "column": 9, "nodeType": "685", "messageId": "686", "endLine": 339, "endColumn": 30}, {"ruleId": "683", "severity": 1, "message": "801", "line": 379, "column": 9, "nodeType": "685", "messageId": "686", "endLine": 379, "endColumn": 28}, {"ruleId": "702", "severity": 1, "message": "706", "line": 574, "column": 37, "nodeType": "704", "messageId": "705", "endLine": 574, "endColumn": 39}, {"ruleId": "687", "severity": 1, "message": "802", "line": 93, "column": 6, "nodeType": "689", "endLine": 93, "endColumn": 40, "suggestions": "803"}, {"ruleId": "687", "severity": 1, "message": "804", "line": 98, "column": 6, "nodeType": "689", "endLine": 98, "endColumn": 40, "suggestions": "805"}, {"ruleId": "687", "severity": 1, "message": "806", "line": 184, "column": 6, "nodeType": "689", "endLine": 184, "endColumn": 41, "suggestions": "807"}, {"ruleId": "683", "severity": 1, "message": "808", "line": 372, "column": 9, "nodeType": "685", "messageId": "686", "endLine": 372, "endColumn": 19}, {"ruleId": "683", "severity": 1, "message": "724", "line": 2, "column": 17, "nodeType": "685", "messageId": "686", "endLine": 2, "endColumn": 26}, {"ruleId": "702", "severity": 1, "message": "703", "line": 67, "column": 31, "nodeType": "704", "messageId": "705", "endLine": 67, "endColumn": 33}, {"ruleId": "702", "severity": 1, "message": "703", "line": 141, "column": 33, "nodeType": "704", "messageId": "705", "endLine": 141, "endColumn": 35}, {"ruleId": "683", "severity": 1, "message": "809", "line": 17, "column": 26, "nodeType": "685", "messageId": "686", "endLine": 17, "endColumn": 27}, {"ruleId": "683", "severity": 1, "message": "810", "line": 17, "column": 29, "nodeType": "685", "messageId": "686", "endLine": 17, "endColumn": 35}, {"ruleId": "683", "severity": 1, "message": "811", "line": 17, "column": 37, "nodeType": "685", "messageId": "686", "endLine": 17, "endColumn": 42}, {"ruleId": "687", "severity": 1, "message": "812", "line": 36, "column": 6, "nodeType": "689", "endLine": 36, "endColumn": 16, "suggestions": "813"}, {"ruleId": "702", "severity": 1, "message": "706", "line": 226, "column": 33, "nodeType": "704", "messageId": "705", "endLine": 226, "endColumn": 35}, {"ruleId": "683", "severity": 1, "message": "814", "line": 2, "column": 52, "nodeType": "685", "messageId": "686", "endLine": 2, "endColumn": 57}, {"ruleId": "683", "severity": 1, "message": "815", "line": 20, "column": 9, "nodeType": "685", "messageId": "686", "endLine": 20, "endColumn": 13}, {"ruleId": "687", "severity": 1, "message": "816", "line": 67, "column": 6, "nodeType": "689", "endLine": 67, "endColumn": 30, "suggestions": "817"}, {"ruleId": "702", "severity": 1, "message": "706", "line": 353, "column": 36, "nodeType": "704", "messageId": "705", "endLine": 353, "endColumn": 38}, {"ruleId": "702", "severity": 1, "message": "706", "line": 361, "column": 36, "nodeType": "704", "messageId": "705", "endLine": 361, "endColumn": 38}, {"ruleId": "702", "severity": 1, "message": "703", "line": 12, "column": 33, "nodeType": "704", "messageId": "705", "endLine": 12, "endColumn": 35}, {"ruleId": "702", "severity": 1, "message": "703", "line": 15, "column": 37, "nodeType": "704", "messageId": "705", "endLine": 15, "endColumn": 39}, {"ruleId": "702", "severity": 1, "message": "703", "line": 18, "column": 33, "nodeType": "704", "messageId": "705", "endLine": 18, "endColumn": 35}, {"ruleId": "818", "severity": 1, "message": "819", "line": 10, "column": 3, "nodeType": "820", "messageId": "705", "endLine": 10, "endColumn": 17}, {"ruleId": "818", "severity": 1, "message": "821", "line": 11, "column": 3, "nodeType": "820", "messageId": "705", "endLine": 11, "endColumn": 18}, {"ruleId": "818", "severity": 1, "message": "822", "line": 48, "column": 3, "nodeType": "820", "messageId": "705", "endLine": 48, "endColumn": 28}, {"ruleId": "823", "severity": 1, "message": "824", "line": 37, "column": 1, "nodeType": "825", "endLine": 108, "endColumn": 3}, {"ruleId": "687", "severity": 1, "message": "826", "line": 105, "column": 6, "nodeType": "689", "endLine": 105, "endColumn": 8, "suggestions": "827"}, {"ruleId": "702", "severity": 1, "message": "706", "line": 227, "column": 49, "nodeType": "704", "messageId": "705", "endLine": 227, "endColumn": 51}, {"ruleId": "683", "severity": 1, "message": "828", "line": 11, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 11, "endColumn": 27}, {"ruleId": "683", "severity": 1, "message": "829", "line": 11, "column": 29, "nodeType": "685", "messageId": "686", "endLine": 11, "endColumn": 49}, {"ruleId": "687", "severity": 1, "message": "830", "line": 18, "column": 6, "nodeType": "689", "endLine": 18, "endColumn": 24, "suggestions": "831"}, {"ruleId": "683", "severity": 1, "message": "832", "line": 163, "column": 15, "nodeType": "685", "messageId": "686", "endLine": 163, "endColumn": 24}, {"ruleId": "683", "severity": 1, "message": "833", "line": 2, "column": 41, "nodeType": "685", "messageId": "686", "endLine": 2, "endColumn": 48}, {"ruleId": "683", "severity": 1, "message": "834", "line": 3, "column": 10, "nodeType": "685", "messageId": "686", "endLine": 3, "endColumn": 15}, {"ruleId": "683", "severity": 1, "message": "835", "line": 3, "column": 68, "nodeType": "685", "messageId": "686", "endLine": 3, "endColumn": 76}, {"ruleId": "683", "severity": 1, "message": "836", "line": 3, "column": 78, "nodeType": "685", "messageId": "686", "endLine": 3, "endColumn": 84}, {"ruleId": "687", "severity": 1, "message": "830", "line": 74, "column": 6, "nodeType": "689", "endLine": 74, "endColumn": 16, "suggestions": "837"}, {"ruleId": "687", "severity": 1, "message": "838", "line": 88, "column": 6, "nodeType": "689", "endLine": 88, "endColumn": 39, "suggestions": "839"}, {"ruleId": "683", "severity": 1, "message": "840", "line": 304, "column": 32, "nodeType": "685", "messageId": "686", "endLine": 304, "endColumn": 50}, {"ruleId": "683", "severity": 1, "message": "841", "line": 8, "column": 13, "nodeType": "685", "messageId": "686", "endLine": 8, "endColumn": 17}, {"ruleId": "683", "severity": 1, "message": "842", "line": 8, "column": 19, "nodeType": "685", "messageId": "686", "endLine": 8, "endColumn": 24}, {"ruleId": "683", "severity": 1, "message": "843", "line": 8, "column": 42, "nodeType": "685", "messageId": "686", "endLine": 8, "endColumn": 48}, "no-unused-vars", "'Utils' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["844"], "react/jsx-pascal-case", "Imported JSX component Home_detail must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "'image4' is defined but never used.", "'image5' is defined but never used.", "'image6' is defined but never used.", "'RoomActions' is defined but never used.", "'hotels' is assigned a value but never used.", ["845"], "'errors' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "Expected '===' and instead saw '=='.", "'CustomerReviews' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'InputGroup' is defined but never used.", "'formatCurrency' is assigned a value but never used.", "'Route' is defined but never used.", "'isSearching' is assigned a value but never used.", "'setIsSearching' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleBackToBooking'. Either include it or remove the dependency array.", ["846"], "React Hook useEffect has a missing dependency: 'searchParams'. Either include it or remove the dependency array.", ["847"], "'status' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "'useEffect' is defined but never used.", "'Factories' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReservation'. Either include it or remove the dependency array.", ["848"], "React Hook useEffect has missing dependencies: 'createdAt', 'idReservation', 'messageError', 'messageSuccess', 'navigate', and 'totalPrice'. Either include them or remove the dependency array.", ["849"], "React Hook useEffect has a missing dependency: 'handleAccept'. Either include it or remove the dependency array.", ["850"], "React Hook useEffect has a missing dependency: 'handleDelete'. Either include it or remove the dependency array.", ["851"], "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'useLocation' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleConfirm'. Either include it or remove the dependency array.", ["852"], "'currentServiceIndex' is assigned a value but never used.", "'totalPrice' is assigned a value but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["853"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["854"], ["855"], "'User' is assigned a value but never used.", "'FaArrowLeft' is defined but never used.", "'AuthActions' is defined but never used.", "'axios' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'useState' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'FaPhone' is defined but never used.", "'FaEnvelope' is defined but never used.", "'fontLoaded' is assigned a value but never used.", "'pdfLib' is assigned a value but never used.", "'pdfFontLib' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchReservationDetail'. Either include it or remove the dependency array.", ["856"], "React Hook useEffect has a missing dependency: 'fetchHotelDetails'. Either include it or remove the dependency array.", ["857"], "'userReservations' is assigned a value but never used.", "'setUserReservations' is assigned a value but never used.", ["858"], "React Hook useEffect has missing dependencies: 'dispatch' and 'fetchHotelDetails'. Either include them or remove the dependency array.", ["859"], "'setSearchParamsObj' is assigned a value but never used.", "'FaUser' is defined but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'addressMap'. Either include it or remove the dependency array.", ["860"], "'onFailed' is assigned a value but never used.", "'onError' is assigned a value but never used.", "'call' is defined but never used.", "'fork' is defined but never used.", "'put' is defined but never used.", "'takeEvery' is defined but never used.", "React Hook useEffect has a missing dependency: 'activePage'. Either include it or remove the dependency array.", ["861"], "React Hook useEffect has missing dependencies: 'activePage' and 'updateURL'. Either include them or remove the dependency array.", ["862"], "'Container' is defined but never used.", "'setItemsPerPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserReservations'. Either include it or remove the dependency array.", ["863"], "React Hook useEffect has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["864"], "React Hook useEffect has missing dependencies: 'activePage' and 'totalPages'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setActivePage' needs the current value of 'totalPages'.", ["865"], "no-redeclare", "'parseCurrency' is already defined.", "redeclared", "'calculateRefundPolicy' is assigned a value but never used.", "'calculateTotalPrice' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'activePage', 'sortOption', and 'starFilter'. Either include them or remove the dependency array.", ["866"], "React Hook useEffect has a missing dependency: 'fetchUserFeedbacks'. Either include it or remove the dependency array.", ["867"], "React Hook useEffect has missing dependencies: 'activePage', 'getFilteredFeedbacks', and 'updateURL'. Either include them or remove the dependency array.", ["868"], "'formatDate' is assigned a value but never used.", "'X' is defined but never used.", "'Pencil' is defined but never used.", "'Trash' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUserReports'. Either include it or remove the dependency array.", ["869"], "'Toast' is defined but never used.", "'Auth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["870"], "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has missing dependencies: 'calculateRefundPolicy' and 'setRefundAmount'. Either include them or remove the dependency array. If 'setRefundAmount' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["871"], "'selectedPromotion' is assigned a value but never used.", "'setSelectedPromotion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPromotions'. Either include it or remove the dependency array.", ["872"], "'startDate' is assigned a value but never used.", "'Spinner' is defined but never used.", "'FaTag' is defined but never used.", "'FaFilter' is defined but never used.", "'FaSync' is defined but never used.", ["873"], "React Hook useEffect has missing dependencies: 'getFilteredPromotions' and 'updateURL'. Either include them or remove the dependency array.", ["874"], "'totalFilteredCount' is assigned a value but never used.", "'page' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'userId' is assigned a value but never used.", {"desc": "875", "fix": "876"}, {"desc": "877", "fix": "878"}, {"desc": "879", "fix": "880"}, {"desc": "881", "fix": "882"}, {"desc": "883", "fix": "884"}, {"desc": "885", "fix": "886"}, {"desc": "887", "fix": "888"}, {"desc": "889", "fix": "890"}, {"desc": "891", "fix": "892"}, {"desc": "893", "fix": "894"}, {"desc": "895", "fix": "896"}, {"desc": "897", "fix": "898"}, {"desc": "899", "fix": "900"}, {"desc": "901", "fix": "902"}, {"desc": "903", "fix": "904"}, {"desc": "905", "fix": "906"}, {"desc": "907", "fix": "908"}, {"desc": "909", "fix": "910"}, {"desc": "911", "fix": "912"}, {"desc": "913", "fix": "914"}, {"desc": "915", "fix": "916"}, {"desc": "917", "fix": "918"}, {"desc": "919", "fix": "920"}, {"desc": "921", "fix": "922"}, {"desc": "923", "fix": "924"}, {"desc": "925", "fix": "926"}, {"desc": "927", "fix": "928"}, {"desc": "929", "fix": "930"}, {"desc": "931", "fix": "932"}, {"desc": "933", "fix": "934"}, {"desc": "935", "fix": "936"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "937", "text": "938"}, "Update the dependencies array to be: [dispatch]", {"range": "939", "text": "940"}, "Update the dependencies array to be: [handleBackToBooking]", {"range": "941", "text": "942"}, "Update the dependencies array to be: [hotelId, dispatch, searchParams]", {"range": "943", "text": "944"}, "Update the dependencies array to be: [fetchReservation]", {"range": "945", "text": "946"}, "Update the dependencies array to be: [createdAt, idReservation, location, messageError, messageSuccess, navigate, totalPrice]", {"range": "947", "text": "948"}, "Update the dependencies array to be: [handleAccept]", {"range": "949", "text": "950"}, "Update the dependencies array to be: [timeLeft, navigate, handleDelete]", {"range": "951", "text": "952"}, "Update the dependencies array to be: [reservationId, navigate, handleConfirm]", {"range": "953", "text": "954"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "955", "text": "956"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "957", "text": "958"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "959", "text": "960"}, "Update the dependencies array to be: [fetchReservationDetail, id]", {"range": "961", "text": "962"}, "Update the dependencies array to be: [fetchHotelDetails, reservationDetail]", {"range": "963", "text": "964"}, "Update the dependencies array to be: [fetchReservationDetail, reservationId]", {"range": "965", "text": "966"}, "Update the dependencies array to be: [Auth, reservationId, hotelId, hotelDetail, dispatch, fetchHotelDetails]", {"range": "967", "text": "968"}, "Update the dependencies array to be: [addressMap]", {"range": "969", "text": "970"}, "Update the dependencies array to be: [activePage, page]", {"range": "971", "text": "972"}, "Update the dependencies array to be: [dispatch, Auth?.favorites, paramsQuery, activePage, updateURL]", {"range": "973", "text": "974"}, "Update the dependencies array to be: [dispatch, fetchUserReservations]", {"range": "975", "text": "976"}, "Update the dependencies array to be: [activeFilter, reservations, dateFilter, filters]", {"range": "977", "text": "978"}, "Update the dependencies array to be: [activePage, filterBill, itemsPerPage, totalPages]", {"range": "979", "text": "980"}, "Update the dependencies array to be: [activePage, pageParam, sortOption, sortParam, starFilter, starsParam]", {"range": "981", "text": "982"}, "Update the dependencies array to be: [dispatch, fetchUserFeedbacks, sortOption, starFilter]", {"range": "983", "text": "984"}, "Update the dependencies array to be: [feedbacks, starFilter, sortOption, getFilteredFeedbacks, activePage, updateURL]", {"range": "985", "text": "986"}, "Update the dependencies array to be: [Auth._id, fetchUserReports]", {"range": "987", "text": "988"}, "Update the dependencies array to be: [dispatch, activeStatus, fetchData]", {"range": "989", "text": "990"}, "Update the dependencies array to be: [calculateRefundPolicy, setRefundAmount]", {"range": "991", "text": "992"}, "Update the dependencies array to be: [fetchPromotions, show, totalPrice]", {"range": "993", "text": "994"}, "Update the dependencies array to be: [dispatch, fetchPromotions]", {"range": "995", "text": "996"}, "Update the dependencies array to be: [promotions, filters, activePage, getFilteredPromotions, updateURL]", {"range": "997", "text": "998"}, [2163, 2174], "[Auth?._id, dispatch]", [3226, 3228], "[dispatch]", [6150, 6152], "[handleBackToBooking]", [9886, 9905], "[hotelId, dispatch, searchParams]", [1405, 1407], "[fetchReservation]", [1841, 1851], "[createdAt, idReservation, location, messageError, messageSuccess, navigate, totalPrice]", [3122, 3124], "[handleAccept]", [3447, 3467], "[timeLeft, navigate, handleDelete]", [1197, 1222], "[reservationId, navigate, handleConfirm]", [2913, 2915], "[fetchAllUser]", [2972, 2986], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4575, 4613], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]", [2124, 2128], "[fetchReservationDetail, id]", [2328, 2347], "[fetchHotelDetails, reservationDetail]", [7500, 7515], "[fetchReservationDetail, reservationId]", [9032, 9075], "[Auth, reservationId, hotelId, hotelDetail, dispatch, fetchHotelDetails]", [1416, 1418], "[addressMap]", [3659, 3665], "[activePage, page]", [4664, 4704], "[dispatch, Auth?.favorites, paramsQuery, activePage, updateURL]", [4692, 4702], "[dispatch, fetchUserReservations]", [7726, 7766], "[activeFilter, reservations, dateFilter, filters]", [9200, 9226], "[activePage, filterBill, itemsPerPage, totalPages]", [3136, 3170], "[activePage, pageParam, sortOption, sortParam, starFilter, starsParam]", [3276, 3310], "[dispatch, fetchUserFeedbacks, sortOption, starFilter]", [6005, 6040], "[feedbacks, starFilter, sortOption, getFilteredFeedbacks, activePage, updateURL]", [1363, 1373], "[Auth._id, fetchUserReports]", [2318, 2342], "[dispatch, activeStatus, fetchData]", [3197, 3199], "[calculateRefundPolicy, setRefundAmount]", [724, 742], "[fetchPromotions, show, totalPrice]", [2718, 2728], "[dispatch, fetchPromotions]", [3198, 3231], "[promotions, filters, activePage, getFilteredPromotions, updateURL]"]