{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\BookingCheckPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { Container, Row, Col, Card, Form, Button, InputGroup } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\nimport Banner from \"../../../images/banner.jpg\";\nimport Header from \"../Header\";\nimport Footer from \"../Footer\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport ConfirmationModal from \"@components/ConfirmationModal\";\nimport PromotionModal from \"./components/PromotionModal\";\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\nimport Utils from \"../../../utils/Utils\";\nimport Factories from \"../../../redux/search/factories\";\nimport { ChatBox } from \"./HomePage\";\nimport SearchActions from \"../../../redux/search/actions\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport HotelClosedModal from \"./components/HotelClosedModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookingCheckPage = () => {\n  _s();\n  var _hotelDetail$hotelNam, _hotelDetail$address;\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const SearchInformation = useAppSelector(state => state.Search.SearchInformation);\n  const selectedRoomsTemps = useAppSelector(state => state.Search.selectedRooms);\n  const selectedServicesFromRedux = useAppSelector(state => state.Search.selectedServices);\n  const hotelDetailFromRedux = useAppSelector(state => state.Search.hotelDetail);\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\n\n  // Promotion code state\n  const [promotionCode, setPromotionCode] = useState(\"\");\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\n  const [promotionId, setPromotionId] = useState(null);\n\n  // Add state for booking data\n  const [bookingData, setBookingData] = useState({\n    selectedRooms: selectedRoomsTemps || [],\n    selectedServices: selectedServicesFromRedux || [],\n    hotelDetail: hotelDetailFromRedux || null,\n    searchInfo: SearchInformation\n  });\n  const [dataRestored, setDataRestored] = useState(false);\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\n\n  // Restore data from sessionStorage stack when component mounts\n  useEffect(() => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      const currentBooking = bookingStack[bookingStack.length - 1];\n      setBookingData(currentBooking);\n\n      // Update Redux store with current data\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: currentBooking.selectedRooms,\n          selectedServices: currentBooking.selectedServices,\n          hotelDetail: currentBooking.hotelDetail\n        }\n      });\n    }\n    setDataRestored(true);\n  }, [dispatch]);\n\n  // Load promotion info from sessionStorage AFTER booking data is restored\n  useEffect(() => {\n    if (dataRestored) {\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\n      if (promo) {\n        setPromotionCode(promo.promotionCode || \"\");\n        setPromotionDiscount(promo.promotionDiscount || 0);\n        setPromotionMessage(promo.promotionMessage || \"\");\n        setPromotionId(promo.promotionId || null);\n      }\n    }\n  }, [dataRestored]);\n\n  // Save promotion info to sessionStorage when any promotion state changes\n  useEffect(() => {\n    if (dataRestored) {\n      // Chỉ save khi đã restore xong data\n      sessionStorage.setItem(\"promotionInfo\", JSON.stringify({\n        promotionCode,\n        promotionDiscount,\n        promotionMessage,\n        promotionId\n      }));\n    }\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored]);\n\n  // Use bookingData instead of Redux state\n  const selectedRooms = bookingData.selectedRooms;\n  const selectedServices = bookingData.selectedServices;\n  const hotelDetail = bookingData.hotelDetail;\n  const searchInfo = bookingData.searchInfo;\n\n  // Calculate number of days between check-in and check-out\n  const calculateNumberOfDays = () => {\n    const checkIn = new Date(searchInfo.checkinDate);\n    const checkOut = new Date(searchInfo.checkoutDate);\n    const diffTime = Math.abs(checkOut - checkIn);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n  const numberOfDays = calculateNumberOfDays();\n\n  // Calculate prices\n  const totalRoomPrice = selectedRooms.reduce((total, {\n    room,\n    amount\n  }) => total + room.price * amount * numberOfDays, 0);\n  const totalServicePrice = selectedServices.reduce((total, service) => {\n    const selectedDates = service.selectedDates || [];\n    const serviceQuantity = service.quantity * selectedDates.length;\n    return total + service.price * serviceQuantity;\n  }, 0);\n  const subtotal = totalRoomPrice + totalServicePrice;\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\n\n  // Validate promotion when data is restored or booking changes\n  useEffect(() => {\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\n\n    // Add a small delay to ensure promotion is fully restored before validation\n    const timeoutId = setTimeout(() => {\n      const validatePromotion = async () => {\n        setIsValidatingPromotion(true);\n        try {\n          const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\n            code: promotionCode,\n            orderAmount: subtotal\n          });\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\n            // Promotion is no longer valid or discount changed\n            setPromotionCode(\"\");\n            setPromotionDiscount(0);\n            setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\n            setPromotionId(null);\n            sessionStorage.removeItem(\"promotionInfo\");\n          }\n        } catch (err) {\n          // Promotion validation failed\n          setPromotionCode(\"\");\n          setPromotionDiscount(0);\n          setPromotionMessage(\"Promotion is no longer valid\");\n          setPromotionId(null);\n          sessionStorage.removeItem(\"promotionInfo\");\n        } finally {\n          setIsValidatingPromotion(false);\n        }\n      };\n      validatePromotion();\n    }, 100);\n    return () => clearTimeout(timeoutId);\n  }, [dataRestored, subtotal, promotionCode, promotionId, promotionDiscount]); // Validate when subtotal changes or data is restored\n\n  // Handle navigation back to HomeDetailPage\n  const handleBackToHomeDetail = () => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      // Remove the current booking from stack\n      bookingStack.pop();\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n    }\n    navigate(-1);\n  };\n\n  // Star rating component\n  const StarRating = ({\n    rating\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"star-rating\",\n      children: [...Array(5)].map((_, index) => index < rating ? /*#__PURE__*/_jsxDEV(FaStar, {\n        className: \"star filled\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(FaRegStar, {\n        className: \"star\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this);\n  };\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\n\n  // Hàm xử lý áp dụng promotion từ modal\n  const handleApplyPromotionFromModal = promotionData => {\n    setPromotionCode(promotionData.code);\n    setPromotionDiscount(promotionData.discount);\n    setPromotionMessage(promotionData.message);\n    setPromotionId(promotionData.promotionId);\n  };\n\n  // Function to check hotel status before booking\n  const checkHotelStatusBeforeBooking = async () => {\n    return new Promise((resolve, reject) => {\n      setIsCheckingHotelStatus(true);\n      dispatch({\n        type: HotelActions.FETCH_DETAIL_HOTEL,\n        payload: {\n          hotelId: hotelDetail._id,\n          userId: Auth._id,\n          onSuccess: hotel => {\n            setIsCheckingHotelStatus(false);\n            if (hotel.ownerStatus === \"ACTIVE\") {\n              resolve(hotel);\n            } else {\n              reject(new Error(\"Hotel is currently inactive\"));\n            }\n          },\n          onFailed: error => {\n            setIsCheckingHotelStatus(false);\n            reject(new Error(error || \"Failed to check hotel status\"));\n          },\n          onError: error => {\n            setIsCheckingHotelStatus(false);\n            reject(new Error(\"Server error while checking hotel status\"));\n          }\n        }\n      });\n    });\n  };\n  const createBooking = async () => {\n    try {\n      // Check hotel status first\n      const hotel = await checkHotelStatusBeforeBooking();\n      console.log(\"Hotel detail fetched successfully:\", hotel);\n      const totalRoomPrice = selectedRooms.reduce((total, {\n        room,\n        amount\n      }) => total + room.price * amount * numberOfDays, 0);\n      const totalServicePrice = selectedServices.reduce((total, service) => {\n        const selectedDates = service.selectedDates || [];\n        const serviceQuantity = service.quantity * selectedDates.length;\n        return total + service.price * serviceQuantity;\n      }, 0);\n      const bookingSubtotal = totalRoomPrice + totalServicePrice;\n      const params = {\n        hotelId: hotelDetail._id,\n        checkOutDate: searchInfo.checkoutDate,\n        checkInDate: searchInfo.checkinDate,\n        totalPrice: bookingSubtotal,\n        // giá gốc\n        finalPrice: finalPrice,\n        // giá sau giảm giá\n        roomDetails: selectedRooms.map(({\n          room,\n          amount\n        }) => ({\n          room: {\n            _id: room._id\n          },\n          amount: amount\n        })),\n        serviceDetails: selectedServices.map(service => {\n          var _service$selectedDate;\n          return {\n            _id: service._id,\n            quantity: service.quantity * (((_service$selectedDate = service.selectedDates) === null || _service$selectedDate === void 0 ? void 0 : _service$selectedDate.length) || 0),\n            selectDate: service.selectedDates || []\n          };\n        }),\n        // Thêm promotionId và promotionDiscount nếu có\n        ...(promotionId && {\n          promotionId\n        }),\n        ...(promotionDiscount > 0 && {\n          promotionDiscount\n        })\n      };\n      console.log(\"params >> \", params);\n\n      // Helper function to save reservationId to bookingStack\n      const saveReservationIdToBookingStack = reservationId => {\n        if (reservationId) {\n          const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n          if (bookingStack.length > 0) {\n            bookingStack[bookingStack.length - 1].reservationId = reservationId;\n            sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n          }\n        }\n      };\n      try {\n        let reservationId = null;\n        const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n        if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\n          reservationId = bookingStack[bookingStack.length - 1].reservationId;\n        }\n        const response = await Factories.create_booking({\n          ...params,\n          reservationId\n        });\n        console.log(\"response >> \", response);\n        if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n          var _response$data, _response$data$unpaid, _responseCheckout$dat;\n          reservationId = response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : (_response$data$unpaid = _response$data.unpaidReservation) === null || _response$data$unpaid === void 0 ? void 0 : _response$data$unpaid._id;\n          saveReservationIdToBookingStack(reservationId);\n          const unpaidReservationId = reservationId;\n          const responseCheckout = await Factories.checkout_booking(unpaidReservationId);\n          console.log(\"responseCheckout >> \", responseCheckout);\n          const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat = responseCheckout.data) === null || _responseCheckout$dat === void 0 ? void 0 : _responseCheckout$dat.sessionUrl;\n          if (paymentUrl) {\n            window.location.href = paymentUrl;\n          }\n        } else if ((response === null || response === void 0 ? void 0 : response.status) === 201) {\n          var _response$data2, _response$data2$reser, _responseCheckout$dat2;\n          reservationId = response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$reser = _response$data2.reservation) === null || _response$data2$reser === void 0 ? void 0 : _response$data2$reser._id;\n          saveReservationIdToBookingStack(reservationId);\n          const responseCheckout = await Factories.checkout_booking(reservationId);\n          const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat2 = responseCheckout.data) === null || _responseCheckout$dat2 === void 0 ? void 0 : _responseCheckout$dat2.sessionUrl;\n          if (paymentUrl) {\n            window.location.href = paymentUrl;\n          }\n        } else {\n          console.log(\"error create booking\");\n        }\n      } catch (error) {\n        console.error(\"Error create payment: \", error);\n        navigate(Routers.ErrorPage);\n      }\n    } catch (error) {\n      console.error(\"Error checking hotel status:\", error);\n      setShowModalStatusBooking(true);\n    }\n  };\n  const handleAccept = () => {\n    const totalRoomPrice = selectedRooms.reduce((total, {\n      room,\n      amount\n    }) => total + room.price * amount * numberOfDays, 0);\n    if (totalRoomPrice > 0) {\n      createBooking();\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: [],\n          selectedServices: [],\n          hotelDetail: hotelDetail\n        }\n      });\n    }\n  };\n  const handleConfirmBooking = () => {\n    setShowAcceptModal(true);\n  };\n  const formatCurrency = amount => {\n    if (amount === undefined || amount === null) return \"$0\";\n    return new Intl.NumberFormat(\"en-US\", {\n      style: \"currency\",\n      currency: \"USD\",\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(amount);\n  };\n\n  // Add null check for hotelDetail\n  if (!hotelDetail) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: \"100vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\",\n      style: {\n        paddingTop: \"65px\",\n        paddingBottom: \"50px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Container, {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"booking-card text-white\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                marginBottom: \"20px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stars mb-2\",\n                style: {\n                  justifyContent: \"flex-start\",\n                  justifyItems: \"self-start\"\n                },\n                children: /*#__PURE__*/_jsxDEV(StarRating, {\n                  rating: hotelDetail.star\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"hotel-name mb-1\",\n                children: (_hotelDetail$hotelNam = hotelDetail.hotelName) !== null && _hotelDetail$hotelNam !== void 0 ? _hotelDetail$hotelNam : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hotel-address small mb-4\",\n                children: (_hotelDetail$address = hotelDetail.address) !== null && _hotelDetail$address !== void 0 ? _hotelDetail$address : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-4\",\n                children: \"Your booking detail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkin\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkinDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkout\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkout\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkoutDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stay-info mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total length of stay:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [numberOfDays, \" night\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 21\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total number of people:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [searchInfo.adults, \" Adults - \", searchInfo.childrens, \" \", \"Childrens\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-room mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-4\",\n                  children: \"You selected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 19\n                }, this), selectedRooms.map(({\n                  room,\n                  amount\n                }) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [amount, \" x \", room.name, \" (\", numberOfDays, \" days):\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(room.price * amount * numberOfDays)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this)]\n                }, room._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 21\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: handleBackToHomeDetail,\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this), selectedServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-services mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-3\",\n                  children: \"Selected Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 21\n                }, this), selectedServices.map(service => {\n                  const selectedDates = service.selectedDates || [];\n                  const serviceQuantity = service.quantity * selectedDates.length;\n                  const serviceTotal = service.price * serviceQuantity;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [service.quantity, \" x \", service.name, \" (\", selectedDates.length, \" days):\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"fw-bold\",\n                      children: Utils.formatCurrency(serviceTotal)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 561,\n                      columnNumber: 27\n                    }, this)]\n                  }, service._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: () => {\n                      dispatch({\n                        type: SearchActions.SAVE_SELECTED_ROOMS,\n                        payload: {\n                          selectedRooms: selectedRooms,\n                          selectedServices: selectedServices,\n                          hotelDetail: hotelDetail\n                        }\n                      });\n                      navigate(-1);\n                    },\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-section mb-3\",\n                children: [promotionDiscount > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-applied mb-3\",\n                  style: {\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n                    borderColor: \"#28a745\",\n                    border: \"2px solid #28a745\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"text-success me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 614,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"fw-bold text-success\",\n                            children: promotionCode\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 615,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 613,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-success\",\n                          children: [\"Save \", Utils.formatCurrency(promotionDiscount)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 617,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 612,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleApplyPromotionFromModal({\n                          code: \"\",\n                          discount: 0,\n                          message: \"\",\n                          promotionId: null\n                        }),\n                        className: \"d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 632,\n                          columnNumber: 29\n                        }, this), \"Remove\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 621,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-3 mb-3\",\n                  style: {\n                    border: \"2px dashed rgba(255,255,255,0.3)\",\n                    borderRadius: \"8px\",\n                    backgroundColor: \"rgba(255,255,255,0.05)\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"text-muted mb-2\",\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-muted small\",\n                    children: \"No promotion applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"w-100 d-flex align-items-center justify-content-center\",\n                  onClick: () => setShowPromotionModal(true),\n                  style: {\n                    borderStyle: \"dashed\",\n                    borderWidth: \"2px\",\n                    padding: \"12px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 21\n                  }, this), promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 19\n                }, this), promotionMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `mt-2 small text-center ${promotionDiscount > 0 ? \"text-success\" : \"text-danger\"}`,\n                  children: promotionMessage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-danger mb-0\",\n                    children: [\"Total: \", Utils.formatCurrency(finalPrice)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: \"Includes taxes and fees\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                color: \"white\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-4\",\n                children: \"Check your information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Full name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: Auth.name,\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"email\",\n                    value: Auth.email,\n                    placeholder: \"<EMAIL>\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"tel\",\n                    value: Auth.phoneNumber,\n                    placeholder: \"0912345678\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Who are you booking for?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 743,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"mainGuest\",\n                      label: \"I'm the main guest\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"mainGuest\",\n                      onChange: () => setBookingFor(\"mainGuest\"),\n                      className: \"mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 745,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"someoneElse\",\n                      label: \"I'm booking for someone else\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"someoneElse\",\n                      onChange: () => setBookingFor(\"someoneElse\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 754,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 744,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    className: \"px-4 py-2\",\n                    style: {\n                      borderRadius: \"10px\",\n                      backgroundColor: \"white\",\n                      color: \"#007bff\",\n                      border: \"none\",\n                      fontWeight: \"bold\"\n                    },\n                    onClick: handleConfirmBooking,\n                    children: \"Booking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 766,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                    show: showAcceptModal,\n                    onHide: () => setShowAcceptModal(false),\n                    onConfirm: handleAccept,\n                    title: \"Confirm Acceptance\",\n                    message: \"Do you want to proceed with this booking confirmation?\",\n                    confirmButtonText: \"Accept\",\n                    type: \"accept\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(ChatBox, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 796,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 795,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 799,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionModal, {\n      show: showPromotionModal,\n      onHide: () => setShowPromotionModal(false),\n      totalPrice: totalPrice,\n      onApplyPromotion: handleApplyPromotionFromModal,\n      currentPromotionId: promotionId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 802,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HotelClosedModal, {\n      show: showModalStatusBooking,\n      onClose: () => {\n        setShowModalStatusBooking(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 810,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 404,\n    columnNumber: 5\n  }, this);\n};\n_s(BookingCheckPage, \"uqI3oA+wJZSJj4G3SA3uz0YW8v4=\", false, function () {\n  return [useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useNavigate, useAppDispatch];\n});\n_c = BookingCheckPage;\nexport default BookingCheckPage;\nvar _c;\n$RefreshReg$(_c, \"BookingCheckPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "InputGroup", "FaStar", "FaRegStar", "FaTag", "FaTimes", "Banner", "Header", "Footer", "Routers", "useNavigate", "ConfirmationModal", "PromotionModal", "useAppSelector", "useAppDispatch", "Utils", "Factories", "ChatBox", "SearchActions", "HotelActions", "HotelClosedModal", "jsxDEV", "_jsxDEV", "BookingCheckPage", "_s", "_hotelDetail$hotelNam", "_hotelDetail$address", "showModalStatusBooking", "setShowModalStatusBooking", "<PERSON><PERSON>", "state", "SearchInformation", "Search", "selectedRoomsTemps", "selectedRooms", "selectedServicesFromRedux", "selectedServices", "hotelDetailFromRedux", "hotelDetail", "navigate", "dispatch", "bookingFor", "setBookingFor", "promotionCode", "setPromotionCode", "promotionDiscount", "setPromotionDiscount", "promotionMessage", "setPromotionMessage", "promotionId", "setPromotionId", "bookingData", "setBookingData", "searchInfo", "dataRestored", "setDataRestored", "isValidatingPromotion", "setIsValidatingPromotion", "isCheckingHotelStatus", "setIsCheckingHotelStatus", "bookingStack", "JSON", "parse", "sessionStorage", "getItem", "length", "currentBooking", "type", "SAVE_SELECTED_ROOMS", "payload", "promo", "setItem", "stringify", "calculateNumberOfDays", "checkIn", "Date", "checkinDate", "checkOut", "checkoutDate", "diffTime", "Math", "abs", "diffDays", "ceil", "numberOfDays", "totalRoomPrice", "reduce", "total", "room", "amount", "price", "totalServicePrice", "service", "selectedDates", "serviceQuantity", "quantity", "subtotal", "finalPrice", "max", "timeoutId", "setTimeout", "validatePromotion", "res", "post", "code", "orderAmount", "data", "valid", "discount", "removeItem", "err", "clearTimeout", "handleBackToHomeDetail", "pop", "StarRating", "rating", "className", "children", "Array", "map", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showAcceptModal", "setShowAcceptModal", "showPromotionModal", "setShowPromotionModal", "handleApplyPromotionFromModal", "promotionData", "message", "checkHotelStatusBeforeBooking", "Promise", "resolve", "reject", "FETCH_DETAIL_HOTEL", "hotelId", "_id", "userId", "onSuccess", "hotel", "ownerStatus", "Error", "onFailed", "error", "onError", "createBooking", "console", "log", "bookingSubtotal", "params", "checkOutDate", "checkInDate", "totalPrice", "roomDetails", "serviceDetails", "_service$selectedDate", "selectDate", "saveReservationIdToBookingStack", "reservationId", "response", "create_booking", "status", "_response$data", "_response$data$unpaid", "_responseCheckout$dat", "unpaidReservation", "unpaidReservationId", "responseCheckout", "checkout_booking", "paymentUrl", "sessionUrl", "window", "location", "href", "_response$data2", "_response$data2$reser", "_responseCheckout$dat2", "reservation", "ErrorPage", "handleAccept", "handleConfirmBooking", "formatCurrency", "undefined", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "height", "role", "backgroundImage", "backgroundSize", "backgroundPosition", "paddingTop", "paddingBottom", "md", "lg", "backgroundColor", "borderRadius", "padding", "marginBottom", "justifyContent", "justifyItems", "star", "hotelName", "address", "margin", "xs", "fontSize", "getDate", "adults", "childrens", "name", "cursor", "onClick", "serviceTotal", "borderColor", "border", "Body", "variant", "size", "borderStyle", "borderWidth", "color", "Group", "Label", "Control", "value", "email", "placeholder", "phoneNumber", "Check", "id", "label", "checked", "onChange", "fontWeight", "show", "onHide", "onConfirm", "title", "confirmButtonText", "onApplyPromotion", "currentPromotionId", "onClose", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Customer/src/pages/customer/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Card,\r\n  Form,\r\n  Button,\r\n  InputGroup,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport Header from \"../Header\";\r\nimport Footer from \"../Footer\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport ConfirmationModal from \"@components/ConfirmationModal\";\r\nimport PromotionModal from \"./components/PromotionModal\";\r\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\r\nimport Utils from \"../../../utils/Utils\";\r\nimport Factories from \"../../../redux/search/factories\";\r\nimport { ChatBox } from \"./HomePage\";\r\nimport SearchActions from \"../../../redux/search/actions\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\nimport HotelClosedModal from \"./components/HotelClosedModal\";\r\n\r\nconst BookingCheckPage = () => {\r\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\r\n\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const SearchInformation = useAppSelector(\r\n    (state) => state.Search.SearchInformation\r\n  );\r\n  const selectedRoomsTemps = useAppSelector(\r\n    (state) => state.Search.selectedRooms\r\n  );\r\n  const selectedServicesFromRedux = useAppSelector(\r\n    (state) => state.Search.selectedServices\r\n  );\r\n  const hotelDetailFromRedux = useAppSelector(\r\n    (state) => state.Search.hotelDetail\r\n  );\r\n  const navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\r\n\r\n  // Promotion code state\r\n  const [promotionCode, setPromotionCode] = useState(\"\");\r\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\r\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\r\n  const [promotionId, setPromotionId] = useState(null);\r\n\r\n  // Add state for booking data\r\n  const [bookingData, setBookingData] = useState({\r\n    selectedRooms: selectedRoomsTemps || [],\r\n    selectedServices: selectedServicesFromRedux || [],\r\n    hotelDetail: hotelDetailFromRedux || null,\r\n    searchInfo: SearchInformation,\r\n  });\r\n\r\n  const [dataRestored, setDataRestored] = useState(false);\r\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\r\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\r\n\r\n  // Restore data from sessionStorage stack when component mounts\r\n  useEffect(() => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      const currentBooking = bookingStack[bookingStack.length - 1];\r\n      setBookingData(currentBooking);\r\n\r\n      // Update Redux store with current data\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: currentBooking.selectedRooms,\r\n          selectedServices: currentBooking.selectedServices,\r\n          hotelDetail: currentBooking.hotelDetail,\r\n        },\r\n      });\r\n    }\r\n    setDataRestored(true);\r\n  }, [dispatch]);\r\n\r\n  // Load promotion info from sessionStorage AFTER booking data is restored\r\n  useEffect(() => {\r\n    if (dataRestored) {\r\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\r\n      if (promo) {\r\n        setPromotionCode(promo.promotionCode || \"\");\r\n        setPromotionDiscount(promo.promotionDiscount || 0);\r\n        setPromotionMessage(promo.promotionMessage || \"\");\r\n        setPromotionId(promo.promotionId || null);\r\n      }\r\n    }\r\n  }, [dataRestored]);\r\n\r\n  // Save promotion info to sessionStorage when any promotion state changes\r\n  useEffect(() => {\r\n    if (dataRestored) { // Chỉ save khi đã restore xong data\r\n      sessionStorage.setItem(\r\n        \"promotionInfo\",\r\n        JSON.stringify({\r\n          promotionCode,\r\n          promotionDiscount,\r\n          promotionMessage,\r\n          promotionId,\r\n        })\r\n      );\r\n    }\r\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored]);\r\n\r\n  // Use bookingData instead of Redux state\r\n  const selectedRooms = bookingData.selectedRooms;\r\n  const selectedServices = bookingData.selectedServices;\r\n  const hotelDetail = bookingData.hotelDetail;\r\n  const searchInfo = bookingData.searchInfo;\r\n\r\n  // Calculate number of days between check-in and check-out\r\n  const calculateNumberOfDays = () => {\r\n    const checkIn = new Date(searchInfo.checkinDate);\r\n    const checkOut = new Date(searchInfo.checkoutDate);\r\n    const diffTime = Math.abs(checkOut - checkIn);\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    return diffDays;\r\n  };\r\n\r\n  const numberOfDays = calculateNumberOfDays();\r\n\r\n  // Calculate prices\r\n  const totalRoomPrice = selectedRooms.reduce(\r\n    (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n    0\r\n  );\r\n  const totalServicePrice = selectedServices.reduce((total, service) => {\r\n    const selectedDates = service.selectedDates || [];\r\n    const serviceQuantity = service.quantity * selectedDates.length;\r\n    return total + service.price * serviceQuantity;\r\n  }, 0);\r\n  const subtotal = totalRoomPrice + totalServicePrice;\r\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\r\n\r\n  // Validate promotion when data is restored or booking changes\r\n  useEffect(() => {\r\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\r\n\r\n    // Add a small delay to ensure promotion is fully restored before validation\r\n    const timeoutId = setTimeout(() => {\r\n      const validatePromotion = async () => {\r\n        setIsValidatingPromotion(true);\r\n        try {\r\n          const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\r\n            code: promotionCode,\r\n            orderAmount: subtotal,\r\n          });\r\n\r\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\r\n            // Promotion is no longer valid or discount changed\r\n            setPromotionCode(\"\");\r\n            setPromotionDiscount(0);\r\n            setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\r\n            setPromotionId(null);\r\n            sessionStorage.removeItem(\"promotionInfo\");\r\n          }\r\n        } catch (err) {\r\n          // Promotion validation failed\r\n          setPromotionCode(\"\");\r\n          setPromotionDiscount(0);\r\n          setPromotionMessage(\"Promotion is no longer valid\");\r\n          setPromotionId(null);\r\n          sessionStorage.removeItem(\"promotionInfo\");\r\n        } finally {\r\n          setIsValidatingPromotion(false);\r\n        }\r\n      };\r\n\r\n      validatePromotion();\r\n    }, 100);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [dataRestored, subtotal, promotionCode, promotionId, promotionDiscount]); // Validate when subtotal changes or data is restored\r\n\r\n  // Handle navigation back to HomeDetailPage\r\n  const handleBackToHomeDetail = () => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      // Remove the current booking from stack\r\n      bookingStack.pop();\r\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n    }\r\n    navigate(-1);\r\n  };\r\n\r\n  // Star rating component\r\n  const StarRating = ({ rating }) => {\r\n    return (\r\n      <div className=\"star-rating\">\r\n        {[...Array(5)].map((_, index) =>\r\n          index < rating ? (\r\n            <FaStar key={index} className=\"star filled\" />\r\n          ) : (\r\n            <FaRegStar key={index} className=\"star\" />\r\n          )\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\r\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\r\n\r\n  // Hàm xử lý áp dụng promotion từ modal\r\n  const handleApplyPromotionFromModal = (promotionData) => {\r\n    setPromotionCode(promotionData.code);\r\n    setPromotionDiscount(promotionData.discount);\r\n    setPromotionMessage(promotionData.message);\r\n    setPromotionId(promotionData.promotionId);\r\n  };\r\n\r\n  // Function to check hotel status before booking\r\n  const checkHotelStatusBeforeBooking = async () => {\r\n    return new Promise((resolve, reject) => {\r\n      setIsCheckingHotelStatus(true);\r\n      dispatch({\r\n        type: HotelActions.FETCH_DETAIL_HOTEL,\r\n        payload: {\r\n          hotelId: hotelDetail._id,\r\n          userId: Auth._id,\r\n          onSuccess: (hotel) => {\r\n            setIsCheckingHotelStatus(false);\r\n            if (hotel.ownerStatus === \"ACTIVE\") {\r\n              resolve(hotel);\r\n            } else {\r\n              reject(new Error(\"Hotel is currently inactive\"));\r\n            }\r\n          },\r\n          onFailed: (error) => {\r\n            setIsCheckingHotelStatus(false);\r\n            reject(new Error(error || \"Failed to check hotel status\"));\r\n          },\r\n          onError: (error) => {\r\n            setIsCheckingHotelStatus(false);\r\n            reject(new Error(\"Server error while checking hotel status\"));\r\n          }\r\n        },\r\n      });\r\n    });\r\n  };\r\n\r\n  const createBooking = async () => {\r\n    try {\r\n      // Check hotel status first\r\n      const hotel = await checkHotelStatusBeforeBooking();\r\n      console.log(\"Hotel detail fetched successfully:\", hotel);\r\n            const totalRoomPrice = selectedRooms.reduce(\r\n              (total, { room, amount }) =>\r\n                total + room.price * amount * numberOfDays,\r\n              0\r\n            );\r\n\r\n            const totalServicePrice = selectedServices.reduce(\r\n              (total, service) => {\r\n                const selectedDates = service.selectedDates || [];\r\n                const serviceQuantity = service.quantity * selectedDates.length;\r\n                return total + service.price * serviceQuantity;\r\n              },\r\n              0\r\n            );\r\n\r\n            const bookingSubtotal = totalRoomPrice + totalServicePrice;\r\n\r\n            const params = {\r\n              hotelId: hotelDetail._id,\r\n              checkOutDate: searchInfo.checkoutDate,\r\n              checkInDate: searchInfo.checkinDate,\r\n              totalPrice: bookingSubtotal, // giá gốc\r\n              finalPrice: finalPrice, // giá sau giảm giá\r\n              roomDetails: selectedRooms.map(({ room, amount }) => ({\r\n                room: {\r\n                  _id: room._id,\r\n                },\r\n                amount: amount,\r\n              })),\r\n              serviceDetails: selectedServices.map((service) => ({\r\n                _id: service._id,\r\n                quantity:\r\n                  service.quantity * (service.selectedDates?.length || 0),\r\n                selectDate: service.selectedDates || [],\r\n              })),\r\n              // Thêm promotionId và promotionDiscount nếu có\r\n              ...(promotionId && { promotionId }),\r\n              ...(promotionDiscount > 0 && { promotionDiscount }),\r\n            };\r\n\r\n            console.log(\"params >> \", params);\r\n\r\n            // Helper function to save reservationId to bookingStack\r\n            const saveReservationIdToBookingStack = (reservationId) => {\r\n              if (reservationId) {\r\n                const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n                if (bookingStack.length > 0) {\r\n                  bookingStack[bookingStack.length - 1].reservationId = reservationId;\r\n                  sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n                }\r\n              }\r\n            };\r\n            try {\r\n              let reservationId = null;\r\n              const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n              if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\r\n                reservationId = bookingStack[bookingStack.length - 1].reservationId;\r\n              }\r\n              const response = await Factories.create_booking({ ...params, reservationId });\r\n              console.log(\"response >> \", response);\r\n              if (response?.status === 200) {\r\n                reservationId = response?.data?.unpaidReservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const unpaidReservationId = reservationId;\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  unpaidReservationId\r\n                );\r\n                console.log(\"responseCheckout >> \", responseCheckout);\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else if (response?.status === 201) {\r\n                reservationId = response?.data?.reservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  reservationId\r\n                );\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else {\r\n                console.log(\"error create booking\");\r\n              }\r\n            } catch (error) {\r\n              console.error(\"Error create payment: \", error);\r\n              navigate(Routers.ErrorPage);\r\n            }\r\n    } catch (error) {\r\n      console.error(\"Error checking hotel status:\", error);\r\n      setShowModalStatusBooking(true);\r\n    }\r\n  };\r\n\r\n  const handleAccept = () => {\r\n    const totalRoomPrice = selectedRooms.reduce(\r\n      (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n      0\r\n    );\r\n\r\n    if (totalRoomPrice > 0) {\r\n      createBooking();\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: [],\r\n          selectedServices: [],\r\n          hotelDetail: hotelDetail,\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleConfirmBooking = () => {\r\n    setShowAcceptModal(true);\r\n  };\r\n\r\n  const formatCurrency = (amount) => {\r\n    if (amount === undefined || amount === null) return \"$0\";\r\n    return new Intl.NumberFormat(\"en-US\", {\r\n      style: \"currency\",\r\n      currency: \"USD\",\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 0,\r\n    }).format(amount);\r\n  };\r\n\r\n  // Add null check for hotelDetail\r\n  if (!hotelDetail) {\r\n    return (\r\n      <div\r\n        className=\"d-flex justify-content-center align-items-center\"\r\n        style={{ height: \"100vh\" }}\r\n      >\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"d-flex flex-column min-vh-100\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Header />\r\n      <div\r\n        className=\"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\"\r\n        style={{ paddingTop: \"65px\", paddingBottom: \"50px\" }}\r\n      >\r\n        <Container className=\"mt-4\">\r\n          <Row className=\"justify-content-center\">\r\n            {/* Left Card - Booking Details */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"booking-card text-white\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  marginBottom: \"20px\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"stars mb-2\"\r\n                  style={{\r\n                    justifyContent: \"flex-start\",\r\n                    justifyItems: \"self-start\",\r\n                  }}\r\n                >\r\n                  <StarRating rating={hotelDetail.star} />\r\n                </div>\r\n\r\n                <h4 className=\"hotel-name mb-1\">\r\n                  {hotelDetail.hotelName ?? \"\"}\r\n                </h4>\r\n\r\n                <p className=\"hotel-address small mb-4\">\r\n                  {hotelDetail.address ?? \"\"}\r\n                </p>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <h5 className=\"mb-4\">Your booking detail</h5>\r\n\r\n                <Row className=\"mb-4\">\r\n                  <Col xs={6}>\r\n                    <div className=\"checkin\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkin\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkinDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                  <Col xs={6}>\r\n                    <div className=\"checkout\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkout\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkoutDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n\r\n                <div className=\"stay-info mb-2\">\r\n                  <div className=\"d-flex justify-content-between mb-2\">\r\n                    <span>Total length of stay:</span>\r\n                    <span className=\"fw-bold\">{numberOfDays} night</span>{\" \"}\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between mb-3\">\r\n                    <span>Total number of people:</span>\r\n                    <span className=\"fw-bold\">\r\n                      {searchInfo.adults} Adults - {searchInfo.childrens}{\" \"}\r\n                      Childrens\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"selected-room mb-2\">\r\n                  <h5 className=\"mb-4\">You selected</h5>\r\n\r\n                  {selectedRooms.map(({ room, amount }) => (\r\n                    <div\r\n                      key={room._id}\r\n                      className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                    >\r\n                      <span>\r\n                        {amount} x {room.name} ({numberOfDays} days):\r\n                      </span>\r\n                      <span className=\"fw-bold\">\r\n                        {Utils.formatCurrency(\r\n                          room.price * amount * numberOfDays\r\n                        )}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n\r\n                  <div className=\"small mb-3\">\r\n                    <a\r\n                      className=\"text-blue text-decoration-none\"\r\n                      style={{ cursor: \"pointer\" }}\r\n                      onClick={handleBackToHomeDetail}\r\n                    >\r\n                      Change your selection\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Selected Services Section */}\r\n                {selectedServices.length > 0 && (\r\n                  <div className=\"selected-services mb-2\">\r\n                    <h5 className=\"mb-3\">Selected Services</h5>\r\n\r\n                    {selectedServices.map((service) => {\r\n                      const selectedDates = service.selectedDates || [];\r\n                      const serviceQuantity =\r\n                        service.quantity * selectedDates.length;\r\n                      const serviceTotal = service.price * serviceQuantity;\r\n\r\n                      return (\r\n                        <div\r\n                          key={service._id}\r\n                          className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                        >\r\n                          <span>\r\n                            {service.quantity} x {service.name} (\r\n                            {selectedDates.length} days):\r\n                          </span>\r\n                          <span className=\"fw-bold\">\r\n                            {Utils.formatCurrency(serviceTotal)}\r\n                          </span>\r\n                        </div>\r\n                      );\r\n                    })}\r\n\r\n                    <div className=\"small mb-3\">\r\n                      <a\r\n                        className=\"text-blue text-decoration-none\"\r\n                        style={{ cursor: \"pointer\" }}\r\n                        onClick={() => {\r\n                          dispatch({\r\n                            type: SearchActions.SAVE_SELECTED_ROOMS,\r\n                            payload: {\r\n                              selectedRooms: selectedRooms,\r\n                              selectedServices: selectedServices,\r\n                              hotelDetail: hotelDetail,\r\n                            },\r\n                          });\r\n                          navigate(-1);\r\n                        }}\r\n                      >\r\n                        Change your selection\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"promotion-section mb-3\">\r\n                  {/* Current applied promotion display */}\r\n                  {promotionDiscount > 0 ? (\r\n                    <Card \r\n                      className=\"promotion-applied mb-3\"\r\n                      style={{ \r\n                        backgroundColor: \"rgba(40, 167, 69, 0.2)\", \r\n                        borderColor: \"#28a745\",\r\n                        border: \"2px solid #28a745\"\r\n                      }}\r\n                    >\r\n                      <Card.Body className=\"py-2\">\r\n                        <div className=\"d-flex justify-content-between align-items-center\">\r\n                          <div>\r\n                            <div className=\"d-flex align-items-center\">\r\n                              <FaTag className=\"text-success me-2\" />\r\n                              <span className=\"fw-bold text-success\">{promotionCode}</span>\r\n                            </div>\r\n                            <small className=\"text-success\">\r\n                              Save {Utils.formatCurrency(promotionDiscount)}\r\n                            </small>\r\n                          </div>\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            size=\"sm\"\r\n                            onClick={() => handleApplyPromotionFromModal({\r\n                              code: \"\",\r\n                              discount: 0,\r\n                              message: \"\",\r\n                              promotionId: null\r\n                            })}\r\n                            className=\"d-flex align-items-center\"\r\n                          >\r\n                            <FaTimes className=\"me-1\" />\r\n                            Remove\r\n                          </Button>\r\n                        </div>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  ) : (\r\n                    <div className=\"text-center py-3 mb-3\" style={{ \r\n                      border: \"2px dashed rgba(255,255,255,0.3)\", \r\n                      borderRadius: \"8px\",\r\n                      backgroundColor: \"rgba(255,255,255,0.05)\"\r\n                    }}>\r\n                      <FaTag className=\"text-muted mb-2\" size={24} />\r\n                      <div className=\"text-muted small\">No promotion applied</div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Button to open promotion modal */}\r\n                  <Button\r\n                    variant=\"outline-light\"\r\n                    className=\"w-100 d-flex align-items-center justify-content-center\"\r\n                    onClick={() => setShowPromotionModal(true)}\r\n                    style={{ \r\n                      borderStyle: \"dashed\",\r\n                      borderWidth: \"2px\",\r\n                      padding: \"12px\"\r\n                    }}\r\n                  >\r\n                    <FaTag className=\"me-2\" />\r\n                    {promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"}\r\n                  </Button>\r\n                  \r\n                  {/* Promotion message */}\r\n                  {promotionMessage && (\r\n                    <div\r\n                      className={`mt-2 small text-center ${\r\n                        promotionDiscount > 0 ? \"text-success\" : \"text-danger\"\r\n                      }`}\r\n                    >\r\n                      {promotionMessage}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"total-price\">\r\n                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                    <h5 className=\"text-danger mb-0\">\r\n                      Total: {Utils.formatCurrency(finalPrice)}\r\n                    </h5>\r\n                  </div>\r\n                  <div className=\"small\">Includes taxes and fees</div>\r\n                </div>\r\n              </Card>\r\n            </Col>\r\n\r\n            {/* Right Card - Customer Information */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"info-card\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  color: \"white\",\r\n                }}\r\n              >\r\n                <h4 className=\"mb-4\">Check your information</h4>\r\n\r\n                <Form>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Full name</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={Auth.name}\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Email</Form.Label>\r\n                    <Form.Control\r\n                      type=\"email\"\r\n                      value={Auth.email}\r\n                      placeholder=\"<EMAIL>\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Phone</Form.Label>\r\n                    <Form.Control\r\n                      type=\"tel\"\r\n                      value={Auth.phoneNumber}\r\n                      placeholder=\"0912345678\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Who are you booking for?</Form.Label>\r\n                    <div>\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"mainGuest\"\r\n                        label=\"I'm the main guest\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"mainGuest\"}\r\n                        onChange={() => setBookingFor(\"mainGuest\")}\r\n                        className=\"mb-2\"\r\n                      />\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"someoneElse\"\r\n                        label=\"I'm booking for someone else\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"someoneElse\"}\r\n                        onChange={() => setBookingFor(\"someoneElse\")}\r\n                      />\r\n                    </div>\r\n                  </Form.Group>\r\n\r\n                  <div className=\"text-center\">\r\n                    <Button\r\n                      className=\"px-4 py-2\"\r\n                      style={{\r\n                        borderRadius: \"10px\",\r\n                        backgroundColor: \"white\",\r\n                        color: \"#007bff\",\r\n                        border: \"none\",\r\n                        fontWeight: \"bold\",\r\n                      }}\r\n                      onClick={handleConfirmBooking}\r\n                    >\r\n                      Booking\r\n                    </Button>\r\n                    {/* Accept Confirmation Modal */}\r\n                    <ConfirmationModal\r\n                      show={showAcceptModal}\r\n                      onHide={() => setShowAcceptModal(false)}\r\n                      onConfirm={handleAccept}\r\n                      title=\"Confirm Acceptance\"\r\n                      message=\"Do you want to proceed with this booking confirmation?\"\r\n                      confirmButtonText=\"Accept\"\r\n                      type=\"accept\"\r\n                    />\r\n                  </div>\r\n                </Form>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n        <div>\r\n          <ChatBox />\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n      \r\n      {/* Promotion Modal */}\r\n      <PromotionModal\r\n        show={showPromotionModal}\r\n        onHide={() => setShowPromotionModal(false)}\r\n        totalPrice={totalPrice}\r\n        onApplyPromotion={handleApplyPromotionFromModal}\r\n        currentPromotionId={promotionId}\r\n      />\r\n      \r\n      <HotelClosedModal\r\n        show={showModalStatusBooking}\r\n        onClose={() => {\r\n          setShowModalStatusBooking(false);\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BookingCheckPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,UAAU,QACL,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SAASC,cAAc,EAAEC,cAAc,QAAQ,sBAAsB;AACrE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,SAAS,MAAM,iCAAiC;AACvD,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC7B,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAE3E,MAAMqC,IAAI,GAAGhB,cAAc,CAAEiB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;EACvD,MAAME,iBAAiB,GAAGlB,cAAc,CACrCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACD,iBAC1B,CAAC;EACD,MAAME,kBAAkB,GAAGpB,cAAc,CACtCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACE,aAC1B,CAAC;EACD,MAAMC,yBAAyB,GAAGtB,cAAc,CAC7CiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACI,gBAC1B,CAAC;EACD,MAAMC,oBAAoB,GAAGxB,cAAc,CACxCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACM,WAC1B,CAAC;EACD,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,QAAQ,GAAG1B,cAAc,CAAC,CAAC;EACjC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,WAAW,CAAC;;EAEzD;EACA,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC;IAC7C0C,aAAa,EAAED,kBAAkB,IAAI,EAAE;IACvCG,gBAAgB,EAAED,yBAAyB,IAAI,EAAE;IACjDG,WAAW,EAAED,oBAAoB,IAAI,IAAI;IACzCgB,UAAU,EAAEtB;EACd,CAAC,CAAC;EAEF,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmE,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,cAAc,GAAGN,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC;MAC5Db,cAAc,CAACc,cAAc,CAAC;;MAE9B;MACA1B,QAAQ,CAAC;QACP2B,IAAI,EAAEjD,aAAa,CAACkD,mBAAmB;QACvCC,OAAO,EAAE;UACPnC,aAAa,EAAEgC,cAAc,CAAChC,aAAa;UAC3CE,gBAAgB,EAAE8B,cAAc,CAAC9B,gBAAgB;UACjDE,WAAW,EAAE4B,cAAc,CAAC5B;QAC9B;MACF,CAAC,CAAC;IACJ;IACAiB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;;EAEd;EACA/C,SAAS,CAAC,MAAM;IACd,IAAI6D,YAAY,EAAE;MAChB,MAAMgB,KAAK,GAAGT,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,MAAM,CAAC;MAC3E,IAAIM,KAAK,EAAE;QACT1B,gBAAgB,CAAC0B,KAAK,CAAC3B,aAAa,IAAI,EAAE,CAAC;QAC3CG,oBAAoB,CAACwB,KAAK,CAACzB,iBAAiB,IAAI,CAAC,CAAC;QAClDG,mBAAmB,CAACsB,KAAK,CAACvB,gBAAgB,IAAI,EAAE,CAAC;QACjDG,cAAc,CAACoB,KAAK,CAACrB,WAAW,IAAI,IAAI,CAAC;MAC3C;IACF;EACF,CAAC,EAAE,CAACK,YAAY,CAAC,CAAC;;EAElB;EACA7D,SAAS,CAAC,MAAM;IACd,IAAI6D,YAAY,EAAE;MAAE;MAClBS,cAAc,CAACQ,OAAO,CACpB,eAAe,EACfV,IAAI,CAACW,SAAS,CAAC;QACb7B,aAAa;QACbE,iBAAiB;QACjBE,gBAAgB;QAChBE;MACF,CAAC,CACH,CAAC;IACH;EACF,CAAC,EAAE,CAACN,aAAa,EAAEE,iBAAiB,EAAEE,gBAAgB,EAAEE,WAAW,EAAEK,YAAY,CAAC,CAAC;;EAEnF;EACA,MAAMpB,aAAa,GAAGiB,WAAW,CAACjB,aAAa;EAC/C,MAAME,gBAAgB,GAAGe,WAAW,CAACf,gBAAgB;EACrD,MAAME,WAAW,GAAGa,WAAW,CAACb,WAAW;EAC3C,MAAMe,UAAU,GAAGF,WAAW,CAACE,UAAU;;EAEzC;EACA,MAAMoB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACtB,UAAU,CAACuB,WAAW,CAAC;IAChD,MAAMC,QAAQ,GAAG,IAAIF,IAAI,CAACtB,UAAU,CAACyB,YAAY,CAAC;IAClD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAGH,OAAO,CAAC;IAC7C,MAAMQ,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB,CAAC;EAED,MAAME,YAAY,GAAGX,qBAAqB,CAAC,CAAC;;EAE5C;EACA,MAAMY,cAAc,GAAGnD,aAAa,CAACoD,MAAM,CACzC,CAACC,KAAK,EAAE;IAAEC,IAAI;IAAEC;EAAO,CAAC,KAAKF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EACvE,CACF,CAAC;EACD,MAAMO,iBAAiB,GAAGvD,gBAAgB,CAACkD,MAAM,CAAC,CAACC,KAAK,EAAEK,OAAO,KAAK;IACpE,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;IACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC5B,MAAM;IAC/D,OAAOsB,KAAK,GAAGK,OAAO,CAACF,KAAK,GAAGI,eAAe;EAChD,CAAC,EAAE,CAAC,CAAC;EACL,MAAME,QAAQ,GAAGX,cAAc,GAAGM,iBAAiB;EACnD,MAAMM,UAAU,GAAGjB,IAAI,CAACkB,GAAG,CAACF,QAAQ,GAAGnD,iBAAiB,EAAE,CAAC,CAAC;;EAE5D;EACApD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6D,YAAY,IAAI,CAACX,aAAa,IAAI,CAACM,WAAW,IAAIJ,iBAAiB,KAAK,CAAC,EAAE;;IAEhF;IACA,MAAMsD,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpC5C,wBAAwB,CAAC,IAAI,CAAC;QAC9B,IAAI;UACF,MAAM6C,GAAG,GAAG,MAAM5G,KAAK,CAAC6G,IAAI,CAAC,4CAA4C,EAAE;YACzEC,IAAI,EAAE7D,aAAa;YACnB8D,WAAW,EAAET;UACf,CAAC,CAAC;UAEF,IAAI,CAACM,GAAG,CAACI,IAAI,CAACC,KAAK,IAAIL,GAAG,CAACI,IAAI,CAACE,QAAQ,KAAK/D,iBAAiB,EAAE;YAC9D;YACAD,gBAAgB,CAAC,EAAE,CAAC;YACpBE,oBAAoB,CAAC,CAAC,CAAC;YACvBE,mBAAmB,CAAC,qDAAqD,CAAC;YAC1EE,cAAc,CAAC,IAAI,CAAC;YACpBa,cAAc,CAAC8C,UAAU,CAAC,eAAe,CAAC;UAC5C;QACF,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZ;UACAlE,gBAAgB,CAAC,EAAE,CAAC;UACpBE,oBAAoB,CAAC,CAAC,CAAC;UACvBE,mBAAmB,CAAC,8BAA8B,CAAC;UACnDE,cAAc,CAAC,IAAI,CAAC;UACpBa,cAAc,CAAC8C,UAAU,CAAC,eAAe,CAAC;QAC5C,CAAC,SAAS;UACRpD,wBAAwB,CAAC,KAAK,CAAC;QACjC;MACF,CAAC;MAED4C,iBAAiB,CAAC,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMU,YAAY,CAACZ,SAAS,CAAC;EACtC,CAAC,EAAE,CAAC7C,YAAY,EAAE0C,QAAQ,EAAErD,aAAa,EAAEM,WAAW,EAAEJ,iBAAiB,CAAC,CAAC,CAAC,CAAC;;EAE7E;EACA,MAAMmE,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMpD,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B;MACAL,YAAY,CAACqD,GAAG,CAAC,CAAC;MAClBlD,cAAc,CAACQ,OAAO,CAAC,cAAc,EAAEV,IAAI,CAACW,SAAS,CAACZ,YAAY,CAAC,CAAC;IACtE;IACArB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAM2E,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,KAAK;IACjC,oBACE7F,OAAA;MAAK8F,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC1BA,KAAK,GAAGN,MAAM,gBACZ7F,OAAA,CAACpB,MAAM;QAAakH,SAAS,EAAC;MAAa,GAA9BK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2B,CAAC,gBAE9CvG,OAAA,CAACnB,SAAS;QAAaiH,SAAS,EAAC;MAAM,GAAvBK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAE7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvI,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzI,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAM0I,6BAA6B,GAAIC,aAAa,IAAK;IACvDvF,gBAAgB,CAACuF,aAAa,CAAC3B,IAAI,CAAC;IACpC1D,oBAAoB,CAACqF,aAAa,CAACvB,QAAQ,CAAC;IAC5C5D,mBAAmB,CAACmF,aAAa,CAACC,OAAO,CAAC;IAC1ClF,cAAc,CAACiF,aAAa,CAAClF,WAAW,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMoF,6BAA6B,GAAG,MAAAA,CAAA,KAAY;IAChD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC7E,wBAAwB,CAAC,IAAI,CAAC;MAC9BnB,QAAQ,CAAC;QACP2B,IAAI,EAAEhD,YAAY,CAACsH,kBAAkB;QACrCpE,OAAO,EAAE;UACPqE,OAAO,EAAEpG,WAAW,CAACqG,GAAG;UACxBC,MAAM,EAAE/G,IAAI,CAAC8G,GAAG;UAChBE,SAAS,EAAGC,KAAK,IAAK;YACpBnF,wBAAwB,CAAC,KAAK,CAAC;YAC/B,IAAImF,KAAK,CAACC,WAAW,KAAK,QAAQ,EAAE;cAClCR,OAAO,CAACO,KAAK,CAAC;YAChB,CAAC,MAAM;cACLN,MAAM,CAAC,IAAIQ,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAClD;UACF,CAAC;UACDC,QAAQ,EAAGC,KAAK,IAAK;YACnBvF,wBAAwB,CAAC,KAAK,CAAC;YAC/B6E,MAAM,CAAC,IAAIQ,KAAK,CAACE,KAAK,IAAI,8BAA8B,CAAC,CAAC;UAC5D,CAAC;UACDC,OAAO,EAAGD,KAAK,IAAK;YAClBvF,wBAAwB,CAAC,KAAK,CAAC;YAC/B6E,MAAM,CAAC,IAAIQ,KAAK,CAAC,0CAA0C,CAAC,CAAC;UAC/D;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF;MACA,MAAMN,KAAK,GAAG,MAAMT,6BAA6B,CAAC,CAAC;MACnDgB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAER,KAAK,CAAC;MAClD,MAAMzD,cAAc,GAAGnD,aAAa,CAACoD,MAAM,CACzC,CAACC,KAAK,EAAE;QAAEC,IAAI;QAAEC;MAAO,CAAC,KACtBF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EAC5C,CACF,CAAC;MAED,MAAMO,iBAAiB,GAAGvD,gBAAgB,CAACkD,MAAM,CAC/C,CAACC,KAAK,EAAEK,OAAO,KAAK;QAClB,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;QACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC5B,MAAM;QAC/D,OAAOsB,KAAK,GAAGK,OAAO,CAACF,KAAK,GAAGI,eAAe;MAChD,CAAC,EACD,CACF,CAAC;MAED,MAAMyD,eAAe,GAAGlE,cAAc,GAAGM,iBAAiB;MAE1D,MAAM6D,MAAM,GAAG;QACbd,OAAO,EAAEpG,WAAW,CAACqG,GAAG;QACxBc,YAAY,EAAEpG,UAAU,CAACyB,YAAY;QACrC4E,WAAW,EAAErG,UAAU,CAACuB,WAAW;QACnC+E,UAAU,EAAEJ,eAAe;QAAE;QAC7BtD,UAAU,EAAEA,UAAU;QAAE;QACxB2D,WAAW,EAAE1H,aAAa,CAACqF,GAAG,CAAC,CAAC;UAAE/B,IAAI;UAAEC;QAAO,CAAC,MAAM;UACpDD,IAAI,EAAE;YACJmD,GAAG,EAAEnD,IAAI,CAACmD;UACZ,CAAC;UACDlD,MAAM,EAAEA;QACV,CAAC,CAAC,CAAC;QACHoE,cAAc,EAAEzH,gBAAgB,CAACmF,GAAG,CAAE3B,OAAO;UAAA,IAAAkE,qBAAA;UAAA,OAAM;YACjDnB,GAAG,EAAE/C,OAAO,CAAC+C,GAAG;YAChB5C,QAAQ,EACNH,OAAO,CAACG,QAAQ,IAAI,EAAA+D,qBAAA,GAAAlE,OAAO,CAACC,aAAa,cAAAiE,qBAAA,uBAArBA,qBAAA,CAAuB7F,MAAM,KAAI,CAAC,CAAC;YACzD8F,UAAU,EAAEnE,OAAO,CAACC,aAAa,IAAI;UACvC,CAAC;QAAA,CAAC,CAAC;QACH;QACA,IAAI5C,WAAW,IAAI;UAAEA;QAAY,CAAC,CAAC;QACnC,IAAIJ,iBAAiB,GAAG,CAAC,IAAI;UAAEA;QAAkB,CAAC;MACpD,CAAC;MAEDwG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEE,MAAM,CAAC;;MAEjC;MACA,MAAMQ,+BAA+B,GAAIC,aAAa,IAAK;QACzD,IAAIA,aAAa,EAAE;UACjB,MAAMrG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;UAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;YAC3BL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACgG,aAAa,GAAGA,aAAa;YACnElG,cAAc,CAACQ,OAAO,CAAC,cAAc,EAAEV,IAAI,CAACW,SAAS,CAACZ,YAAY,CAAC,CAAC;UACtE;QACF;MACF,CAAC;MACD,IAAI;QACF,IAAIqG,aAAa,GAAG,IAAI;QACxB,MAAMrG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,IAAIL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACgG,aAAa,EAAE;UAClFA,aAAa,GAAGrG,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACgG,aAAa;QACrE;QACA,MAAMC,QAAQ,GAAG,MAAMlJ,SAAS,CAACmJ,cAAc,CAAC;UAAE,GAAGX,MAAM;UAAES;QAAc,CAAC,CAAC;QAC7EZ,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEY,QAAQ,CAAC;QACrC,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,qBAAA;UAC5BN,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAG,cAAA,GAARH,QAAQ,CAAExD,IAAI,cAAA2D,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBG,iBAAiB,cAAAF,qBAAA,uBAAjCA,qBAAA,CAAmC3B,GAAG;UACtDqB,+BAA+B,CAACC,aAAa,CAAC;UAC9C,MAAMQ,mBAAmB,GAAGR,aAAa;UACzC,MAAMS,gBAAgB,GAAG,MAAM1J,SAAS,CAAC2J,gBAAgB,CACvDF,mBACF,CAAC;UACDpB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoB,gBAAgB,CAAC;UACrD,MAAME,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAH,qBAAA,GAAhBG,gBAAgB,CAAEhE,IAAI,cAAA6D,qBAAA,uBAAtBA,qBAAA,CAAwBM,UAAU;UACrD,IAAID,UAAU,EAAE;YACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;UACnC;QACF,CAAC,MAAM,IAAI,CAAAV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAAA,IAAAa,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACnClB,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAe,eAAA,GAARf,QAAQ,CAAExD,IAAI,cAAAuE,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBG,WAAW,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6BvC,GAAG;UAChDqB,+BAA+B,CAACC,aAAa,CAAC;UAC9C,MAAMS,gBAAgB,GAAG,MAAM1J,SAAS,CAAC2J,gBAAgB,CACvDV,aACF,CAAC;UACD,MAAMW,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAS,sBAAA,GAAhBT,gBAAgB,CAAEhE,IAAI,cAAAyE,sBAAA,uBAAtBA,sBAAA,CAAwBN,UAAU;UACrD,IAAID,UAAU,EAAE;YACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;UACnC;QACF,CAAC,MAAM;UACLvB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACrC;MACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdG,OAAO,CAACH,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C3G,QAAQ,CAAC9B,OAAO,CAAC4K,SAAS,CAAC;MAC7B;IACR,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDtH,yBAAyB,CAAC,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAM0J,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMjG,cAAc,GAAGnD,aAAa,CAACoD,MAAM,CACzC,CAACC,KAAK,EAAE;MAAEC,IAAI;MAAEC;IAAO,CAAC,KAAKF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EACvE,CACF,CAAC;IAED,IAAIC,cAAc,GAAG,CAAC,EAAE;MACtB+D,aAAa,CAAC,CAAC;MACf5G,QAAQ,CAAC;QACP2B,IAAI,EAAEjD,aAAa,CAACkD,mBAAmB;QACvCC,OAAO,EAAE;UACPnC,aAAa,EAAE,EAAE;UACjBE,gBAAgB,EAAE,EAAE;UACpBE,WAAW,EAAEA;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMiJ,oBAAoB,GAAGA,CAAA,KAAM;IACjCxD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMyD,cAAc,GAAI/F,MAAM,IAAK;IACjC,IAAIA,MAAM,KAAKgG,SAAS,IAAIhG,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;IACxD,OAAO,IAAIiG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACvG,MAAM,CAAC;EACnB,CAAC;;EAED;EACA,IAAI,CAACnD,WAAW,EAAE;IAChB,oBACEhB,OAAA;MACE8F,SAAS,EAAC,kDAAkD;MAC5DwE,KAAK,EAAE;QAAEK,MAAM,EAAE;MAAQ,CAAE;MAAA5E,QAAA,eAE3B/F,OAAA;QAAK8F,SAAS,EAAC,6BAA6B;QAAC8E,IAAI,EAAC,QAAQ;QAAA7E,QAAA,eACxD/F,OAAA;UAAM8F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvG,OAAA;IACE8F,SAAS,EAAC,+BAA+B;IACzCwE,KAAK,EAAE;MACLO,eAAe,EAAE,OAAO7L,MAAM,GAAG;MACjC8L,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAAhF,QAAA,gBAEF/F,OAAA,CAACf,MAAM;MAAAmH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVvG,OAAA;MACE8F,SAAS,EAAC,8EAA8E;MACxFwE,KAAK,EAAE;QAAEU,UAAU,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAO,CAAE;MAAAlF,QAAA,gBAErD/F,OAAA,CAAC3B,SAAS;QAACyH,SAAS,EAAC,MAAM;QAAAC,QAAA,eACzB/F,OAAA,CAAC1B,GAAG;UAACwH,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAErC/F,OAAA,CAACzB,GAAG;YAAC2M,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAApF,QAAA,eAChB/F,OAAA,CAACxB,IAAI;cACHsH,SAAS,EAAC,yBAAyB;cACnCwE,KAAK,EAAE;gBACLc,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE;cAChB,CAAE;cAAAxF,QAAA,gBAEF/F,OAAA;gBACE8F,SAAS,EAAC,YAAY;gBACtBwE,KAAK,EAAE;kBACLkB,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE;gBAChB,CAAE;gBAAA1F,QAAA,eAEF/F,OAAA,CAAC4F,UAAU;kBAACC,MAAM,EAAE7E,WAAW,CAAC0K;gBAAK;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAENvG,OAAA;gBAAI8F,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAA5F,qBAAA,GAC5Ba,WAAW,CAAC2K,SAAS,cAAAxL,qBAAA,cAAAA,qBAAA,GAAI;cAAE;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAELvG,OAAA;gBAAG8F,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GAAA3F,oBAAA,GACpCY,WAAW,CAAC4K,OAAO,cAAAxL,oBAAA,cAAAA,oBAAA,GAAI;cAAE;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEJvG,OAAA;gBACE8F,SAAS,EAAC,sBAAsB;gBAChCwE,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAAzF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPvG,OAAA;gBAAI8F,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE7CvG,OAAA,CAAC1B,GAAG;gBAACwH,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB/F,OAAA,CAACzB,GAAG;kBAACuN,EAAE,EAAE,CAAE;kBAAA/F,QAAA,eACT/F,OAAA;oBAAK8F,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtB/F,OAAA;sBACE8F,SAAS,EAAC,oBAAoB;sBAC9BwE,KAAK,EAAE;wBAAEyB,QAAQ,EAAE;sBAAG,CAAE;sBAAAhG,QAAA,EACzB;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNvG,OAAA;sBAAK8F,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBtG,KAAK,CAACuM,OAAO,CAACjK,UAAU,CAACuB,WAAW,EAAE,CAAC;oBAAC;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvG,OAAA,CAACzB,GAAG;kBAACuN,EAAE,EAAE,CAAE;kBAAA/F,QAAA,eACT/F,OAAA;oBAAK8F,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvB/F,OAAA;sBACE8F,SAAS,EAAC,oBAAoB;sBAC9BwE,KAAK,EAAE;wBAAEyB,QAAQ,EAAE;sBAAG,CAAE;sBAAAhG,QAAA,EACzB;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNvG,OAAA;sBAAK8F,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBtG,KAAK,CAACuM,OAAO,CAACjK,UAAU,CAACyB,YAAY,EAAE,CAAC;oBAAC;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvG,OAAA;gBAAK8F,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B/F,OAAA;kBAAK8F,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClD/F,OAAA;oBAAA+F,QAAA,EAAM;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClCvG,OAAA;oBAAM8F,SAAS,EAAC,SAAS;oBAAAC,QAAA,GAAEjC,YAAY,EAAC,QAAM;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAAC,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNvG,OAAA;kBAAK8F,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClD/F,OAAA;oBAAA+F,QAAA,EAAM;kBAAuB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpCvG,OAAA;oBAAM8F,SAAS,EAAC,SAAS;oBAAAC,QAAA,GACtBhE,UAAU,CAACkK,MAAM,EAAC,YAAU,EAAClK,UAAU,CAACmK,SAAS,EAAE,GAAG,EAAC,WAE1D;kBAAA;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvG,OAAA;gBACE8F,SAAS,EAAC,sBAAsB;gBAChCwE,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAAzF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPvG,OAAA;gBAAK8F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC/F,OAAA;kBAAI8F,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAY;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAErC3F,aAAa,CAACqF,GAAG,CAAC,CAAC;kBAAE/B,IAAI;kBAAEC;gBAAO,CAAC,kBAClCnE,OAAA;kBAEE8F,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElE/F,OAAA;oBAAA+F,QAAA,GACG5B,MAAM,EAAC,KAAG,EAACD,IAAI,CAACiI,IAAI,EAAC,IAAE,EAACrI,YAAY,EAAC,SACxC;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPvG,OAAA;oBAAM8F,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACtBtG,KAAK,CAACyK,cAAc,CACnBhG,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YACxB;kBAAC;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAVFrC,IAAI,CAACmD,GAAG;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN,CAAC,eAEFvG,OAAA;kBAAK8F,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzB/F,OAAA;oBACE8F,SAAS,EAAC,gCAAgC;oBAC1CwE,KAAK,EAAE;sBAAE8B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAE3G,sBAAuB;oBAAAK,QAAA,EACjC;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLzF,gBAAgB,CAAC6B,MAAM,GAAG,CAAC,iBAC1B3C,OAAA;gBAAK8F,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC/F,OAAA;kBAAI8F,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAE1CzF,gBAAgB,CAACmF,GAAG,CAAE3B,OAAO,IAAK;kBACjC,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;kBACjD,MAAMC,eAAe,GACnBF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC5B,MAAM;kBACzC,MAAM2J,YAAY,GAAGhI,OAAO,CAACF,KAAK,GAAGI,eAAe;kBAEpD,oBACExE,OAAA;oBAEE8F,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,gBAElE/F,OAAA;sBAAA+F,QAAA,GACGzB,OAAO,CAACG,QAAQ,EAAC,KAAG,EAACH,OAAO,CAAC6H,IAAI,EAAC,IACnC,EAAC5H,aAAa,CAAC5B,MAAM,EAAC,SACxB;oBAAA;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPvG,OAAA;sBAAM8F,SAAS,EAAC,SAAS;sBAAAC,QAAA,EACtBtG,KAAK,CAACyK,cAAc,CAACoC,YAAY;oBAAC;sBAAAlG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA,GATFjC,OAAO,CAAC+C,GAAG;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUb,CAAC;gBAEV,CAAC,CAAC,eAEFvG,OAAA;kBAAK8F,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzB/F,OAAA;oBACE8F,SAAS,EAAC,gCAAgC;oBAC1CwE,KAAK,EAAE;sBAAE8B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAEA,CAAA,KAAM;sBACbnL,QAAQ,CAAC;wBACP2B,IAAI,EAAEjD,aAAa,CAACkD,mBAAmB;wBACvCC,OAAO,EAAE;0BACPnC,aAAa,EAAEA,aAAa;0BAC5BE,gBAAgB,EAAEA,gBAAgB;0BAClCE,WAAW,EAAEA;wBACf;sBACF,CAAC,CAAC;sBACFC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACd,CAAE;oBAAA8E,QAAA,EACH;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDvG,OAAA;gBACE8F,SAAS,EAAC,sBAAsB;gBAChCwE,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAAzF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPvG,OAAA;gBAAK8F,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GAEpCxE,iBAAiB,GAAG,CAAC,gBACpBvB,OAAA,CAACxB,IAAI;kBACHsH,SAAS,EAAC,wBAAwB;kBAClCwE,KAAK,EAAE;oBACLc,eAAe,EAAE,wBAAwB;oBACzCmB,WAAW,EAAE,SAAS;oBACtBC,MAAM,EAAE;kBACV,CAAE;kBAAAzG,QAAA,eAEF/F,OAAA,CAACxB,IAAI,CAACiO,IAAI;oBAAC3G,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACzB/F,OAAA;sBAAK8F,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChE/F,OAAA;wBAAA+F,QAAA,gBACE/F,OAAA;0BAAK8F,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxC/F,OAAA,CAAClB,KAAK;4BAACgH,SAAS,EAAC;0BAAmB;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvCvG,OAAA;4BAAM8F,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAE1E;0BAAa;4BAAA+E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1D,CAAC,eACNvG,OAAA;0BAAO8F,SAAS,EAAC,cAAc;0BAAAC,QAAA,GAAC,OACzB,EAACtG,KAAK,CAACyK,cAAc,CAAC3I,iBAAiB,CAAC;wBAAA;0BAAA6E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNvG,OAAA,CAACtB,MAAM;wBACLgO,OAAO,EAAC,gBAAgB;wBACxBC,IAAI,EAAC,IAAI;wBACTN,OAAO,EAAEA,CAAA,KAAMzF,6BAA6B,CAAC;0BAC3C1B,IAAI,EAAE,EAAE;0BACRI,QAAQ,EAAE,CAAC;0BACXwB,OAAO,EAAE,EAAE;0BACXnF,WAAW,EAAE;wBACf,CAAC,CAAE;wBACHmE,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,gBAErC/F,OAAA,CAACjB,OAAO;0BAAC+G,SAAS,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAE9B;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,gBAEPvG,OAAA;kBAAK8F,SAAS,EAAC,uBAAuB;kBAACwE,KAAK,EAAE;oBAC5CkC,MAAM,EAAE,kCAAkC;oBAC1CnB,YAAY,EAAE,KAAK;oBACnBD,eAAe,EAAE;kBACnB,CAAE;kBAAArF,QAAA,gBACA/F,OAAA,CAAClB,KAAK;oBAACgH,SAAS,EAAC,iBAAiB;oBAAC6G,IAAI,EAAE;kBAAG;oBAAAvG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/CvG,OAAA;oBAAK8F,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CACN,eAGDvG,OAAA,CAACtB,MAAM;kBACLgO,OAAO,EAAC,eAAe;kBACvB5G,SAAS,EAAC,wDAAwD;kBAClEuG,OAAO,EAAEA,CAAA,KAAM1F,qBAAqB,CAAC,IAAI,CAAE;kBAC3C2D,KAAK,EAAE;oBACLsC,WAAW,EAAE,QAAQ;oBACrBC,WAAW,EAAE,KAAK;oBAClBvB,OAAO,EAAE;kBACX,CAAE;kBAAAvF,QAAA,gBAEF/F,OAAA,CAAClB,KAAK;oBAACgH,SAAS,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzBhF,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,kBAAkB;gBAAA;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,EAGR9E,gBAAgB,iBACfzB,OAAA;kBACE8F,SAAS,EAAE,0BACTvE,iBAAiB,GAAG,CAAC,GAAG,cAAc,GAAG,aAAa,EACrD;kBAAAwE,QAAA,EAEFtE;gBAAgB;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENvG,OAAA;gBAAK8F,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B/F,OAAA;kBAAK8F,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChE/F,OAAA;oBAAI8F,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,SACxB,EAACtG,KAAK,CAACyK,cAAc,CAACvF,UAAU,CAAC;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNvG,OAAA;kBAAK8F,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNvG,OAAA,CAACzB,GAAG;YAAC2M,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAApF,QAAA,eAChB/F,OAAA,CAACxB,IAAI;cACHsH,SAAS,EAAC,WAAW;cACrBwE,KAAK,EAAE;gBACLc,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfwB,KAAK,EAAE;cACT,CAAE;cAAA/G,QAAA,gBAEF/F,OAAA;gBAAI8F,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAsB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEhDvG,OAAA,CAACvB,IAAI;gBAAAsH,QAAA,gBACH/F,OAAA,CAACvB,IAAI,CAACsO,KAAK;kBAACjH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1B/F,OAAA,CAACvB,IAAI,CAACuO,KAAK;oBAAAjH,QAAA,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClCvG,OAAA,CAACvB,IAAI,CAACwO,OAAO;oBACXpK,IAAI,EAAC,MAAM;oBACXqK,KAAK,EAAE3M,IAAI,CAAC4L,IAAK;oBACjBrG,SAAS,EAAC,2BAA2B;oBACrCwE,KAAK,EAAE;sBACLkC,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAjF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbvG,OAAA,CAACvB,IAAI,CAACsO,KAAK;kBAACjH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1B/F,OAAA,CAACvB,IAAI,CAACuO,KAAK;oBAAAjH,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BvG,OAAA,CAACvB,IAAI,CAACwO,OAAO;oBACXpK,IAAI,EAAC,OAAO;oBACZqK,KAAK,EAAE3M,IAAI,CAAC4M,KAAM;oBAClBC,WAAW,EAAC,sBAAsB;oBAClCtH,SAAS,EAAC,2BAA2B;oBACrCwE,KAAK,EAAE;sBACLkC,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAjF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbvG,OAAA,CAACvB,IAAI,CAACsO,KAAK;kBAACjH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1B/F,OAAA,CAACvB,IAAI,CAACuO,KAAK;oBAAAjH,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BvG,OAAA,CAACvB,IAAI,CAACwO,OAAO;oBACXpK,IAAI,EAAC,KAAK;oBACVqK,KAAK,EAAE3M,IAAI,CAAC8M,WAAY;oBACxBD,WAAW,EAAC,YAAY;oBACxBtH,SAAS,EAAC,2BAA2B;oBACrCwE,KAAK,EAAE;sBACLkC,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAjF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbvG,OAAA,CAACvB,IAAI,CAACsO,KAAK;kBAACjH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1B/F,OAAA,CAACvB,IAAI,CAACuO,KAAK;oBAAAjH,QAAA,EAAC;kBAAwB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjDvG,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA,CAACvB,IAAI,CAAC6O,KAAK;sBACTzK,IAAI,EAAC,OAAO;sBACZ0K,EAAE,EAAC,WAAW;sBACdC,KAAK,EAAC,oBAAoB;sBAC1BrB,IAAI,EAAC,YAAY;sBACjBsB,OAAO,EAAEtM,UAAU,KAAK,WAAY;sBACpCuM,QAAQ,EAAEA,CAAA,KAAMtM,aAAa,CAAC,WAAW,CAAE;sBAC3C0E,SAAS,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACFvG,OAAA,CAACvB,IAAI,CAAC6O,KAAK;sBACTzK,IAAI,EAAC,OAAO;sBACZ0K,EAAE,EAAC,aAAa;sBAChBC,KAAK,EAAC,8BAA8B;sBACpCrB,IAAI,EAAC,YAAY;sBACjBsB,OAAO,EAAEtM,UAAU,KAAK,aAAc;sBACtCuM,QAAQ,EAAEA,CAAA,KAAMtM,aAAa,CAAC,aAAa;oBAAE;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbvG,OAAA;kBAAK8F,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1B/F,OAAA,CAACtB,MAAM;oBACLoH,SAAS,EAAC,WAAW;oBACrBwE,KAAK,EAAE;sBACLe,YAAY,EAAE,MAAM;sBACpBD,eAAe,EAAE,OAAO;sBACxB0B,KAAK,EAAE,SAAS;sBAChBN,MAAM,EAAE,MAAM;sBACdmB,UAAU,EAAE;oBACd,CAAE;oBACFtB,OAAO,EAAEpC,oBAAqB;oBAAAlE,QAAA,EAC/B;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAETvG,OAAA,CAACX,iBAAiB;oBAChBuO,IAAI,EAAEpH,eAAgB;oBACtBqH,MAAM,EAAEA,CAAA,KAAMpH,kBAAkB,CAAC,KAAK,CAAE;oBACxCqH,SAAS,EAAE9D,YAAa;oBACxB+D,KAAK,EAAC,oBAAoB;oBAC1BjH,OAAO,EAAC,wDAAwD;oBAChEkH,iBAAiB,EAAC,QAAQ;oBAC1BnL,IAAI,EAAC;kBAAQ;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZvG,OAAA;QAAA+F,QAAA,eACE/F,OAAA,CAACL,OAAO;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNvG,OAAA,CAACd,MAAM;MAAAkH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVvG,OAAA,CAACV,cAAc;MACbsO,IAAI,EAAElH,kBAAmB;MACzBmH,MAAM,EAAEA,CAAA,KAAMlH,qBAAqB,CAAC,KAAK,CAAE;MAC3C0B,UAAU,EAAEA,UAAW;MACvB4F,gBAAgB,EAAErH,6BAA8B;MAChDsH,kBAAkB,EAAEvM;IAAY;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEFvG,OAAA,CAACF,gBAAgB;MACf8N,IAAI,EAAEvN,sBAAuB;MAC7B8N,OAAO,EAAEA,CAAA,KAAM;QACb7N,yBAAyB,CAAC,KAAK,CAAC;MAClC;IAAE;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACrG,EAAA,CArxBID,gBAAgB;EAAA,QAGPV,cAAc,EACDA,cAAc,EAGbA,cAAc,EAGPA,cAAc,EAGnBA,cAAc,EAG1BH,WAAW,EACXI,cAAc;AAAA;AAAA4O,EAAA,GAjB3BnO,gBAAgB;AAuxBtB,eAAeA,gBAAgB;AAAC,IAAAmO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}