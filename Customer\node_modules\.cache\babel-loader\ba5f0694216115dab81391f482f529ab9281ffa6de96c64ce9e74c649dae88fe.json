{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\BookingCheckPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { Container, Row, Col, Card, Form, Button, InputGroup } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\nimport Banner from \"../../../images/banner.jpg\";\nimport Header from \"../Header\";\nimport Footer from \"../Footer\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport ConfirmationModal from \"@components/ConfirmationModal\";\nimport PromotionModal from \"./components/PromotionModal\";\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\nimport Utils from \"../../../utils/Utils\";\nimport Factories from \"../../../redux/search/factories\";\nimport { ChatBox } from \"./HomePage\";\nimport SearchActions from \"../../../redux/search/actions\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport HotelClosedModal from \"./components/HotelClosedModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookingCheckPage = () => {\n  _s();\n  var _hotelDetail$hotelNam, _hotelDetail$address;\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const SearchInformation = useAppSelector(state => state.Search.SearchInformation);\n  const selectedRoomsTemps = useAppSelector(state => state.Search.selectedRooms);\n  const selectedServicesFromRedux = useAppSelector(state => state.Search.selectedServices);\n  const hotelDetailFromRedux = useAppSelector(state => state.Search.hotelDetail);\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\n\n  // Promotion code state\n  const [promotionCode, setPromotionCode] = useState(\"\");\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\n  const [promotionId, setPromotionId] = useState(null);\n\n  // Add state for booking data\n  const [bookingData, setBookingData] = useState({\n    selectedRooms: selectedRoomsTemps || [],\n    selectedServices: selectedServicesFromRedux || [],\n    hotelDetail: hotelDetailFromRedux || null,\n    searchInfo: SearchInformation\n  });\n  const [dataRestored, setDataRestored] = useState(false);\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\n\n  // Restore data from sessionStorage stack when component mounts\n  useEffect(() => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      const currentBooking = bookingStack[bookingStack.length - 1];\n      setBookingData(currentBooking);\n\n      // Update Redux store with current data\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: currentBooking.selectedRooms,\n          selectedServices: currentBooking.selectedServices,\n          hotelDetail: currentBooking.hotelDetail\n        }\n      });\n    }\n    setDataRestored(true);\n  }, [dispatch]);\n\n  // Load promotion info from sessionStorage AFTER booking data is restored\n  useEffect(() => {\n    if (dataRestored) {\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\n      if (promo) {\n        setPromotionCode(promo.promotionCode || \"\");\n        setPromotionDiscount(promo.promotionDiscount || 0);\n        setPromotionMessage(promo.promotionMessage || \"\");\n        setPromotionId(promo.promotionId || null);\n      }\n    }\n  }, [dataRestored]);\n\n  // Save promotion info to sessionStorage when any promotion state changes\n  useEffect(() => {\n    if (dataRestored) {\n      // Chỉ save khi đã restore xong data\n      sessionStorage.setItem(\"promotionInfo\", JSON.stringify({\n        promotionCode,\n        promotionDiscount,\n        promotionMessage,\n        promotionId\n      }));\n    }\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored]);\n\n  // Use bookingData instead of Redux state\n  const selectedRooms = bookingData.selectedRooms;\n  const selectedServices = bookingData.selectedServices;\n  const hotelDetail = bookingData.hotelDetail;\n  const searchInfo = bookingData.searchInfo;\n\n  // Calculate number of days between check-in and check-out\n  const calculateNumberOfDays = () => {\n    const checkIn = new Date(searchInfo.checkinDate);\n    const checkOut = new Date(searchInfo.checkoutDate);\n    const diffTime = Math.abs(checkOut - checkIn);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n  const numberOfDays = calculateNumberOfDays();\n\n  // Calculate prices\n  const totalRoomPrice = selectedRooms.reduce((total, {\n    room,\n    amount\n  }) => total + room.price * amount * numberOfDays, 0);\n  const totalServicePrice = selectedServices.reduce((total, service) => {\n    const selectedDates = service.selectedDates || [];\n    const serviceQuantity = service.quantity * selectedDates.length;\n    return total + service.price * serviceQuantity;\n  }, 0);\n  const subtotal = totalRoomPrice + totalServicePrice;\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\n\n  // Validate promotion when data is restored or booking changes\n  useEffect(() => {\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\n\n    // Add a small delay to ensure promotion is fully restored before validation\n    const timeoutId = setTimeout(() => {\n      const validatePromotion = async () => {\n        try {\n          const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\n            code: promotionCode,\n            orderAmount: totalPrice\n          });\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\n            // Promotion is no longer valid or discount changed\n            setPromotionCode(\"\");\n            setPromotionDiscount(0);\n            setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\n            setPromotionId(null);\n            sessionStorage.removeItem(\"promotionInfo\");\n          }\n        } catch (err) {\n          // Promotion validation failed\n          setPromotionCode(\"\");\n          setPromotionDiscount(0);\n          setPromotionMessage(\"Promotion is no longer valid\");\n          setPromotionId(null);\n          sessionStorage.removeItem(\"promotionInfo\");\n        }\n      };\n      validatePromotion();\n    }, 100);\n    return () => clearTimeout(timeoutId);\n  }, [dataRestored, totalPrice, promotionCode, promotionId, promotionDiscount]); // Validate when total price changes or data is restored\n\n  // Handle navigation back to HomeDetailPage\n  const handleBackToHomeDetail = () => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      // Remove the current booking from stack\n      bookingStack.pop();\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n    }\n    navigate(-1);\n  };\n\n  // Star rating component\n  const StarRating = ({\n    rating\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"star-rating\",\n      children: [...Array(5)].map((_, index) => index < rating ? /*#__PURE__*/_jsxDEV(FaStar, {\n        className: \"star filled\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(FaRegStar, {\n        className: \"star\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this);\n  };\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\n\n  // Hàm xử lý áp dụng promotion từ modal\n  const handleApplyPromotionFromModal = promotionData => {\n    setPromotionCode(promotionData.code);\n    setPromotionDiscount(promotionData.discount);\n    setPromotionMessage(promotionData.message);\n    setPromotionId(promotionData.promotionId);\n  };\n  const createBooking = async () => {\n    dispatch({\n      type: HotelActions.FETCH_DETAIL_HOTEL,\n      payload: {\n        hotelId: hotelDetail._id,\n        userId: Auth._id,\n        onSuccess: async hotel => {\n          console.log(\"Hotel detail fetched successfully:\", hotel);\n          if (hotel.ownerStatus === \"ACTIVE\") {\n            const totalRoomPrice = selectedRooms.reduce((total, {\n              room,\n              amount\n            }) => total + room.price * amount * numberOfDays, 0);\n            const totalServicePrice = selectedServices.reduce((total, service) => {\n              const selectedDates = service.selectedDates || [];\n              const serviceQuantity = service.quantity * selectedDates.length;\n              return total + service.price * serviceQuantity;\n            }, 0);\n            const totalPrice = totalRoomPrice + totalServicePrice;\n            const params = {\n              hotelId: hotelDetail._id,\n              checkOutDate: searchInfo.checkoutDate,\n              checkInDate: searchInfo.checkinDate,\n              totalPrice: totalPrice,\n              // giá gốc\n              finalPrice: finalPrice,\n              // giá sau giảm giá\n              roomDetails: selectedRooms.map(({\n                room,\n                amount\n              }) => ({\n                room: {\n                  _id: room._id\n                },\n                amount: amount\n              })),\n              serviceDetails: selectedServices.map(service => {\n                var _service$selectedDate;\n                return {\n                  _id: service._id,\n                  quantity: service.quantity * (((_service$selectedDate = service.selectedDates) === null || _service$selectedDate === void 0 ? void 0 : _service$selectedDate.length) || 0),\n                  selectDate: service.selectedDates || []\n                };\n              }),\n              // Thêm promotionId và promotionDiscount nếu có\n              ...(promotionId && {\n                promotionId\n              }),\n              ...(promotionDiscount > 0 && {\n                promotionDiscount\n              })\n            };\n            console.log(\"params >> \", params);\n\n            // Helper function to save reservationId to bookingStack\n            const saveReservationIdToBookingStack = reservationId => {\n              if (reservationId) {\n                const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n                if (bookingStack.length > 0) {\n                  bookingStack[bookingStack.length - 1].reservationId = reservationId;\n                  sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n                }\n              }\n            };\n            try {\n              let reservationId = null;\n              const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n              if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\n                reservationId = bookingStack[bookingStack.length - 1].reservationId;\n              }\n              const response = await Factories.create_booking({\n                ...params,\n                reservationId\n              });\n              console.log(\"response >> \", response);\n              if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                var _response$data, _response$data$unpaid, _responseCheckout$dat;\n                reservationId = response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : (_response$data$unpaid = _response$data.unpaidReservation) === null || _response$data$unpaid === void 0 ? void 0 : _response$data$unpaid._id;\n                saveReservationIdToBookingStack(reservationId);\n                const unpaidReservationId = reservationId;\n                const responseCheckout = await Factories.checkout_booking(unpaidReservationId);\n                console.log(\"responseCheckout >> \", responseCheckout);\n                const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat = responseCheckout.data) === null || _responseCheckout$dat === void 0 ? void 0 : _responseCheckout$dat.sessionUrl;\n                if (paymentUrl) {\n                  window.location.href = paymentUrl;\n                }\n              } else if ((response === null || response === void 0 ? void 0 : response.status) === 201) {\n                var _response$data2, _response$data2$reser, _responseCheckout$dat2;\n                reservationId = response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$reser = _response$data2.reservation) === null || _response$data2$reser === void 0 ? void 0 : _response$data2$reser._id;\n                saveReservationIdToBookingStack(reservationId);\n                const responseCheckout = await Factories.checkout_booking(reservationId);\n                const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat2 = responseCheckout.data) === null || _responseCheckout$dat2 === void 0 ? void 0 : _responseCheckout$dat2.sessionUrl;\n                if (paymentUrl) {\n                  window.location.href = paymentUrl;\n                }\n              } else {\n                console.log(\"error create booking\");\n              }\n            } catch (error) {\n              console.error(\"Error create payment: \", error);\n              navigate(Routers.ErrorPage);\n            }\n          } else {\n            setShowModalStatusBooking(true);\n          }\n        }\n      }\n    });\n  };\n  const handleAccept = () => {\n    const totalRoomPrice = selectedRooms.reduce((total, {\n      room,\n      amount\n    }) => total + room.price * amount * numberOfDays, 0);\n    if (totalRoomPrice > 0) {\n      createBooking();\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: [],\n          selectedServices: [],\n          hotelDetail: hotelDetail\n        }\n      });\n    }\n  };\n  const handleConfirmBooking = () => {\n    setShowAcceptModal(true);\n  };\n  const formatCurrency = amount => {\n    if (amount === undefined || amount === null) return \"$0\";\n    return new Intl.NumberFormat(\"en-US\", {\n      style: \"currency\",\n      currency: \"USD\",\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(amount);\n  };\n\n  // Add null check for hotelDetail\n  if (!hotelDetail) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: \"100vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\",\n      style: {\n        paddingTop: \"65px\",\n        paddingBottom: \"50px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Container, {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"booking-card text-white\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                marginBottom: \"20px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stars mb-2\",\n                style: {\n                  justifyContent: \"flex-start\",\n                  justifyItems: \"self-start\"\n                },\n                children: /*#__PURE__*/_jsxDEV(StarRating, {\n                  rating: hotelDetail.star\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"hotel-name mb-1\",\n                children: (_hotelDetail$hotelNam = hotelDetail.hotelName) !== null && _hotelDetail$hotelNam !== void 0 ? _hotelDetail$hotelNam : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hotel-address small mb-4\",\n                children: (_hotelDetail$address = hotelDetail.address) !== null && _hotelDetail$address !== void 0 ? _hotelDetail$address : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-4\",\n                children: \"Your booking detail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkin\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkinDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkout\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkout\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkoutDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stay-info mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total length of stay:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [numberOfDays, \" night\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 21\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total number of people:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [searchInfo.adults, \" Adults - \", searchInfo.childrens, \" \", \"Childrens\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-room mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-4\",\n                  children: \"You selected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), selectedRooms.map(({\n                  room,\n                  amount\n                }) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [amount, \" x \", room.name, \" (\", numberOfDays, \" days):\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(room.price * amount * numberOfDays)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 23\n                  }, this)]\n                }, room._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: handleBackToHomeDetail,\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this), selectedServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-services mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-3\",\n                  children: \"Selected Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 21\n                }, this), selectedServices.map(service => {\n                  const selectedDates = service.selectedDates || [];\n                  const serviceQuantity = service.quantity * selectedDates.length;\n                  const serviceTotal = service.price * serviceQuantity;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [service.quantity, \" x \", service.name, \" (\", selectedDates.length, \" days):\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"fw-bold\",\n                      children: Utils.formatCurrency(serviceTotal)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 27\n                    }, this)]\n                  }, service._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: () => {\n                      dispatch({\n                        type: SearchActions.SAVE_SELECTED_ROOMS,\n                        payload: {\n                          selectedRooms: selectedRooms,\n                          selectedServices: selectedServices,\n                          hotelDetail: hotelDetail\n                        }\n                      });\n                      navigate(-1);\n                    },\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-section mb-3\",\n                children: [promotionDiscount > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-applied mb-3\",\n                  style: {\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n                    borderColor: \"#28a745\",\n                    border: \"2px solid #28a745\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"text-success me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 587,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"fw-bold text-success\",\n                            children: promotionCode\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 588,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 586,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-success\",\n                          children: [\"Save \", Utils.formatCurrency(promotionDiscount)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 590,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 585,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleApplyPromotionFromModal({\n                          code: \"\",\n                          discount: 0,\n                          message: \"\",\n                          promotionId: null\n                        }),\n                        className: \"d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 605,\n                          columnNumber: 29\n                        }, this), \"Remove\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 594,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-3 mb-3\",\n                  style: {\n                    border: \"2px dashed rgba(255,255,255,0.3)\",\n                    borderRadius: \"8px\",\n                    backgroundColor: \"rgba(255,255,255,0.05)\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"text-muted mb-2\",\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-muted small\",\n                    children: \"No promotion applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"w-100 d-flex align-items-center justify-content-center\",\n                  onClick: () => setShowPromotionModal(true),\n                  style: {\n                    borderStyle: \"dashed\",\n                    borderWidth: \"2px\",\n                    padding: \"12px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 21\n                  }, this), promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 19\n                }, this), promotionMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `mt-2 small text-center ${promotionDiscount > 0 ? \"text-success\" : \"text-danger\"}`,\n                  children: promotionMessage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-danger mb-0\",\n                    children: [\"Total: \", Utils.formatCurrency(finalPrice)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: \"Includes taxes and fees\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                color: \"white\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-4\",\n                children: \"Check your information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Full name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: Auth.name,\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"email\",\n                    value: Auth.email,\n                    placeholder: \"<EMAIL>\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 689,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"tel\",\n                    value: Auth.phoneNumber,\n                    placeholder: \"0912345678\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Who are you booking for?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"mainGuest\",\n                      label: \"I'm the main guest\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"mainGuest\",\n                      onChange: () => setBookingFor(\"mainGuest\"),\n                      className: \"mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 718,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"someoneElse\",\n                      label: \"I'm booking for someone else\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"someoneElse\",\n                      onChange: () => setBookingFor(\"someoneElse\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    className: \"px-4 py-2\",\n                    style: {\n                      borderRadius: \"10px\",\n                      backgroundColor: \"white\",\n                      color: \"#007bff\",\n                      border: \"none\",\n                      fontWeight: \"bold\"\n                    },\n                    onClick: handleConfirmBooking,\n                    children: \"Booking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 739,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                    show: showAcceptModal,\n                    onHide: () => setShowAcceptModal(false),\n                    onConfirm: handleAccept,\n                    title: \"Confirm Acceptance\",\n                    message: \"Do you want to proceed with this booking confirmation?\",\n                    confirmButtonText: \"Accept\",\n                    type: \"accept\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 753,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(ChatBox, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 768,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 772,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionModal, {\n      show: showPromotionModal,\n      onHide: () => setShowPromotionModal(false),\n      totalPrice: totalPrice,\n      onApplyPromotion: handleApplyPromotionFromModal,\n      currentPromotionId: promotionId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 775,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HotelClosedModal, {\n      show: showModalStatusBooking,\n      onClose: () => {\n        setShowModalStatusBooking(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 783,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 377,\n    columnNumber: 5\n  }, this);\n};\n_s(BookingCheckPage, \"uqI3oA+wJZSJj4G3SA3uz0YW8v4=\", false, function () {\n  return [useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useNavigate, useAppDispatch];\n});\n_c = BookingCheckPage;\nexport default BookingCheckPage;\nvar _c;\n$RefreshReg$(_c, \"BookingCheckPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "InputGroup", "FaStar", "FaRegStar", "FaTag", "FaTimes", "Banner", "Header", "Footer", "Routers", "useNavigate", "ConfirmationModal", "PromotionModal", "useAppSelector", "useAppDispatch", "Utils", "Factories", "ChatBox", "SearchActions", "HotelActions", "HotelClosedModal", "jsxDEV", "_jsxDEV", "BookingCheckPage", "_s", "_hotelDetail$hotelNam", "_hotelDetail$address", "showModalStatusBooking", "setShowModalStatusBooking", "<PERSON><PERSON>", "state", "SearchInformation", "Search", "selectedRoomsTemps", "selectedRooms", "selectedServicesFromRedux", "selectedServices", "hotelDetailFromRedux", "hotelDetail", "navigate", "dispatch", "bookingFor", "setBookingFor", "promotionCode", "setPromotionCode", "promotionDiscount", "setPromotionDiscount", "promotionMessage", "setPromotionMessage", "promotionId", "setPromotionId", "bookingData", "setBookingData", "searchInfo", "dataRestored", "setDataRestored", "isValidatingPromotion", "setIsValidatingPromotion", "isCheckingHotelStatus", "setIsCheckingHotelStatus", "bookingStack", "JSON", "parse", "sessionStorage", "getItem", "length", "currentBooking", "type", "SAVE_SELECTED_ROOMS", "payload", "promo", "setItem", "stringify", "calculateNumberOfDays", "checkIn", "Date", "checkinDate", "checkOut", "checkoutDate", "diffTime", "Math", "abs", "diffDays", "ceil", "numberOfDays", "totalRoomPrice", "reduce", "total", "room", "amount", "price", "totalServicePrice", "service", "selectedDates", "serviceQuantity", "quantity", "subtotal", "finalPrice", "max", "timeoutId", "setTimeout", "validatePromotion", "res", "post", "code", "orderAmount", "totalPrice", "data", "valid", "discount", "removeItem", "err", "clearTimeout", "handleBackToHomeDetail", "pop", "StarRating", "rating", "className", "children", "Array", "map", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showAcceptModal", "setShowAcceptModal", "showPromotionModal", "setShowPromotionModal", "handleApplyPromotionFromModal", "promotionData", "message", "createBooking", "FETCH_DETAIL_HOTEL", "hotelId", "_id", "userId", "onSuccess", "hotel", "console", "log", "ownerStatus", "params", "checkOutDate", "checkInDate", "roomDetails", "serviceDetails", "_service$selectedDate", "selectDate", "saveReservationIdToBookingStack", "reservationId", "response", "create_booking", "status", "_response$data", "_response$data$unpaid", "_responseCheckout$dat", "unpaidReservation", "unpaidReservationId", "responseCheckout", "checkout_booking", "paymentUrl", "sessionUrl", "window", "location", "href", "_response$data2", "_response$data2$reser", "_responseCheckout$dat2", "reservation", "error", "ErrorPage", "handleAccept", "handleConfirmBooking", "formatCurrency", "undefined", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "height", "role", "backgroundImage", "backgroundSize", "backgroundPosition", "paddingTop", "paddingBottom", "md", "lg", "backgroundColor", "borderRadius", "padding", "marginBottom", "justifyContent", "justifyItems", "star", "hotelName", "address", "margin", "xs", "fontSize", "getDate", "adults", "childrens", "name", "cursor", "onClick", "serviceTotal", "borderColor", "border", "Body", "variant", "size", "borderStyle", "borderWidth", "color", "Group", "Label", "Control", "value", "email", "placeholder", "phoneNumber", "Check", "id", "label", "checked", "onChange", "fontWeight", "show", "onHide", "onConfirm", "title", "confirmButtonText", "onApplyPromotion", "currentPromotionId", "onClose", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Customer/src/pages/customer/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Card,\r\n  Form,\r\n  Button,\r\n  InputGroup,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport Header from \"../Header\";\r\nimport Footer from \"../Footer\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport ConfirmationModal from \"@components/ConfirmationModal\";\r\nimport PromotionModal from \"./components/PromotionModal\";\r\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\r\nimport Utils from \"../../../utils/Utils\";\r\nimport Factories from \"../../../redux/search/factories\";\r\nimport { ChatBox } from \"./HomePage\";\r\nimport SearchActions from \"../../../redux/search/actions\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\nimport HotelClosedModal from \"./components/HotelClosedModal\";\r\n\r\nconst BookingCheckPage = () => {\r\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\r\n\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const SearchInformation = useAppSelector(\r\n    (state) => state.Search.SearchInformation\r\n  );\r\n  const selectedRoomsTemps = useAppSelector(\r\n    (state) => state.Search.selectedRooms\r\n  );\r\n  const selectedServicesFromRedux = useAppSelector(\r\n    (state) => state.Search.selectedServices\r\n  );\r\n  const hotelDetailFromRedux = useAppSelector(\r\n    (state) => state.Search.hotelDetail\r\n  );\r\n  const navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\r\n\r\n  // Promotion code state\r\n  const [promotionCode, setPromotionCode] = useState(\"\");\r\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\r\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\r\n  const [promotionId, setPromotionId] = useState(null);\r\n\r\n  // Add state for booking data\r\n  const [bookingData, setBookingData] = useState({\r\n    selectedRooms: selectedRoomsTemps || [],\r\n    selectedServices: selectedServicesFromRedux || [],\r\n    hotelDetail: hotelDetailFromRedux || null,\r\n    searchInfo: SearchInformation,\r\n  });\r\n\r\n  const [dataRestored, setDataRestored] = useState(false);\r\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\r\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\r\n\r\n  // Restore data from sessionStorage stack when component mounts\r\n  useEffect(() => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      const currentBooking = bookingStack[bookingStack.length - 1];\r\n      setBookingData(currentBooking);\r\n\r\n      // Update Redux store with current data\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: currentBooking.selectedRooms,\r\n          selectedServices: currentBooking.selectedServices,\r\n          hotelDetail: currentBooking.hotelDetail,\r\n        },\r\n      });\r\n    }\r\n    setDataRestored(true);\r\n  }, [dispatch]);\r\n\r\n  // Load promotion info from sessionStorage AFTER booking data is restored\r\n  useEffect(() => {\r\n    if (dataRestored) {\r\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\r\n      if (promo) {\r\n        setPromotionCode(promo.promotionCode || \"\");\r\n        setPromotionDiscount(promo.promotionDiscount || 0);\r\n        setPromotionMessage(promo.promotionMessage || \"\");\r\n        setPromotionId(promo.promotionId || null);\r\n      }\r\n    }\r\n  }, [dataRestored]);\r\n\r\n  // Save promotion info to sessionStorage when any promotion state changes\r\n  useEffect(() => {\r\n    if (dataRestored) { // Chỉ save khi đã restore xong data\r\n      sessionStorage.setItem(\r\n        \"promotionInfo\",\r\n        JSON.stringify({\r\n          promotionCode,\r\n          promotionDiscount,\r\n          promotionMessage,\r\n          promotionId,\r\n        })\r\n      );\r\n    }\r\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored]);\r\n\r\n  // Use bookingData instead of Redux state\r\n  const selectedRooms = bookingData.selectedRooms;\r\n  const selectedServices = bookingData.selectedServices;\r\n  const hotelDetail = bookingData.hotelDetail;\r\n  const searchInfo = bookingData.searchInfo;\r\n\r\n  // Calculate number of days between check-in and check-out\r\n  const calculateNumberOfDays = () => {\r\n    const checkIn = new Date(searchInfo.checkinDate);\r\n    const checkOut = new Date(searchInfo.checkoutDate);\r\n    const diffTime = Math.abs(checkOut - checkIn);\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    return diffDays;\r\n  };\r\n\r\n  const numberOfDays = calculateNumberOfDays();\r\n\r\n  // Calculate prices\r\n  const totalRoomPrice = selectedRooms.reduce(\r\n    (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n    0\r\n  );\r\n  const totalServicePrice = selectedServices.reduce((total, service) => {\r\n    const selectedDates = service.selectedDates || [];\r\n    const serviceQuantity = service.quantity * selectedDates.length;\r\n    return total + service.price * serviceQuantity;\r\n  }, 0);\r\n  const subtotal = totalRoomPrice + totalServicePrice;\r\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\r\n\r\n  // Validate promotion when data is restored or booking changes\r\n  useEffect(() => {\r\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\r\n\r\n    // Add a small delay to ensure promotion is fully restored before validation\r\n    const timeoutId = setTimeout(() => {\r\n      const validatePromotion = async () => {\r\n        try {\r\n          const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\r\n            code: promotionCode,\r\n            orderAmount: totalPrice,\r\n          });\r\n          \r\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\r\n            // Promotion is no longer valid or discount changed\r\n            setPromotionCode(\"\");\r\n            setPromotionDiscount(0);\r\n            setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\r\n            setPromotionId(null);\r\n            sessionStorage.removeItem(\"promotionInfo\");\r\n          }\r\n        } catch (err) {\r\n          // Promotion validation failed\r\n          setPromotionCode(\"\");\r\n          setPromotionDiscount(0);\r\n          setPromotionMessage(\"Promotion is no longer valid\");\r\n          setPromotionId(null);\r\n          sessionStorage.removeItem(\"promotionInfo\");\r\n        }\r\n      };\r\n\r\n      validatePromotion();\r\n    }, 100);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [dataRestored, totalPrice, promotionCode, promotionId, promotionDiscount]); // Validate when total price changes or data is restored\r\n\r\n  // Handle navigation back to HomeDetailPage\r\n  const handleBackToHomeDetail = () => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      // Remove the current booking from stack\r\n      bookingStack.pop();\r\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n    }\r\n    navigate(-1);\r\n  };\r\n\r\n  // Star rating component\r\n  const StarRating = ({ rating }) => {\r\n    return (\r\n      <div className=\"star-rating\">\r\n        {[...Array(5)].map((_, index) =>\r\n          index < rating ? (\r\n            <FaStar key={index} className=\"star filled\" />\r\n          ) : (\r\n            <FaRegStar key={index} className=\"star\" />\r\n          )\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\r\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\r\n\r\n  // Hàm xử lý áp dụng promotion từ modal\r\n  const handleApplyPromotionFromModal = (promotionData) => {\r\n    setPromotionCode(promotionData.code);\r\n    setPromotionDiscount(promotionData.discount);\r\n    setPromotionMessage(promotionData.message);\r\n    setPromotionId(promotionData.promotionId);\r\n  };\r\n\r\n  const createBooking = async () => {\r\n    dispatch({\r\n      type: HotelActions.FETCH_DETAIL_HOTEL,\r\n      payload: {\r\n        hotelId: hotelDetail._id,\r\n        userId: Auth._id,\r\n        onSuccess: async (hotel) => {\r\n          console.log(\"Hotel detail fetched successfully:\", hotel);\r\n          if (hotel.ownerStatus === \"ACTIVE\") {\r\n            const totalRoomPrice = selectedRooms.reduce(\r\n              (total, { room, amount }) =>\r\n                total + room.price * amount * numberOfDays,\r\n              0\r\n            );\r\n\r\n            const totalServicePrice = selectedServices.reduce(\r\n              (total, service) => {\r\n                const selectedDates = service.selectedDates || [];\r\n                const serviceQuantity = service.quantity * selectedDates.length;\r\n                return total + service.price * serviceQuantity;\r\n              },\r\n              0\r\n            );\r\n\r\n            const totalPrice = totalRoomPrice + totalServicePrice;\r\n\r\n            const params = {\r\n              hotelId: hotelDetail._id,\r\n              checkOutDate: searchInfo.checkoutDate,\r\n              checkInDate: searchInfo.checkinDate,\r\n              totalPrice: totalPrice, // giá gốc\r\n              finalPrice: finalPrice, // giá sau giảm giá\r\n              roomDetails: selectedRooms.map(({ room, amount }) => ({\r\n                room: {\r\n                  _id: room._id,\r\n                },\r\n                amount: amount,\r\n              })),\r\n              serviceDetails: selectedServices.map((service) => ({\r\n                _id: service._id,\r\n                quantity:\r\n                  service.quantity * (service.selectedDates?.length || 0),\r\n                selectDate: service.selectedDates || [],\r\n              })),\r\n              // Thêm promotionId và promotionDiscount nếu có\r\n              ...(promotionId && { promotionId }),\r\n              ...(promotionDiscount > 0 && { promotionDiscount }),\r\n            };\r\n\r\n            console.log(\"params >> \", params);\r\n\r\n            // Helper function to save reservationId to bookingStack\r\n            const saveReservationIdToBookingStack = (reservationId) => {\r\n              if (reservationId) {\r\n                const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n                if (bookingStack.length > 0) {\r\n                  bookingStack[bookingStack.length - 1].reservationId = reservationId;\r\n                  sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n                }\r\n              }\r\n            };\r\n            try {\r\n              let reservationId = null;\r\n              const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n              if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\r\n                reservationId = bookingStack[bookingStack.length - 1].reservationId;\r\n              }\r\n              const response = await Factories.create_booking({ ...params, reservationId });\r\n              console.log(\"response >> \", response);\r\n              if (response?.status === 200) {\r\n                reservationId = response?.data?.unpaidReservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const unpaidReservationId = reservationId;\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  unpaidReservationId\r\n                );\r\n                console.log(\"responseCheckout >> \", responseCheckout);\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else if (response?.status === 201) {\r\n                reservationId = response?.data?.reservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  reservationId\r\n                );\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else {\r\n                console.log(\"error create booking\");\r\n              }\r\n            } catch (error) {\r\n              console.error(\"Error create payment: \", error);\r\n              navigate(Routers.ErrorPage);\r\n            }\r\n          } else {\r\n            setShowModalStatusBooking(true);\r\n          }\r\n        },\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleAccept = () => {\r\n    const totalRoomPrice = selectedRooms.reduce(\r\n      (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n      0\r\n    );\r\n\r\n    if (totalRoomPrice > 0) {\r\n      createBooking();\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: [],\r\n          selectedServices: [],\r\n          hotelDetail: hotelDetail,\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleConfirmBooking = () => {\r\n    setShowAcceptModal(true);\r\n  };\r\n\r\n  const formatCurrency = (amount) => {\r\n    if (amount === undefined || amount === null) return \"$0\";\r\n    return new Intl.NumberFormat(\"en-US\", {\r\n      style: \"currency\",\r\n      currency: \"USD\",\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 0,\r\n    }).format(amount);\r\n  };\r\n\r\n  // Add null check for hotelDetail\r\n  if (!hotelDetail) {\r\n    return (\r\n      <div\r\n        className=\"d-flex justify-content-center align-items-center\"\r\n        style={{ height: \"100vh\" }}\r\n      >\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"d-flex flex-column min-vh-100\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Header />\r\n      <div\r\n        className=\"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\"\r\n        style={{ paddingTop: \"65px\", paddingBottom: \"50px\" }}\r\n      >\r\n        <Container className=\"mt-4\">\r\n          <Row className=\"justify-content-center\">\r\n            {/* Left Card - Booking Details */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"booking-card text-white\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  marginBottom: \"20px\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"stars mb-2\"\r\n                  style={{\r\n                    justifyContent: \"flex-start\",\r\n                    justifyItems: \"self-start\",\r\n                  }}\r\n                >\r\n                  <StarRating rating={hotelDetail.star} />\r\n                </div>\r\n\r\n                <h4 className=\"hotel-name mb-1\">\r\n                  {hotelDetail.hotelName ?? \"\"}\r\n                </h4>\r\n\r\n                <p className=\"hotel-address small mb-4\">\r\n                  {hotelDetail.address ?? \"\"}\r\n                </p>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <h5 className=\"mb-4\">Your booking detail</h5>\r\n\r\n                <Row className=\"mb-4\">\r\n                  <Col xs={6}>\r\n                    <div className=\"checkin\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkin\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkinDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                  <Col xs={6}>\r\n                    <div className=\"checkout\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkout\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkoutDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n\r\n                <div className=\"stay-info mb-2\">\r\n                  <div className=\"d-flex justify-content-between mb-2\">\r\n                    <span>Total length of stay:</span>\r\n                    <span className=\"fw-bold\">{numberOfDays} night</span>{\" \"}\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between mb-3\">\r\n                    <span>Total number of people:</span>\r\n                    <span className=\"fw-bold\">\r\n                      {searchInfo.adults} Adults - {searchInfo.childrens}{\" \"}\r\n                      Childrens\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"selected-room mb-2\">\r\n                  <h5 className=\"mb-4\">You selected</h5>\r\n\r\n                  {selectedRooms.map(({ room, amount }) => (\r\n                    <div\r\n                      key={room._id}\r\n                      className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                    >\r\n                      <span>\r\n                        {amount} x {room.name} ({numberOfDays} days):\r\n                      </span>\r\n                      <span className=\"fw-bold\">\r\n                        {Utils.formatCurrency(\r\n                          room.price * amount * numberOfDays\r\n                        )}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n\r\n                  <div className=\"small mb-3\">\r\n                    <a\r\n                      className=\"text-blue text-decoration-none\"\r\n                      style={{ cursor: \"pointer\" }}\r\n                      onClick={handleBackToHomeDetail}\r\n                    >\r\n                      Change your selection\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Selected Services Section */}\r\n                {selectedServices.length > 0 && (\r\n                  <div className=\"selected-services mb-2\">\r\n                    <h5 className=\"mb-3\">Selected Services</h5>\r\n\r\n                    {selectedServices.map((service) => {\r\n                      const selectedDates = service.selectedDates || [];\r\n                      const serviceQuantity =\r\n                        service.quantity * selectedDates.length;\r\n                      const serviceTotal = service.price * serviceQuantity;\r\n\r\n                      return (\r\n                        <div\r\n                          key={service._id}\r\n                          className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                        >\r\n                          <span>\r\n                            {service.quantity} x {service.name} (\r\n                            {selectedDates.length} days):\r\n                          </span>\r\n                          <span className=\"fw-bold\">\r\n                            {Utils.formatCurrency(serviceTotal)}\r\n                          </span>\r\n                        </div>\r\n                      );\r\n                    })}\r\n\r\n                    <div className=\"small mb-3\">\r\n                      <a\r\n                        className=\"text-blue text-decoration-none\"\r\n                        style={{ cursor: \"pointer\" }}\r\n                        onClick={() => {\r\n                          dispatch({\r\n                            type: SearchActions.SAVE_SELECTED_ROOMS,\r\n                            payload: {\r\n                              selectedRooms: selectedRooms,\r\n                              selectedServices: selectedServices,\r\n                              hotelDetail: hotelDetail,\r\n                            },\r\n                          });\r\n                          navigate(-1);\r\n                        }}\r\n                      >\r\n                        Change your selection\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"promotion-section mb-3\">\r\n                  {/* Current applied promotion display */}\r\n                  {promotionDiscount > 0 ? (\r\n                    <Card \r\n                      className=\"promotion-applied mb-3\"\r\n                      style={{ \r\n                        backgroundColor: \"rgba(40, 167, 69, 0.2)\", \r\n                        borderColor: \"#28a745\",\r\n                        border: \"2px solid #28a745\"\r\n                      }}\r\n                    >\r\n                      <Card.Body className=\"py-2\">\r\n                        <div className=\"d-flex justify-content-between align-items-center\">\r\n                          <div>\r\n                            <div className=\"d-flex align-items-center\">\r\n                              <FaTag className=\"text-success me-2\" />\r\n                              <span className=\"fw-bold text-success\">{promotionCode}</span>\r\n                            </div>\r\n                            <small className=\"text-success\">\r\n                              Save {Utils.formatCurrency(promotionDiscount)}\r\n                            </small>\r\n                          </div>\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            size=\"sm\"\r\n                            onClick={() => handleApplyPromotionFromModal({\r\n                              code: \"\",\r\n                              discount: 0,\r\n                              message: \"\",\r\n                              promotionId: null\r\n                            })}\r\n                            className=\"d-flex align-items-center\"\r\n                          >\r\n                            <FaTimes className=\"me-1\" />\r\n                            Remove\r\n                          </Button>\r\n                        </div>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  ) : (\r\n                    <div className=\"text-center py-3 mb-3\" style={{ \r\n                      border: \"2px dashed rgba(255,255,255,0.3)\", \r\n                      borderRadius: \"8px\",\r\n                      backgroundColor: \"rgba(255,255,255,0.05)\"\r\n                    }}>\r\n                      <FaTag className=\"text-muted mb-2\" size={24} />\r\n                      <div className=\"text-muted small\">No promotion applied</div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Button to open promotion modal */}\r\n                  <Button\r\n                    variant=\"outline-light\"\r\n                    className=\"w-100 d-flex align-items-center justify-content-center\"\r\n                    onClick={() => setShowPromotionModal(true)}\r\n                    style={{ \r\n                      borderStyle: \"dashed\",\r\n                      borderWidth: \"2px\",\r\n                      padding: \"12px\"\r\n                    }}\r\n                  >\r\n                    <FaTag className=\"me-2\" />\r\n                    {promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"}\r\n                  </Button>\r\n                  \r\n                  {/* Promotion message */}\r\n                  {promotionMessage && (\r\n                    <div\r\n                      className={`mt-2 small text-center ${\r\n                        promotionDiscount > 0 ? \"text-success\" : \"text-danger\"\r\n                      }`}\r\n                    >\r\n                      {promotionMessage}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"total-price\">\r\n                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                    <h5 className=\"text-danger mb-0\">\r\n                      Total: {Utils.formatCurrency(finalPrice)}\r\n                    </h5>\r\n                  </div>\r\n                  <div className=\"small\">Includes taxes and fees</div>\r\n                </div>\r\n              </Card>\r\n            </Col>\r\n\r\n            {/* Right Card - Customer Information */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"info-card\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  color: \"white\",\r\n                }}\r\n              >\r\n                <h4 className=\"mb-4\">Check your information</h4>\r\n\r\n                <Form>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Full name</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={Auth.name}\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Email</Form.Label>\r\n                    <Form.Control\r\n                      type=\"email\"\r\n                      value={Auth.email}\r\n                      placeholder=\"<EMAIL>\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Phone</Form.Label>\r\n                    <Form.Control\r\n                      type=\"tel\"\r\n                      value={Auth.phoneNumber}\r\n                      placeholder=\"0912345678\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Who are you booking for?</Form.Label>\r\n                    <div>\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"mainGuest\"\r\n                        label=\"I'm the main guest\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"mainGuest\"}\r\n                        onChange={() => setBookingFor(\"mainGuest\")}\r\n                        className=\"mb-2\"\r\n                      />\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"someoneElse\"\r\n                        label=\"I'm booking for someone else\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"someoneElse\"}\r\n                        onChange={() => setBookingFor(\"someoneElse\")}\r\n                      />\r\n                    </div>\r\n                  </Form.Group>\r\n\r\n                  <div className=\"text-center\">\r\n                    <Button\r\n                      className=\"px-4 py-2\"\r\n                      style={{\r\n                        borderRadius: \"10px\",\r\n                        backgroundColor: \"white\",\r\n                        color: \"#007bff\",\r\n                        border: \"none\",\r\n                        fontWeight: \"bold\",\r\n                      }}\r\n                      onClick={handleConfirmBooking}\r\n                    >\r\n                      Booking\r\n                    </Button>\r\n                    {/* Accept Confirmation Modal */}\r\n                    <ConfirmationModal\r\n                      show={showAcceptModal}\r\n                      onHide={() => setShowAcceptModal(false)}\r\n                      onConfirm={handleAccept}\r\n                      title=\"Confirm Acceptance\"\r\n                      message=\"Do you want to proceed with this booking confirmation?\"\r\n                      confirmButtonText=\"Accept\"\r\n                      type=\"accept\"\r\n                    />\r\n                  </div>\r\n                </Form>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n        <div>\r\n          <ChatBox />\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n      \r\n      {/* Promotion Modal */}\r\n      <PromotionModal\r\n        show={showPromotionModal}\r\n        onHide={() => setShowPromotionModal(false)}\r\n        totalPrice={totalPrice}\r\n        onApplyPromotion={handleApplyPromotionFromModal}\r\n        currentPromotionId={promotionId}\r\n      />\r\n      \r\n      <HotelClosedModal\r\n        show={showModalStatusBooking}\r\n        onClose={() => {\r\n          setShowModalStatusBooking(false);\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BookingCheckPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,UAAU,QACL,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SAASC,cAAc,EAAEC,cAAc,QAAQ,sBAAsB;AACrE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,SAAS,MAAM,iCAAiC;AACvD,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC7B,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAE3E,MAAMqC,IAAI,GAAGhB,cAAc,CAAEiB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;EACvD,MAAME,iBAAiB,GAAGlB,cAAc,CACrCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACD,iBAC1B,CAAC;EACD,MAAME,kBAAkB,GAAGpB,cAAc,CACtCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACE,aAC1B,CAAC;EACD,MAAMC,yBAAyB,GAAGtB,cAAc,CAC7CiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACI,gBAC1B,CAAC;EACD,MAAMC,oBAAoB,GAAGxB,cAAc,CACxCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACM,WAC1B,CAAC;EACD,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,QAAQ,GAAG1B,cAAc,CAAC,CAAC;EACjC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,WAAW,CAAC;;EAEzD;EACA,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC;IAC7C0C,aAAa,EAAED,kBAAkB,IAAI,EAAE;IACvCG,gBAAgB,EAAED,yBAAyB,IAAI,EAAE;IACjDG,WAAW,EAAED,oBAAoB,IAAI,IAAI;IACzCgB,UAAU,EAAEtB;EACd,CAAC,CAAC;EAEF,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmE,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,cAAc,GAAGN,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC;MAC5Db,cAAc,CAACc,cAAc,CAAC;;MAE9B;MACA1B,QAAQ,CAAC;QACP2B,IAAI,EAAEjD,aAAa,CAACkD,mBAAmB;QACvCC,OAAO,EAAE;UACPnC,aAAa,EAAEgC,cAAc,CAAChC,aAAa;UAC3CE,gBAAgB,EAAE8B,cAAc,CAAC9B,gBAAgB;UACjDE,WAAW,EAAE4B,cAAc,CAAC5B;QAC9B;MACF,CAAC,CAAC;IACJ;IACAiB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;;EAEd;EACA/C,SAAS,CAAC,MAAM;IACd,IAAI6D,YAAY,EAAE;MAChB,MAAMgB,KAAK,GAAGT,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,MAAM,CAAC;MAC3E,IAAIM,KAAK,EAAE;QACT1B,gBAAgB,CAAC0B,KAAK,CAAC3B,aAAa,IAAI,EAAE,CAAC;QAC3CG,oBAAoB,CAACwB,KAAK,CAACzB,iBAAiB,IAAI,CAAC,CAAC;QAClDG,mBAAmB,CAACsB,KAAK,CAACvB,gBAAgB,IAAI,EAAE,CAAC;QACjDG,cAAc,CAACoB,KAAK,CAACrB,WAAW,IAAI,IAAI,CAAC;MAC3C;IACF;EACF,CAAC,EAAE,CAACK,YAAY,CAAC,CAAC;;EAElB;EACA7D,SAAS,CAAC,MAAM;IACd,IAAI6D,YAAY,EAAE;MAAE;MAClBS,cAAc,CAACQ,OAAO,CACpB,eAAe,EACfV,IAAI,CAACW,SAAS,CAAC;QACb7B,aAAa;QACbE,iBAAiB;QACjBE,gBAAgB;QAChBE;MACF,CAAC,CACH,CAAC;IACH;EACF,CAAC,EAAE,CAACN,aAAa,EAAEE,iBAAiB,EAAEE,gBAAgB,EAAEE,WAAW,EAAEK,YAAY,CAAC,CAAC;;EAEnF;EACA,MAAMpB,aAAa,GAAGiB,WAAW,CAACjB,aAAa;EAC/C,MAAME,gBAAgB,GAAGe,WAAW,CAACf,gBAAgB;EACrD,MAAME,WAAW,GAAGa,WAAW,CAACb,WAAW;EAC3C,MAAMe,UAAU,GAAGF,WAAW,CAACE,UAAU;;EAEzC;EACA,MAAMoB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACtB,UAAU,CAACuB,WAAW,CAAC;IAChD,MAAMC,QAAQ,GAAG,IAAIF,IAAI,CAACtB,UAAU,CAACyB,YAAY,CAAC;IAClD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAGH,OAAO,CAAC;IAC7C,MAAMQ,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB,CAAC;EAED,MAAME,YAAY,GAAGX,qBAAqB,CAAC,CAAC;;EAE5C;EACA,MAAMY,cAAc,GAAGnD,aAAa,CAACoD,MAAM,CACzC,CAACC,KAAK,EAAE;IAAEC,IAAI;IAAEC;EAAO,CAAC,KAAKF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EACvE,CACF,CAAC;EACD,MAAMO,iBAAiB,GAAGvD,gBAAgB,CAACkD,MAAM,CAAC,CAACC,KAAK,EAAEK,OAAO,KAAK;IACpE,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;IACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC5B,MAAM;IAC/D,OAAOsB,KAAK,GAAGK,OAAO,CAACF,KAAK,GAAGI,eAAe;EAChD,CAAC,EAAE,CAAC,CAAC;EACL,MAAME,QAAQ,GAAGX,cAAc,GAAGM,iBAAiB;EACnD,MAAMM,UAAU,GAAGjB,IAAI,CAACkB,GAAG,CAACF,QAAQ,GAAGnD,iBAAiB,EAAE,CAAC,CAAC;;EAE5D;EACApD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6D,YAAY,IAAI,CAACX,aAAa,IAAI,CAACM,WAAW,IAAIJ,iBAAiB,KAAK,CAAC,EAAE;;IAEhF;IACA,MAAMsD,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpC,IAAI;UACF,MAAMC,GAAG,GAAG,MAAM5G,KAAK,CAAC6G,IAAI,CAAC,4CAA4C,EAAE;YACzEC,IAAI,EAAE7D,aAAa;YACnB8D,WAAW,EAAEC;UACf,CAAC,CAAC;UAEF,IAAI,CAACJ,GAAG,CAACK,IAAI,CAACC,KAAK,IAAIN,GAAG,CAACK,IAAI,CAACE,QAAQ,KAAKhE,iBAAiB,EAAE;YAC9D;YACAD,gBAAgB,CAAC,EAAE,CAAC;YACpBE,oBAAoB,CAAC,CAAC,CAAC;YACvBE,mBAAmB,CAAC,qDAAqD,CAAC;YAC1EE,cAAc,CAAC,IAAI,CAAC;YACpBa,cAAc,CAAC+C,UAAU,CAAC,eAAe,CAAC;UAC5C;QACF,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZ;UACAnE,gBAAgB,CAAC,EAAE,CAAC;UACpBE,oBAAoB,CAAC,CAAC,CAAC;UACvBE,mBAAmB,CAAC,8BAA8B,CAAC;UACnDE,cAAc,CAAC,IAAI,CAAC;UACpBa,cAAc,CAAC+C,UAAU,CAAC,eAAe,CAAC;QAC5C;MACF,CAAC;MAEDT,iBAAiB,CAAC,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMW,YAAY,CAACb,SAAS,CAAC;EACtC,CAAC,EAAE,CAAC7C,YAAY,EAAEoD,UAAU,EAAE/D,aAAa,EAAEM,WAAW,EAAEJ,iBAAiB,CAAC,CAAC,CAAC,CAAC;;EAE/E;EACA,MAAMoE,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMrD,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B;MACAL,YAAY,CAACsD,GAAG,CAAC,CAAC;MAClBnD,cAAc,CAACQ,OAAO,CAAC,cAAc,EAAEV,IAAI,CAACW,SAAS,CAACZ,YAAY,CAAC,CAAC;IACtE;IACArB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAM4E,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,KAAK;IACjC,oBACE9F,OAAA;MAAK+F,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC1BA,KAAK,GAAGN,MAAM,gBACZ9F,OAAA,CAACpB,MAAM;QAAamH,SAAS,EAAC;MAAa,GAA9BK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2B,CAAC,gBAE9CxG,OAAA,CAACnB,SAAS;QAAakH,SAAS,EAAC;MAAM,GAAvBK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAE7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxI,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1I,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAM2I,6BAA6B,GAAIC,aAAa,IAAK;IACvDxF,gBAAgB,CAACwF,aAAa,CAAC5B,IAAI,CAAC;IACpC1D,oBAAoB,CAACsF,aAAa,CAACvB,QAAQ,CAAC;IAC5C7D,mBAAmB,CAACoF,aAAa,CAACC,OAAO,CAAC;IAC1CnF,cAAc,CAACkF,aAAa,CAACnF,WAAW,CAAC;EAC3C,CAAC;EAED,MAAMqF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC9F,QAAQ,CAAC;MACP2B,IAAI,EAAEhD,YAAY,CAACoH,kBAAkB;MACrClE,OAAO,EAAE;QACPmE,OAAO,EAAElG,WAAW,CAACmG,GAAG;QACxBC,MAAM,EAAE7G,IAAI,CAAC4G,GAAG;QAChBE,SAAS,EAAE,MAAOC,KAAK,IAAK;UAC1BC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEF,KAAK,CAAC;UACxD,IAAIA,KAAK,CAACG,WAAW,KAAK,QAAQ,EAAE;YAClC,MAAM1D,cAAc,GAAGnD,aAAa,CAACoD,MAAM,CACzC,CAACC,KAAK,EAAE;cAAEC,IAAI;cAAEC;YAAO,CAAC,KACtBF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EAC5C,CACF,CAAC;YAED,MAAMO,iBAAiB,GAAGvD,gBAAgB,CAACkD,MAAM,CAC/C,CAACC,KAAK,EAAEK,OAAO,KAAK;cAClB,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;cACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC5B,MAAM;cAC/D,OAAOsB,KAAK,GAAGK,OAAO,CAACF,KAAK,GAAGI,eAAe;YAChD,CAAC,EACD,CACF,CAAC;YAED,MAAMY,UAAU,GAAGrB,cAAc,GAAGM,iBAAiB;YAErD,MAAMqD,MAAM,GAAG;cACbR,OAAO,EAAElG,WAAW,CAACmG,GAAG;cACxBQ,YAAY,EAAE5F,UAAU,CAACyB,YAAY;cACrCoE,WAAW,EAAE7F,UAAU,CAACuB,WAAW;cACnC8B,UAAU,EAAEA,UAAU;cAAE;cACxBT,UAAU,EAAEA,UAAU;cAAE;cACxBkD,WAAW,EAAEjH,aAAa,CAACsF,GAAG,CAAC,CAAC;gBAAEhC,IAAI;gBAAEC;cAAO,CAAC,MAAM;gBACpDD,IAAI,EAAE;kBACJiD,GAAG,EAAEjD,IAAI,CAACiD;gBACZ,CAAC;gBACDhD,MAAM,EAAEA;cACV,CAAC,CAAC,CAAC;cACH2D,cAAc,EAAEhH,gBAAgB,CAACoF,GAAG,CAAE5B,OAAO;gBAAA,IAAAyD,qBAAA;gBAAA,OAAM;kBACjDZ,GAAG,EAAE7C,OAAO,CAAC6C,GAAG;kBAChB1C,QAAQ,EACNH,OAAO,CAACG,QAAQ,IAAI,EAAAsD,qBAAA,GAAAzD,OAAO,CAACC,aAAa,cAAAwD,qBAAA,uBAArBA,qBAAA,CAAuBpF,MAAM,KAAI,CAAC,CAAC;kBACzDqF,UAAU,EAAE1D,OAAO,CAACC,aAAa,IAAI;gBACvC,CAAC;cAAA,CAAC,CAAC;cACH;cACA,IAAI5C,WAAW,IAAI;gBAAEA;cAAY,CAAC,CAAC;cACnC,IAAIJ,iBAAiB,GAAG,CAAC,IAAI;gBAAEA;cAAkB,CAAC;YACpD,CAAC;YAEDgG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEE,MAAM,CAAC;;YAEjC;YACA,MAAMO,+BAA+B,GAAIC,aAAa,IAAK;cACzD,IAAIA,aAAa,EAAE;gBACjB,MAAM5F,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;gBAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;kBAC3BL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACuF,aAAa,GAAGA,aAAa;kBACnEzF,cAAc,CAACQ,OAAO,CAAC,cAAc,EAAEV,IAAI,CAACW,SAAS,CAACZ,YAAY,CAAC,CAAC;gBACtE;cACF;YACF,CAAC;YACD,IAAI;cACF,IAAI4F,aAAa,GAAG,IAAI;cACxB,MAAM5F,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;cAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,IAAIL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACuF,aAAa,EAAE;gBAClFA,aAAa,GAAG5F,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACuF,aAAa;cACrE;cACA,MAAMC,QAAQ,GAAG,MAAMzI,SAAS,CAAC0I,cAAc,CAAC;gBAAE,GAAGV,MAAM;gBAAEQ;cAAc,CAAC,CAAC;cAC7EX,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEW,QAAQ,CAAC;cACrC,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;gBAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,qBAAA;gBAC5BN,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAG,cAAA,GAARH,QAAQ,CAAE9C,IAAI,cAAAiD,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBG,iBAAiB,cAAAF,qBAAA,uBAAjCA,qBAAA,CAAmCpB,GAAG;gBACtDc,+BAA+B,CAACC,aAAa,CAAC;gBAC9C,MAAMQ,mBAAmB,GAAGR,aAAa;gBACzC,MAAMS,gBAAgB,GAAG,MAAMjJ,SAAS,CAACkJ,gBAAgB,CACvDF,mBACF,CAAC;gBACDnB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmB,gBAAgB,CAAC;gBACrD,MAAME,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAH,qBAAA,GAAhBG,gBAAgB,CAAEtD,IAAI,cAAAmD,qBAAA,uBAAtBA,qBAAA,CAAwBM,UAAU;gBACrD,IAAID,UAAU,EAAE;kBACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;gBACnC;cACF,CAAC,MAAM,IAAI,CAAAV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;gBAAA,IAAAa,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;gBACnClB,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAe,eAAA,GAARf,QAAQ,CAAE9C,IAAI,cAAA6D,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBG,WAAW,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6BhC,GAAG;gBAChDc,+BAA+B,CAACC,aAAa,CAAC;gBAC9C,MAAMS,gBAAgB,GAAG,MAAMjJ,SAAS,CAACkJ,gBAAgB,CACvDV,aACF,CAAC;gBACD,MAAMW,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAS,sBAAA,GAAhBT,gBAAgB,CAAEtD,IAAI,cAAA+D,sBAAA,uBAAtBA,sBAAA,CAAwBN,UAAU;gBACrD,IAAID,UAAU,EAAE;kBACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;gBACnC;cACF,CAAC,MAAM;gBACLtB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;cACrC;YACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;cACd/B,OAAO,CAAC+B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;cAC9CrI,QAAQ,CAAC9B,OAAO,CAACoK,SAAS,CAAC;YAC7B;UACF,CAAC,MAAM;YACLjJ,yBAAyB,CAAC,IAAI,CAAC;UACjC;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMkJ,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMzF,cAAc,GAAGnD,aAAa,CAACoD,MAAM,CACzC,CAACC,KAAK,EAAE;MAAEC,IAAI;MAAEC;IAAO,CAAC,KAAKF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EACvE,CACF,CAAC;IAED,IAAIC,cAAc,GAAG,CAAC,EAAE;MACtBiD,aAAa,CAAC,CAAC;MACf9F,QAAQ,CAAC;QACP2B,IAAI,EAAEjD,aAAa,CAACkD,mBAAmB;QACvCC,OAAO,EAAE;UACPnC,aAAa,EAAE,EAAE;UACjBE,gBAAgB,EAAE,EAAE;UACpBE,WAAW,EAAEA;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMyI,oBAAoB,GAAGA,CAAA,KAAM;IACjC/C,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMgD,cAAc,GAAIvF,MAAM,IAAK;IACjC,IAAIA,MAAM,KAAKwF,SAAS,IAAIxF,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;IACxD,OAAO,IAAIyF,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAAC/F,MAAM,CAAC;EACnB,CAAC;;EAED;EACA,IAAI,CAACnD,WAAW,EAAE;IAChB,oBACEhB,OAAA;MACE+F,SAAS,EAAC,kDAAkD;MAC5D+D,KAAK,EAAE;QAAEK,MAAM,EAAE;MAAQ,CAAE;MAAAnE,QAAA,eAE3BhG,OAAA;QAAK+F,SAAS,EAAC,6BAA6B;QAACqE,IAAI,EAAC,QAAQ;QAAApE,QAAA,eACxDhG,OAAA;UAAM+F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExG,OAAA;IACE+F,SAAS,EAAC,+BAA+B;IACzC+D,KAAK,EAAE;MACLO,eAAe,EAAE,OAAOrL,MAAM,GAAG;MACjCsL,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAAvE,QAAA,gBAEFhG,OAAA,CAACf,MAAM;MAAAoH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVxG,OAAA;MACE+F,SAAS,EAAC,8EAA8E;MACxF+D,KAAK,EAAE;QAAEU,UAAU,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAO,CAAE;MAAAzE,QAAA,gBAErDhG,OAAA,CAAC3B,SAAS;QAAC0H,SAAS,EAAC,MAAM;QAAAC,QAAA,eACzBhG,OAAA,CAAC1B,GAAG;UAACyH,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAErChG,OAAA,CAACzB,GAAG;YAACmM,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA3E,QAAA,eAChBhG,OAAA,CAACxB,IAAI;cACHuH,SAAS,EAAC,yBAAyB;cACnC+D,KAAK,EAAE;gBACLc,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE;cAChB,CAAE;cAAA/E,QAAA,gBAEFhG,OAAA;gBACE+F,SAAS,EAAC,YAAY;gBACtB+D,KAAK,EAAE;kBACLkB,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE;gBAChB,CAAE;gBAAAjF,QAAA,eAEFhG,OAAA,CAAC6F,UAAU;kBAACC,MAAM,EAAE9E,WAAW,CAACkK;gBAAK;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAENxG,OAAA;gBAAI+F,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAA7F,qBAAA,GAC5Ba,WAAW,CAACmK,SAAS,cAAAhL,qBAAA,cAAAA,qBAAA,GAAI;cAAE;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAELxG,OAAA;gBAAG+F,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GAAA5F,oBAAA,GACpCY,WAAW,CAACoK,OAAO,cAAAhL,oBAAA,cAAAA,oBAAA,GAAI;cAAE;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEJxG,OAAA;gBACE+F,SAAS,EAAC,sBAAsB;gBAChC+D,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPxG,OAAA;gBAAI+F,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE7CxG,OAAA,CAAC1B,GAAG;gBAACyH,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBhG,OAAA,CAACzB,GAAG;kBAAC+M,EAAE,EAAE,CAAE;kBAAAtF,QAAA,eACThG,OAAA;oBAAK+F,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtBhG,OAAA;sBACE+F,SAAS,EAAC,oBAAoB;sBAC9B+D,KAAK,EAAE;wBAAEyB,QAAQ,EAAE;sBAAG,CAAE;sBAAAvF,QAAA,EACzB;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNxG,OAAA;sBAAK+F,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBvG,KAAK,CAAC+L,OAAO,CAACzJ,UAAU,CAACuB,WAAW,EAAE,CAAC;oBAAC;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxG,OAAA,CAACzB,GAAG;kBAAC+M,EAAE,EAAE,CAAE;kBAAAtF,QAAA,eACThG,OAAA;oBAAK+F,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvBhG,OAAA;sBACE+F,SAAS,EAAC,oBAAoB;sBAC9B+D,KAAK,EAAE;wBAAEyB,QAAQ,EAAE;sBAAG,CAAE;sBAAAvF,QAAA,EACzB;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNxG,OAAA;sBAAK+F,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBvG,KAAK,CAAC+L,OAAO,CAACzJ,UAAU,CAACyB,YAAY,EAAE,CAAC;oBAAC;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxG,OAAA;gBAAK+F,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BhG,OAAA;kBAAK+F,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDhG,OAAA;oBAAAgG,QAAA,EAAM;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClCxG,OAAA;oBAAM+F,SAAS,EAAC,SAAS;oBAAAC,QAAA,GAAElC,YAAY,EAAC,QAAM;kBAAA;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAAC,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNxG,OAAA;kBAAK+F,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDhG,OAAA;oBAAAgG,QAAA,EAAM;kBAAuB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpCxG,OAAA;oBAAM+F,SAAS,EAAC,SAAS;oBAAAC,QAAA,GACtBjE,UAAU,CAAC0J,MAAM,EAAC,YAAU,EAAC1J,UAAU,CAAC2J,SAAS,EAAE,GAAG,EAAC,WAE1D;kBAAA;oBAAArF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxG,OAAA;gBACE+F,SAAS,EAAC,sBAAsB;gBAChC+D,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPxG,OAAA;gBAAK+F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjChG,OAAA;kBAAI+F,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAY;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAErC5F,aAAa,CAACsF,GAAG,CAAC,CAAC;kBAAEhC,IAAI;kBAAEC;gBAAO,CAAC,kBAClCnE,OAAA;kBAEE+F,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElEhG,OAAA;oBAAAgG,QAAA,GACG7B,MAAM,EAAC,KAAG,EAACD,IAAI,CAACyH,IAAI,EAAC,IAAE,EAAC7H,YAAY,EAAC,SACxC;kBAAA;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPxG,OAAA;oBAAM+F,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACtBvG,KAAK,CAACiK,cAAc,CACnBxF,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YACxB;kBAAC;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAVFtC,IAAI,CAACiD,GAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN,CAAC,eAEFxG,OAAA;kBAAK+F,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBhG,OAAA;oBACE+F,SAAS,EAAC,gCAAgC;oBAC1C+D,KAAK,EAAE;sBAAE8B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAElG,sBAAuB;oBAAAK,QAAA,EACjC;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL1F,gBAAgB,CAAC6B,MAAM,GAAG,CAAC,iBAC1B3C,OAAA;gBAAK+F,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrChG,OAAA;kBAAI+F,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAE1C1F,gBAAgB,CAACoF,GAAG,CAAE5B,OAAO,IAAK;kBACjC,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;kBACjD,MAAMC,eAAe,GACnBF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAC5B,MAAM;kBACzC,MAAMmJ,YAAY,GAAGxH,OAAO,CAACF,KAAK,GAAGI,eAAe;kBAEpD,oBACExE,OAAA;oBAEE+F,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,gBAElEhG,OAAA;sBAAAgG,QAAA,GACG1B,OAAO,CAACG,QAAQ,EAAC,KAAG,EAACH,OAAO,CAACqH,IAAI,EAAC,IACnC,EAACpH,aAAa,CAAC5B,MAAM,EAAC,SACxB;oBAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPxG,OAAA;sBAAM+F,SAAS,EAAC,SAAS;sBAAAC,QAAA,EACtBvG,KAAK,CAACiK,cAAc,CAACoC,YAAY;oBAAC;sBAAAzF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA,GATFlC,OAAO,CAAC6C,GAAG;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUb,CAAC;gBAEV,CAAC,CAAC,eAEFxG,OAAA;kBAAK+F,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBhG,OAAA;oBACE+F,SAAS,EAAC,gCAAgC;oBAC1C+D,KAAK,EAAE;sBAAE8B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAEA,CAAA,KAAM;sBACb3K,QAAQ,CAAC;wBACP2B,IAAI,EAAEjD,aAAa,CAACkD,mBAAmB;wBACvCC,OAAO,EAAE;0BACPnC,aAAa,EAAEA,aAAa;0BAC5BE,gBAAgB,EAAEA,gBAAgB;0BAClCE,WAAW,EAAEA;wBACf;sBACF,CAAC,CAAC;sBACFC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACd,CAAE;oBAAA+E,QAAA,EACH;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDxG,OAAA;gBACE+F,SAAS,EAAC,sBAAsB;gBAChC+D,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPxG,OAAA;gBAAK+F,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GAEpCzE,iBAAiB,GAAG,CAAC,gBACpBvB,OAAA,CAACxB,IAAI;kBACHuH,SAAS,EAAC,wBAAwB;kBAClC+D,KAAK,EAAE;oBACLc,eAAe,EAAE,wBAAwB;oBACzCmB,WAAW,EAAE,SAAS;oBACtBC,MAAM,EAAE;kBACV,CAAE;kBAAAhG,QAAA,eAEFhG,OAAA,CAACxB,IAAI,CAACyN,IAAI;oBAAClG,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACzBhG,OAAA;sBAAK+F,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChEhG,OAAA;wBAAAgG,QAAA,gBACEhG,OAAA;0BAAK+F,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxChG,OAAA,CAAClB,KAAK;4BAACiH,SAAS,EAAC;0BAAmB;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvCxG,OAAA;4BAAM+F,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAE3E;0BAAa;4BAAAgF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1D,CAAC,eACNxG,OAAA;0BAAO+F,SAAS,EAAC,cAAc;0BAAAC,QAAA,GAAC,OACzB,EAACvG,KAAK,CAACiK,cAAc,CAACnI,iBAAiB,CAAC;wBAAA;0BAAA8E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNxG,OAAA,CAACtB,MAAM;wBACLwN,OAAO,EAAC,gBAAgB;wBACxBC,IAAI,EAAC,IAAI;wBACTN,OAAO,EAAEA,CAAA,KAAMhF,6BAA6B,CAAC;0BAC3C3B,IAAI,EAAE,EAAE;0BACRK,QAAQ,EAAE,CAAC;0BACXwB,OAAO,EAAE,EAAE;0BACXpF,WAAW,EAAE;wBACf,CAAC,CAAE;wBACHoE,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,gBAErChG,OAAA,CAACjB,OAAO;0BAACgH,SAAS,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAE9B;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,gBAEPxG,OAAA;kBAAK+F,SAAS,EAAC,uBAAuB;kBAAC+D,KAAK,EAAE;oBAC5CkC,MAAM,EAAE,kCAAkC;oBAC1CnB,YAAY,EAAE,KAAK;oBACnBD,eAAe,EAAE;kBACnB,CAAE;kBAAA5E,QAAA,gBACAhG,OAAA,CAAClB,KAAK;oBAACiH,SAAS,EAAC,iBAAiB;oBAACoG,IAAI,EAAE;kBAAG;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/CxG,OAAA;oBAAK+F,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CACN,eAGDxG,OAAA,CAACtB,MAAM;kBACLwN,OAAO,EAAC,eAAe;kBACvBnG,SAAS,EAAC,wDAAwD;kBAClE8F,OAAO,EAAEA,CAAA,KAAMjF,qBAAqB,CAAC,IAAI,CAAE;kBAC3CkD,KAAK,EAAE;oBACLsC,WAAW,EAAE,QAAQ;oBACrBC,WAAW,EAAE,KAAK;oBAClBvB,OAAO,EAAE;kBACX,CAAE;kBAAA9E,QAAA,gBAEFhG,OAAA,CAAClB,KAAK;oBAACiH,SAAS,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzBjF,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,kBAAkB;gBAAA;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,EAGR/E,gBAAgB,iBACfzB,OAAA;kBACE+F,SAAS,EAAE,0BACTxE,iBAAiB,GAAG,CAAC,GAAG,cAAc,GAAG,aAAa,EACrD;kBAAAyE,QAAA,EAEFvE;gBAAgB;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENxG,OAAA;gBAAK+F,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhG,OAAA;kBAAK+F,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChEhG,OAAA;oBAAI+F,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,SACxB,EAACvG,KAAK,CAACiK,cAAc,CAAC/E,UAAU,CAAC;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNxG,OAAA;kBAAK+F,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNxG,OAAA,CAACzB,GAAG;YAACmM,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA3E,QAAA,eAChBhG,OAAA,CAACxB,IAAI;cACHuH,SAAS,EAAC,WAAW;cACrB+D,KAAK,EAAE;gBACLc,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfwB,KAAK,EAAE;cACT,CAAE;cAAAtG,QAAA,gBAEFhG,OAAA;gBAAI+F,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAsB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEhDxG,OAAA,CAACvB,IAAI;gBAAAuH,QAAA,gBACHhG,OAAA,CAACvB,IAAI,CAAC8N,KAAK;kBAACxG,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BhG,OAAA,CAACvB,IAAI,CAAC+N,KAAK;oBAAAxG,QAAA,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClCxG,OAAA,CAACvB,IAAI,CAACgO,OAAO;oBACX5J,IAAI,EAAC,MAAM;oBACX6J,KAAK,EAAEnM,IAAI,CAACoL,IAAK;oBACjB5F,SAAS,EAAC,2BAA2B;oBACrC+D,KAAK,EAAE;sBACLkC,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbxG,OAAA,CAACvB,IAAI,CAAC8N,KAAK;kBAACxG,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BhG,OAAA,CAACvB,IAAI,CAAC+N,KAAK;oBAAAxG,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BxG,OAAA,CAACvB,IAAI,CAACgO,OAAO;oBACX5J,IAAI,EAAC,OAAO;oBACZ6J,KAAK,EAAEnM,IAAI,CAACoM,KAAM;oBAClBC,WAAW,EAAC,sBAAsB;oBAClC7G,SAAS,EAAC,2BAA2B;oBACrC+D,KAAK,EAAE;sBACLkC,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbxG,OAAA,CAACvB,IAAI,CAAC8N,KAAK;kBAACxG,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BhG,OAAA,CAACvB,IAAI,CAAC+N,KAAK;oBAAAxG,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BxG,OAAA,CAACvB,IAAI,CAACgO,OAAO;oBACX5J,IAAI,EAAC,KAAK;oBACV6J,KAAK,EAAEnM,IAAI,CAACsM,WAAY;oBACxBD,WAAW,EAAC,YAAY;oBACxB7G,SAAS,EAAC,2BAA2B;oBACrC+D,KAAK,EAAE;sBACLkC,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbxG,OAAA,CAACvB,IAAI,CAAC8N,KAAK;kBAACxG,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BhG,OAAA,CAACvB,IAAI,CAAC+N,KAAK;oBAAAxG,QAAA,EAAC;kBAAwB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjDxG,OAAA;oBAAAgG,QAAA,gBACEhG,OAAA,CAACvB,IAAI,CAACqO,KAAK;sBACTjK,IAAI,EAAC,OAAO;sBACZkK,EAAE,EAAC,WAAW;sBACdC,KAAK,EAAC,oBAAoB;sBAC1BrB,IAAI,EAAC,YAAY;sBACjBsB,OAAO,EAAE9L,UAAU,KAAK,WAAY;sBACpC+L,QAAQ,EAAEA,CAAA,KAAM9L,aAAa,CAAC,WAAW,CAAE;sBAC3C2E,SAAS,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACFxG,OAAA,CAACvB,IAAI,CAACqO,KAAK;sBACTjK,IAAI,EAAC,OAAO;sBACZkK,EAAE,EAAC,aAAa;sBAChBC,KAAK,EAAC,8BAA8B;sBACpCrB,IAAI,EAAC,YAAY;sBACjBsB,OAAO,EAAE9L,UAAU,KAAK,aAAc;sBACtC+L,QAAQ,EAAEA,CAAA,KAAM9L,aAAa,CAAC,aAAa;oBAAE;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbxG,OAAA;kBAAK+F,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhG,OAAA,CAACtB,MAAM;oBACLqH,SAAS,EAAC,WAAW;oBACrB+D,KAAK,EAAE;sBACLe,YAAY,EAAE,MAAM;sBACpBD,eAAe,EAAE,OAAO;sBACxB0B,KAAK,EAAE,SAAS;sBAChBN,MAAM,EAAE,MAAM;sBACdmB,UAAU,EAAE;oBACd,CAAE;oBACFtB,OAAO,EAAEpC,oBAAqB;oBAAAzD,QAAA,EAC/B;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAETxG,OAAA,CAACX,iBAAiB;oBAChB+N,IAAI,EAAE3G,eAAgB;oBACtB4G,MAAM,EAAEA,CAAA,KAAM3G,kBAAkB,CAAC,KAAK,CAAE;oBACxC4G,SAAS,EAAE9D,YAAa;oBACxB+D,KAAK,EAAC,oBAAoB;oBAC1BxG,OAAO,EAAC,wDAAwD;oBAChEyG,iBAAiB,EAAC,QAAQ;oBAC1B3K,IAAI,EAAC;kBAAQ;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZxG,OAAA;QAAAgG,QAAA,eACEhG,OAAA,CAACL,OAAO;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNxG,OAAA,CAACd,MAAM;MAAAmH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVxG,OAAA,CAACV,cAAc;MACb8N,IAAI,EAAEzG,kBAAmB;MACzB0G,MAAM,EAAEA,CAAA,KAAMzG,qBAAqB,CAAC,KAAK,CAAE;MAC3CxB,UAAU,EAAEA,UAAW;MACvBqI,gBAAgB,EAAE5G,6BAA8B;MAChD6G,kBAAkB,EAAE/L;IAAY;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEFxG,OAAA,CAACF,gBAAgB;MACfsN,IAAI,EAAE/M,sBAAuB;MAC7BsN,OAAO,EAAEA,CAAA,KAAM;QACbrN,yBAAyB,CAAC,KAAK,CAAC;MAClC;IAAE;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACtG,EAAA,CA1vBID,gBAAgB;EAAA,QAGPV,cAAc,EACDA,cAAc,EAGbA,cAAc,EAGPA,cAAc,EAGnBA,cAAc,EAG1BH,WAAW,EACXI,cAAc;AAAA;AAAAoO,EAAA,GAjB3B3N,gBAAgB;AA4vBtB,eAAeA,gBAAgB;AAAC,IAAA2N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}