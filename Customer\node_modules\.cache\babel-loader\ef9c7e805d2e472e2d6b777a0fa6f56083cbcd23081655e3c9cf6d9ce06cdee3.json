{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { socket } from '../socket';\nimport { showToast } from '../components/ToastContainer';\nconst usePromotionValidation = () => {\n  _s();\n  const [validationError, setValidationError] = useState(null);\n  const [isValidating, setIsValidating] = useState(false);\n  const [showValidationModal, setShowValidationModal] = useState(false);\n  const [currentPromotion, setCurrentPromotion] = useState(null);\n  const Auth = useSelector(state => state.Auth);\n\n  // Initialize socket connection for promotion events\n  useEffect(() => {\n    if (!socket.connected) {\n      socket.connect();\n    }\n\n    // Register user for socket events\n    if (Auth._id && Auth._id !== -1) {\n      socket.emit('register', Auth._id);\n    }\n\n    // Listen for promotion validation results\n    socket.on('promotion-validation-result', handleValidationResult);\n\n    // Listen for real-time promotion status changes\n    socket.on('promotion-status-changed', handlePromotionStatusChange);\n\n    // Listen for promotion expiration warnings\n    socket.on('promotion-expiration-warning', handleExpirationWarning);\n\n    // Listen for promotion usage warnings\n    socket.on('promotion-usage-warning', handleUsageWarning);\n\n    // Listen for promotion alternatives\n    socket.on('promotion-alternatives', handlePromotionAlternatives);\n\n    // Listen for validation failures during checkout\n    socket.on('promotion-validation-failed', handleValidationFailure);\n    return () => {\n      socket.off('promotion-validation-result', handleValidationResult);\n      socket.off('promotion-status-changed', handlePromotionStatusChange);\n      socket.off('promotion-expiration-warning', handleExpirationWarning);\n      socket.off('promotion-usage-warning', handleUsageWarning);\n      socket.off('promotion-alternatives', handlePromotionAlternatives);\n      socket.off('promotion-validation-failed', handleValidationFailure);\n    };\n  }, [Auth._id]);\n  const handleValidationResult = useCallback(result => {\n    setIsValidating(false);\n    if (!result.valid) {\n      setValidationError(result);\n      setShowValidationModal(true);\n    }\n  }, []);\n  const handlePromotionStatusChange = useCallback(data => {\n    const {\n      promotionId,\n      status,\n      reason\n    } = data;\n    if (currentPromotion && currentPromotion.id === promotionId) {\n      setValidationError({\n        valid: false,\n        errorCode: status === 'deactivated' ? 'PROMOTION_DEACTIVATED' : 'PROMOTION_EXPIRED',\n        message: reason || 'This promotion is no longer available',\n        severity: 'ERROR',\n        recoverable: true,\n        suggestedAction: 'REMOVE_AND_SUGGEST_ALTERNATIVES'\n      });\n      setShowValidationModal(true);\n      showToast.warning('Your applied promotion is no longer available');\n    }\n  }, [currentPromotion]);\n  const handleExpirationWarning = useCallback(data => {\n    const {\n      promotionId,\n      minutesRemaining,\n      message\n    } = data;\n    if (currentPromotion && currentPromotion.id === promotionId) {\n      showToast.warning(message);\n    }\n  }, [currentPromotion]);\n  const handleUsageWarning = useCallback(data => {\n    const {\n      promotionId,\n      remainingUses,\n      message\n    } = data;\n    if (currentPromotion && currentPromotion.id === promotionId) {\n      showToast.info(message);\n    }\n  }, [currentPromotion]);\n  const handlePromotionAlternatives = useCallback(data => {\n    const {\n      alternatives,\n      message\n    } = data;\n    if (alternatives && alternatives.length > 0) {\n      setValidationError(prev => ({\n        ...prev,\n        alternatives\n      }));\n      showToast.info(message);\n    }\n  }, []);\n  const handleValidationFailure = useCallback(data => {\n    const {\n      reservationId,\n      ...validationResult\n    } = data;\n    setValidationError(validationResult);\n    setShowValidationModal(true);\n    showToast.error('Promotion validation failed during checkout');\n  }, []);\n\n  // Join promotion room for real-time updates\n  const joinPromotionRoom = useCallback(promotionId => {\n    if (Auth._id && Auth._id !== -1 && promotionId) {\n      socket.emit('join-promotion-room', {\n        userId: Auth._id,\n        promotionId\n      });\n    }\n  }, [Auth._id]);\n\n  // Leave promotion room\n  const leavePromotionRoom = useCallback(promotionId => {\n    if (Auth._id && Auth._id !== -1 && promotionId) {\n      socket.emit('leave-promotion-room', {\n        userId: Auth._id,\n        promotionId\n      });\n    }\n  }, [Auth._id]);\n\n  // Validate promotion via socket\n  const validatePromotion = useCallback((promotionId, orderAmount) => {\n    if (!Auth._id || Auth._id === -1) {\n      return;\n    }\n    setIsValidating(true);\n    socket.emit('validate-promotion', {\n      promotionId,\n      orderAmount,\n      userId: Auth._id\n    });\n  }, [Auth._id]);\n\n  // Set current promotion and join its room\n  const setActivePromotion = useCallback(promotion => {\n    // Leave previous promotion room if exists\n    if (currentPromotion && currentPromotion.id) {\n      leavePromotionRoom(currentPromotion.id);\n    }\n    setCurrentPromotion(promotion);\n\n    // Join new promotion room if promotion exists\n    if (promotion && promotion.id) {\n      joinPromotionRoom(promotion.id);\n    }\n  }, [currentPromotion, joinPromotionRoom, leavePromotionRoom]);\n\n  // Clear validation error and close modal\n  const clearValidationError = useCallback(() => {\n    setValidationError(null);\n    setShowValidationModal(false);\n  }, []);\n\n  // Remove current promotion\n  const removePromotion = useCallback(() => {\n    if (currentPromotion && currentPromotion.id) {\n      leavePromotionRoom(currentPromotion.id);\n    }\n    setCurrentPromotion(null);\n    clearValidationError();\n  }, [currentPromotion, leavePromotionRoom, clearValidationError]);\n  return {\n    // State\n    validationError,\n    isValidating,\n    showValidationModal,\n    currentPromotion,\n    // Actions\n    validatePromotion,\n    setActivePromotion,\n    removePromotion,\n    clearValidationError,\n    joinPromotionRoom,\n    leavePromotionRoom,\n    // Modal control\n    setShowValidationModal,\n    setValidationError\n  };\n};\n_s(usePromotionValidation, \"G+qJ9JSpmrlUHvALDfZMZyRDaF8=\", false, function () {\n  return [useSelector];\n});\nexport default usePromotionValidation;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useSelector", "socket", "showToast", "usePromotionValidation", "_s", "validationError", "setValidationError", "isValidating", "setIsValidating", "showValidationModal", "setShowValidationModal", "currentPromotion", "setCurrentPromotion", "<PERSON><PERSON>", "state", "connected", "connect", "_id", "emit", "on", "handleValidationResult", "handlePromotionStatusChange", "handleExpirationWarning", "handleUsageWarning", "handlePromotionAlternatives", "handleValidationFailure", "off", "result", "valid", "data", "promotionId", "status", "reason", "id", "errorCode", "message", "severity", "recoverable", "suggestedAction", "warning", "minutesRemaining", "remainingUses", "info", "alternatives", "length", "prev", "reservationId", "validationResult", "error", "joinPromotionRoom", "userId", "leavePromotionRoom", "validatePromotion", "orderAmount", "setActivePromotion", "promotion", "clearValidationError", "removePromotion"], "sources": ["E:/WDP301_UROOM/Customer/src/hooks/usePromotionValidation.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { socket } from '../socket';\nimport { showToast } from '../components/ToastContainer';\n\nconst usePromotionValidation = () => {\n  const [validationError, setValidationError] = useState(null);\n  const [isValidating, setIsValidating] = useState(false);\n  const [showValidationModal, setShowValidationModal] = useState(false);\n  const [currentPromotion, setCurrentPromotion] = useState(null);\n  \n  const Auth = useSelector((state) => state.Auth);\n\n  // Initialize socket connection for promotion events\n  useEffect(() => {\n    if (!socket.connected) {\n      socket.connect();\n    }\n\n    // Register user for socket events\n    if (Auth._id && Auth._id !== -1) {\n      socket.emit('register', Auth._id);\n    }\n\n    // Listen for promotion validation results\n    socket.on('promotion-validation-result', handleValidationResult);\n    \n    // Listen for real-time promotion status changes\n    socket.on('promotion-status-changed', handlePromotionStatusChange);\n    \n    // Listen for promotion expiration warnings\n    socket.on('promotion-expiration-warning', handleExpirationWarning);\n    \n    // Listen for promotion usage warnings\n    socket.on('promotion-usage-warning', handleUsageWarning);\n    \n    // Listen for promotion alternatives\n    socket.on('promotion-alternatives', handlePromotionAlternatives);\n    \n    // Listen for validation failures during checkout\n    socket.on('promotion-validation-failed', handleValidationFailure);\n\n    return () => {\n      socket.off('promotion-validation-result', handleValidationResult);\n      socket.off('promotion-status-changed', handlePromotionStatusChange);\n      socket.off('promotion-expiration-warning', handleExpirationWarning);\n      socket.off('promotion-usage-warning', handleUsageWarning);\n      socket.off('promotion-alternatives', handlePromotionAlternatives);\n      socket.off('promotion-validation-failed', handleValidationFailure);\n    };\n  }, [Auth._id]);\n\n  const handleValidationResult = useCallback((result) => {\n    setIsValidating(false);\n    \n    if (!result.valid) {\n      setValidationError(result);\n      setShowValidationModal(true);\n    }\n  }, []);\n\n  const handlePromotionStatusChange = useCallback((data) => {\n    const { promotionId, status, reason } = data;\n    \n    if (currentPromotion && currentPromotion.id === promotionId) {\n      setValidationError({\n        valid: false,\n        errorCode: status === 'deactivated' ? 'PROMOTION_DEACTIVATED' : 'PROMOTION_EXPIRED',\n        message: reason || 'This promotion is no longer available',\n        severity: 'ERROR',\n        recoverable: true,\n        suggestedAction: 'REMOVE_AND_SUGGEST_ALTERNATIVES'\n      });\n      setShowValidationModal(true);\n      \n      showToast.warning('Your applied promotion is no longer available');\n    }\n  }, [currentPromotion]);\n\n  const handleExpirationWarning = useCallback((data) => {\n    const { promotionId, minutesRemaining, message } = data;\n    \n    if (currentPromotion && currentPromotion.id === promotionId) {\n      showToast.warning(message);\n    }\n  }, [currentPromotion]);\n\n  const handleUsageWarning = useCallback((data) => {\n    const { promotionId, remainingUses, message } = data;\n    \n    if (currentPromotion && currentPromotion.id === promotionId) {\n      showToast.info(message);\n    }\n  }, [currentPromotion]);\n\n  const handlePromotionAlternatives = useCallback((data) => {\n    const { alternatives, message } = data;\n    \n    if (alternatives && alternatives.length > 0) {\n      setValidationError(prev => ({\n        ...prev,\n        alternatives\n      }));\n      showToast.info(message);\n    }\n  }, []);\n\n  const handleValidationFailure = useCallback((data) => {\n    const { reservationId, ...validationResult } = data;\n    \n    setValidationError(validationResult);\n    setShowValidationModal(true);\n    \n    showToast.error('Promotion validation failed during checkout');\n  }, []);\n\n  // Join promotion room for real-time updates\n  const joinPromotionRoom = useCallback((promotionId) => {\n    if (Auth._id && Auth._id !== -1 && promotionId) {\n      socket.emit('join-promotion-room', {\n        userId: Auth._id,\n        promotionId\n      });\n    }\n  }, [Auth._id]);\n\n  // Leave promotion room\n  const leavePromotionRoom = useCallback((promotionId) => {\n    if (Auth._id && Auth._id !== -1 && promotionId) {\n      socket.emit('leave-promotion-room', {\n        userId: Auth._id,\n        promotionId\n      });\n    }\n  }, [Auth._id]);\n\n  // Validate promotion via socket\n  const validatePromotion = useCallback((promotionId, orderAmount) => {\n    if (!Auth._id || Auth._id === -1) {\n      return;\n    }\n\n    setIsValidating(true);\n    socket.emit('validate-promotion', {\n      promotionId,\n      orderAmount,\n      userId: Auth._id\n    });\n  }, [Auth._id]);\n\n  // Set current promotion and join its room\n  const setActivePromotion = useCallback((promotion) => {\n    // Leave previous promotion room if exists\n    if (currentPromotion && currentPromotion.id) {\n      leavePromotionRoom(currentPromotion.id);\n    }\n\n    setCurrentPromotion(promotion);\n    \n    // Join new promotion room if promotion exists\n    if (promotion && promotion.id) {\n      joinPromotionRoom(promotion.id);\n    }\n  }, [currentPromotion, joinPromotionRoom, leavePromotionRoom]);\n\n  // Clear validation error and close modal\n  const clearValidationError = useCallback(() => {\n    setValidationError(null);\n    setShowValidationModal(false);\n  }, []);\n\n  // Remove current promotion\n  const removePromotion = useCallback(() => {\n    if (currentPromotion && currentPromotion.id) {\n      leavePromotionRoom(currentPromotion.id);\n    }\n    setCurrentPromotion(null);\n    clearValidationError();\n  }, [currentPromotion, leavePromotionRoom, clearValidationError]);\n\n  return {\n    // State\n    validationError,\n    isValidating,\n    showValidationModal,\n    currentPromotion,\n\n    // Actions\n    validatePromotion,\n    setActivePromotion,\n    removePromotion,\n    clearValidationError,\n    joinPromotionRoom,\n    leavePromotionRoom,\n\n    // Modal control\n    setShowValidationModal,\n    setValidationError\n  };\n};\n\nexport default usePromotionValidation;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,WAAW;AAClC,SAASC,SAAS,QAAQ,8BAA8B;AAExD,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACY,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACc,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAMgB,IAAI,GAAGb,WAAW,CAAEc,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAE/C;EACAf,SAAS,CAAC,MAAM;IACd,IAAI,CAACG,MAAM,CAACc,SAAS,EAAE;MACrBd,MAAM,CAACe,OAAO,CAAC,CAAC;IAClB;;IAEA;IACA,IAAIH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAACI,GAAG,KAAK,CAAC,CAAC,EAAE;MAC/BhB,MAAM,CAACiB,IAAI,CAAC,UAAU,EAAEL,IAAI,CAACI,GAAG,CAAC;IACnC;;IAEA;IACAhB,MAAM,CAACkB,EAAE,CAAC,6BAA6B,EAAEC,sBAAsB,CAAC;;IAEhE;IACAnB,MAAM,CAACkB,EAAE,CAAC,0BAA0B,EAAEE,2BAA2B,CAAC;;IAElE;IACApB,MAAM,CAACkB,EAAE,CAAC,8BAA8B,EAAEG,uBAAuB,CAAC;;IAElE;IACArB,MAAM,CAACkB,EAAE,CAAC,yBAAyB,EAAEI,kBAAkB,CAAC;;IAExD;IACAtB,MAAM,CAACkB,EAAE,CAAC,wBAAwB,EAAEK,2BAA2B,CAAC;;IAEhE;IACAvB,MAAM,CAACkB,EAAE,CAAC,6BAA6B,EAAEM,uBAAuB,CAAC;IAEjE,OAAO,MAAM;MACXxB,MAAM,CAACyB,GAAG,CAAC,6BAA6B,EAAEN,sBAAsB,CAAC;MACjEnB,MAAM,CAACyB,GAAG,CAAC,0BAA0B,EAAEL,2BAA2B,CAAC;MACnEpB,MAAM,CAACyB,GAAG,CAAC,8BAA8B,EAAEJ,uBAAuB,CAAC;MACnErB,MAAM,CAACyB,GAAG,CAAC,yBAAyB,EAAEH,kBAAkB,CAAC;MACzDtB,MAAM,CAACyB,GAAG,CAAC,wBAAwB,EAAEF,2BAA2B,CAAC;MACjEvB,MAAM,CAACyB,GAAG,CAAC,6BAA6B,EAAED,uBAAuB,CAAC;IACpE,CAAC;EACH,CAAC,EAAE,CAACZ,IAAI,CAACI,GAAG,CAAC,CAAC;EAEd,MAAMG,sBAAsB,GAAGrB,WAAW,CAAE4B,MAAM,IAAK;IACrDnB,eAAe,CAAC,KAAK,CAAC;IAEtB,IAAI,CAACmB,MAAM,CAACC,KAAK,EAAE;MACjBtB,kBAAkB,CAACqB,MAAM,CAAC;MAC1BjB,sBAAsB,CAAC,IAAI,CAAC;IAC9B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,2BAA2B,GAAGtB,WAAW,CAAE8B,IAAI,IAAK;IACxD,MAAM;MAAEC,WAAW;MAAEC,MAAM;MAAEC;IAAO,CAAC,GAAGH,IAAI;IAE5C,IAAIlB,gBAAgB,IAAIA,gBAAgB,CAACsB,EAAE,KAAKH,WAAW,EAAE;MAC3DxB,kBAAkB,CAAC;QACjBsB,KAAK,EAAE,KAAK;QACZM,SAAS,EAAEH,MAAM,KAAK,aAAa,GAAG,uBAAuB,GAAG,mBAAmB;QACnFI,OAAO,EAAEH,MAAM,IAAI,uCAAuC;QAC1DI,QAAQ,EAAE,OAAO;QACjBC,WAAW,EAAE,IAAI;QACjBC,eAAe,EAAE;MACnB,CAAC,CAAC;MACF5B,sBAAsB,CAAC,IAAI,CAAC;MAE5BR,SAAS,CAACqC,OAAO,CAAC,+CAA+C,CAAC;IACpE;EACF,CAAC,EAAE,CAAC5B,gBAAgB,CAAC,CAAC;EAEtB,MAAMW,uBAAuB,GAAGvB,WAAW,CAAE8B,IAAI,IAAK;IACpD,MAAM;MAAEC,WAAW;MAAEU,gBAAgB;MAAEL;IAAQ,CAAC,GAAGN,IAAI;IAEvD,IAAIlB,gBAAgB,IAAIA,gBAAgB,CAACsB,EAAE,KAAKH,WAAW,EAAE;MAC3D5B,SAAS,CAACqC,OAAO,CAACJ,OAAO,CAAC;IAC5B;EACF,CAAC,EAAE,CAACxB,gBAAgB,CAAC,CAAC;EAEtB,MAAMY,kBAAkB,GAAGxB,WAAW,CAAE8B,IAAI,IAAK;IAC/C,MAAM;MAAEC,WAAW;MAAEW,aAAa;MAAEN;IAAQ,CAAC,GAAGN,IAAI;IAEpD,IAAIlB,gBAAgB,IAAIA,gBAAgB,CAACsB,EAAE,KAAKH,WAAW,EAAE;MAC3D5B,SAAS,CAACwC,IAAI,CAACP,OAAO,CAAC;IACzB;EACF,CAAC,EAAE,CAACxB,gBAAgB,CAAC,CAAC;EAEtB,MAAMa,2BAA2B,GAAGzB,WAAW,CAAE8B,IAAI,IAAK;IACxD,MAAM;MAAEc,YAAY;MAAER;IAAQ,CAAC,GAAGN,IAAI;IAEtC,IAAIc,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3CtC,kBAAkB,CAACuC,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACPF;MACF,CAAC,CAAC,CAAC;MACHzC,SAAS,CAACwC,IAAI,CAACP,OAAO,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMV,uBAAuB,GAAG1B,WAAW,CAAE8B,IAAI,IAAK;IACpD,MAAM;MAAEiB,aAAa;MAAE,GAAGC;IAAiB,CAAC,GAAGlB,IAAI;IAEnDvB,kBAAkB,CAACyC,gBAAgB,CAAC;IACpCrC,sBAAsB,CAAC,IAAI,CAAC;IAE5BR,SAAS,CAAC8C,KAAK,CAAC,6CAA6C,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,iBAAiB,GAAGlD,WAAW,CAAE+B,WAAW,IAAK;IACrD,IAAIjB,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAACI,GAAG,KAAK,CAAC,CAAC,IAAIa,WAAW,EAAE;MAC9C7B,MAAM,CAACiB,IAAI,CAAC,qBAAqB,EAAE;QACjCgC,MAAM,EAAErC,IAAI,CAACI,GAAG;QAChBa;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACjB,IAAI,CAACI,GAAG,CAAC,CAAC;;EAEd;EACA,MAAMkC,kBAAkB,GAAGpD,WAAW,CAAE+B,WAAW,IAAK;IACtD,IAAIjB,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAACI,GAAG,KAAK,CAAC,CAAC,IAAIa,WAAW,EAAE;MAC9C7B,MAAM,CAACiB,IAAI,CAAC,sBAAsB,EAAE;QAClCgC,MAAM,EAAErC,IAAI,CAACI,GAAG;QAChBa;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACjB,IAAI,CAACI,GAAG,CAAC,CAAC;;EAEd;EACA,MAAMmC,iBAAiB,GAAGrD,WAAW,CAAC,CAAC+B,WAAW,EAAEuB,WAAW,KAAK;IAClE,IAAI,CAACxC,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAACI,GAAG,KAAK,CAAC,CAAC,EAAE;MAChC;IACF;IAEAT,eAAe,CAAC,IAAI,CAAC;IACrBP,MAAM,CAACiB,IAAI,CAAC,oBAAoB,EAAE;MAChCY,WAAW;MACXuB,WAAW;MACXH,MAAM,EAAErC,IAAI,CAACI;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACJ,IAAI,CAACI,GAAG,CAAC,CAAC;;EAEd;EACA,MAAMqC,kBAAkB,GAAGvD,WAAW,CAAEwD,SAAS,IAAK;IACpD;IACA,IAAI5C,gBAAgB,IAAIA,gBAAgB,CAACsB,EAAE,EAAE;MAC3CkB,kBAAkB,CAACxC,gBAAgB,CAACsB,EAAE,CAAC;IACzC;IAEArB,mBAAmB,CAAC2C,SAAS,CAAC;;IAE9B;IACA,IAAIA,SAAS,IAAIA,SAAS,CAACtB,EAAE,EAAE;MAC7BgB,iBAAiB,CAACM,SAAS,CAACtB,EAAE,CAAC;IACjC;EACF,CAAC,EAAE,CAACtB,gBAAgB,EAAEsC,iBAAiB,EAAEE,kBAAkB,CAAC,CAAC;;EAE7D;EACA,MAAMK,oBAAoB,GAAGzD,WAAW,CAAC,MAAM;IAC7CO,kBAAkB,CAAC,IAAI,CAAC;IACxBI,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+C,eAAe,GAAG1D,WAAW,CAAC,MAAM;IACxC,IAAIY,gBAAgB,IAAIA,gBAAgB,CAACsB,EAAE,EAAE;MAC3CkB,kBAAkB,CAACxC,gBAAgB,CAACsB,EAAE,CAAC;IACzC;IACArB,mBAAmB,CAAC,IAAI,CAAC;IACzB4C,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAC7C,gBAAgB,EAAEwC,kBAAkB,EAAEK,oBAAoB,CAAC,CAAC;EAEhE,OAAO;IACL;IACAnD,eAAe;IACfE,YAAY;IACZE,mBAAmB;IACnBE,gBAAgB;IAEhB;IACAyC,iBAAiB;IACjBE,kBAAkB;IAClBG,eAAe;IACfD,oBAAoB;IACpBP,iBAAiB;IACjBE,kBAAkB;IAElB;IACAzC,sBAAsB;IACtBJ;EACF,CAAC;AACH,CAAC;AAACF,EAAA,CAlMID,sBAAsB;EAAA,QAMbH,WAAW;AAAA;AA8L1B,eAAeG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}